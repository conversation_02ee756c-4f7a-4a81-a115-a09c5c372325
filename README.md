# 🏛️ Interactive India Map - Hindu Temple Locator

A modern, interactive web application for exploring Hindu temples across India. Built with React, TypeScript, and Tailwind CSS, this application provides an intuitive map-based interface to discover India's rich spiritual heritage.

## ✨ Features

### 🗺️ Interactive Map Navigation
- **Country View**: Overview of all Indian states with clickable regions
- **State View**: Detailed view of selected state with cities
- **City View**: Focused view of selected city with temple locations
- **Breadcrumb Navigation**: Easy navigation between different map levels

### 🏛️ Temple Information
- **Comprehensive Details**: Temple history, significance, architecture, and visiting information
- **Contact Information**: Phone numbers, websites, and addresses
- **Festival Calendar**: Major festivals and celebrations
- **Practical Info**: Visiting hours, entry fees, dress codes, and facilities

### 🎨 Modern Design
- **Responsive Layout**: Mobile-first design that works on all devices
- **Beautiful UI**: Clean, modern interface with gradient backgrounds
- **Smooth Animations**: Hover effects and transitions for better UX
- **Accessibility**: Proper contrast ratios and keyboard navigation

### 🔧 Technical Features
- **TypeScript**: Full type safety and better development experience
- **State Management**: Zustand for efficient state management
- **Component Architecture**: Modular, reusable components
- **SVG Maps**: Scalable vector graphics for crisp map rendering

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd all-india-temple-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

## 🏗️ Project Structure

```
src/
├── components/          # React components
│   ├── IndiaMap.tsx    # Main map container
│   ├── MapViewer.tsx   # SVG map renderer
│   ├── Breadcrumb.tsx  # Navigation breadcrumb
│   ├── TempleCard.tsx  # Temple information card
│   └── ContentArea.tsx # Content display area
├── data/               # Static data files
│   ├── temples.ts      # Temple information
│   ├── states.ts       # State and city data
│   └── mapData.ts      # SVG map coordinates
├── store/              # State management
│   └── mapStore.ts     # Zustand store
├── types/              # TypeScript definitions
│   └── index.ts        # Type definitions
└── utils/              # Utility functions
```

## 🎯 Usage

### Navigation
1. **Start at Country Level**: View all Indian states on the main map
2. **Select a State**: Click on any state to view its cities and temples
3. **Explore Cities**: Click on cities within a state for detailed temple information
4. **Use Breadcrumbs**: Navigate back to previous levels using the breadcrumb navigation

### Temple Information
- Each temple card displays comprehensive information including:
  - Temple name and location
  - Historical significance and architecture
  - Visiting hours and contact details
  - Major festivals and nearby attractions
  - Ratings and reviews

## 🛠️ Technologies Used

- **Frontend Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **State Management**: Zustand
- **Icons**: Heroicons
- **Build Tool**: Vite
- **Maps**: Custom SVG-based map implementation

## 🎨 Design System

### Colors
- **Primary**: Orange gradient (#f97316 to #ea580c)
- **Secondary**: Blue gradient (#0ea5e9 to #0284c7)
- **Accent**: Purple gradient (#d946ef to #c026d3)
- **Temple Theme**: Saffron, White, Green, Gold, Maroon

### Typography
- **Primary Font**: Inter (body text)
- **Heading Font**: Poppins (headings)
- **Display Font**: Playfair Display (decorative)

## 📱 Responsive Design

The application is fully responsive and optimized for:
- **Desktop**: Full-featured experience with side-by-side layout
- **Tablet**: Adapted layout with stacked components
- **Mobile**: Touch-optimized interface with collapsible sections

## 🔮 Future Enhancements

- **Real Map Data**: Integration with actual geographic SVG data
- **Backend Integration**: API for dynamic temple data
- **User Features**: Favorites, reviews, and trip planning
- **Advanced Search**: Filter temples by deity, architecture, or festivals
- **Offline Support**: PWA capabilities for offline access

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Temple data sourced from various cultural and historical references
- Map inspiration from traditional Indian cartography
- Design influenced by modern web standards and accessibility guidelines
