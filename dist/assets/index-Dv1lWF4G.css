@import"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap";/*! tailwindcss v4.1.8 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-border-style:solid;--tw-outline-style:solid;--tw-duration:initial;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1}}}.pointer-events-none{pointer-events:none}.absolute{position:absolute}.relative{position:relative}.static{position:static}.sticky{position:sticky}.z-10{z-index:10}.container{width:100%}.mx-auto{margin-inline:auto}.line-clamp-3{-webkit-line-clamp:3;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.flex{display:flex}.grid{display:grid}.hidden{display:none}.aspect-\[4\/3\]{aspect-ratio:4/3}.aspect-\[16\/10\]{aspect-ratio:16/10}.h-full{height:100%}.min-h-screen{min-height:100vh}.w-full{width:100%}.flex-1{flex:1}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.items-start{align-items:flex-start}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.overflow-hidden{overflow:hidden}.rounded-full{border-radius:3.40282e38px}.border{border-style:var(--tw-border-style);border-width:1px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.bg-gradient-to-br{--tw-gradient-position:to bottom right in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.text-center{text-align:center}.opacity-20{opacity:.2}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.duration-200{--tw-duration:.2s;transition-duration:.2s}.select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}@media (hover:hover){.hover\:scale-105:hover{--tw-scale-x:105%;--tw-scale-y:105%;--tw-scale-z:105%;scale:var(--tw-scale-x)var(--tw-scale-y)}}@layer base{body{color:#0f172a;-webkit-font-smoothing:antialiased;background:linear-gradient(to bottom right,#f8fafc,#eff6ff);min-height:100vh;margin:0;font-family:Inter,system-ui,sans-serif}h1,h2,h3,h4,h5,h6{font-family:Poppins,system-ui,sans-serif}}@layer components{.btn-primary{color:#fff;cursor:pointer;background:linear-gradient(90deg,#f97316,#ea580c);border:none;border-radius:.5rem;padding:.75rem 1.5rem;font-weight:500;transition:all .2s;box-shadow:0 4px 25px -5px #0000001a}.btn-primary:hover{background:linear-gradient(90deg,#ea580c,#c2410c);transform:scale(1.05);box-shadow:0 10px 40px -10px #00000026}.btn-secondary{color:#111827;cursor:pointer;background:#fff;border:1px solid #e5e7eb;border-radius:.5rem;padding:.75rem 1.5rem;font-weight:500;transition:all .2s;box-shadow:0 2px 15px -3px #00000012}.btn-secondary:hover{background:#f9fafb;transform:scale(1.05);box-shadow:0 4px 25px -5px #0000001a}.card{-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);background:#fffc;border:1px solid #fff3;border-radius:.75rem;padding:1.5rem;transition:all .2s;box-shadow:0 2px 15px -3px #00000012}.card:hover{box-shadow:0 4px 25px -5px #0000001a}.map-state{fill:#fed7aa;stroke:#fdba74;stroke-width:1px;cursor:pointer;transition:all .2s}.map-state:hover{fill:#fbbf24;stroke:#f59e0b;stroke-width:2px}.map-state-active{fill:#fdba74;stroke:#f97316;stroke-width:2px}.map-city{fill:#bae6fd;stroke:#7dd3fc;stroke-width:1px;cursor:pointer;transition:all .2s}.map-city:hover{fill:#38bdf8;stroke:#0ea5e9;stroke-width:2px}.map-city-active{fill:#7dd3fc;stroke:#0ea5e9;stroke-width:2px}}@layer utilities{.text-gradient{color:#0000;background:linear-gradient(90deg,#ea580c,#0284c7);-webkit-background-clip:text;background-clip:text}.bg-gradient-temple{background:linear-gradient(to bottom right,#f93,gold,maroon)}.scrollbar-hide{-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide::-webkit-scrollbar{display:none}}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-duration{syntax:"*";inherits:false}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}
