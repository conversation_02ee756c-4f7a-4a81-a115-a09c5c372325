#!/usr/bin/env node

/**
 * SVG Map Data Extraction Script
 * Extracts geographic data from SVG files and generates optimized JSON data
 * 
 * Usage: node extract-svg-data.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// State ID to name mapping
const STATE_NAMES = {
  'in-an': 'Andaman and Nicobar Islands',
  'in-ap': 'Andhra Pradesh',
  'in-ar': 'Arunachal Pradesh',
  'in-as': 'Assam',
  'in-br': 'Bihar',
  'in-ch': 'Chandigarh',
  'in-ct': 'Chhattisgarh',
  'in-dn': 'Dadra and Nagar Haveli',
  'in-dd': 'Daman and Diu',
  'in-dl': 'Delhi',
  'in-ga': 'Goa',
  'in-gj': 'Gujarat',
  'in-hr': 'Haryana',
  'in-hp': 'Himachal Pradesh',
  'in-jk': 'Jammu and Kashmir',
  'in-jh': 'Jharkhand',
  'in-ka': 'Karnataka',
  'in-kl': 'Kerala',
  'in-ld': 'Lakshadweep',
  'in-mp': 'Madhya Pradesh',
  'in-mh': 'Maharashtra',
  'in-mn': 'Manipur',
  'in-ml': 'Meghalaya',
  'in-mz': 'Mizoram',
  'in-nl': 'Nagaland',
  'in-or': 'Odisha',
  'in-py': 'Puducherry',
  'in-pb': 'Punjab',
  'in-rj': 'Rajasthan',
  'in-sk': 'Sikkim',
  'in-tn': 'Tamil Nadu',
  'in-tg': 'Telangana',
  'in-tr': 'Tripura',
  'in-up': 'Uttar Pradesh',
  'in-ut': 'Uttarakhand',
  'in-wb': 'West Bengal'
};

// State code mapping
const STATE_CODES = {
  'in-an': 'AN',
  'in-ap': 'AP',
  'in-ar': 'AR',
  'in-as': 'AS',
  'in-br': 'BR',
  'in-ch': 'CH',
  'in-ct': 'CT',
  'in-dn': 'DN',
  'in-dd': 'DD',
  'in-dl': 'DL',
  'in-ga': 'GA',
  'in-gj': 'GJ',
  'in-hr': 'HR',
  'in-hp': 'HP',
  'in-jk': 'JK',
  'in-jh': 'JH',
  'in-ka': 'KA',
  'in-kl': 'KL',
  'in-ld': 'LD',
  'in-mp': 'MP',
  'in-mh': 'MH',
  'in-mn': 'MN',
  'in-ml': 'ML',
  'in-mz': 'MZ',
  'in-nl': 'NL',
  'in-or': 'OR',
  'in-py': 'PY',
  'in-pb': 'PB',
  'in-rj': 'RJ',
  'in-sk': 'SK',
  'in-tn': 'TN',
  'in-tg': 'TG',
  'in-tr': 'TR',
  'in-up': 'UP',
  'in-ut': 'UT',
  'in-wb': 'WB'
};

class SVGDataExtractor {
  constructor() {
    this.svgPath = path.join(__dirname, 'src', 'assets', 'maps', 'in.svg');
    this.outputDir = path.join(__dirname, 'src', 'data', 'generated');
  }

  /**
   * Extract coordinates from SVG path data
   */
  extractCoordinatesFromPath(pathData) {
    const coordinates = [];
    const coordRegex = /(-?\d*\.?\d+)[,\s]+(-?\d*\.?\d+)/g;
    let match;
    
    while ((match = coordRegex.exec(pathData)) !== null) {
      const x = parseFloat(match[1]);
      const y = parseFloat(match[2]);
      
      if (!isNaN(x) && !isNaN(y)) {
        coordinates.push([x, y]);
      }
    }
    
    return coordinates;
  }

  /**
   * Calculate bounding box from coordinates
   */
  calculateBounds(coordinates) {
    if (coordinates.length === 0) return null;

    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;

    for (const [x, y] of coordinates) {
      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x);
      maxY = Math.max(maxY, y);
    }

    return { minX, minY, maxX, maxY };
  }

  /**
   * Convert SVG coordinates to approximate lat/lng
   */
  svgToLatLng(svgX, svgY, totalBounds) {
    const INDIA_BOUNDS = {
      north: 37.6,
      south: 6.4,
      east: 97.25,
      west: 68.7
    };

    const normalizedX = (svgX - totalBounds.minX) / (totalBounds.maxX - totalBounds.minX);
    const normalizedY = (svgY - totalBounds.minY) / (totalBounds.maxY - totalBounds.minY);

    const lng = INDIA_BOUNDS.west + normalizedX * (INDIA_BOUNDS.east - INDIA_BOUNDS.west);
    const lat = INDIA_BOUNDS.north - normalizedY * (INDIA_BOUNDS.north - INDIA_BOUNDS.south);

    return { lat, lng };
  }

  /**
   * Extract data from SVG file
   */
  extractSVGData() {
    console.log('🗺️  Extracting SVG map data...');
    
    if (!fs.existsSync(this.svgPath)) {
      throw new Error(`SVG file not found at: ${this.svgPath}`);
    }

    const svgContent = fs.readFileSync(this.svgPath, 'utf8');
    
    // Parse SVG content to extract paths
    const pathRegex = /<path[^>]+id="([^"]+)"[^>]+d="([^"]+)"/g;
    const viewBoxMatch = svgContent.match(/viewBox="([^"]+)"/);
    
    const states = [];
    let totalBounds = { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity };
    let match;

    console.log('📍 Processing state paths...');
    
    while ((match = pathRegex.exec(svgContent)) !== null) {
      const [, id, pathData] = match;
      
      if (id.startsWith('in-')) {
        const coordinates = this.extractCoordinatesFromPath(pathData);
        const bounds = this.calculateBounds(coordinates);
        
        if (bounds) {
          // Update total bounds
          totalBounds.minX = Math.min(totalBounds.minX, bounds.minX);
          totalBounds.minY = Math.min(totalBounds.minY, bounds.minY);
          totalBounds.maxX = Math.max(totalBounds.maxX, bounds.maxX);
          totalBounds.maxY = Math.max(totalBounds.maxY, bounds.maxY);

          const center = {
            x: (bounds.minX + bounds.maxX) / 2,
            y: (bounds.minY + bounds.maxY) / 2
          };

          states.push({
            id,
            code: STATE_CODES[id] || id.replace('in-', '').toUpperCase(),
            name: STATE_NAMES[id] || id.replace('in-', '').toUpperCase(),
            path: pathData,
            bounds,
            center,
            coordinateCount: coordinates.length
          });
        }
      }
    }

    // Convert centers to lat/lng
    states.forEach(state => {
      const latLng = this.svgToLatLng(state.center.x, state.center.y, totalBounds);
      state.coordinates = latLng;
    });

    // Extract viewBox
    let viewBox = { x: 0, y: 0, width: 800, height: 600 };
    if (viewBoxMatch) {
      const [x, y, width, height] = viewBoxMatch[1].split(' ').map(Number);
      viewBox = { x, y, width, height };
    }

    console.log(`✅ Extracted data for ${states.length} states`);
    
    return {
      metadata: {
        extractedAt: new Date().toISOString(),
        source: 'in.svg',
        totalStates: states.length,
        dataQuality: 'high',
        coordinateSystem: 'SVG with lat/lng approximation'
      },
      viewBox,
      totalBounds,
      states: states.sort((a, b) => a.name.localeCompare(b.name))
    };
  }

  /**
   * Generate optimized data files
   */
  generateOutputFiles(data) {
    console.log('📝 Generating output files...');
    
    // Ensure output directory exists
    fs.mkdirSync(this.outputDir, { recursive: true });

    // 1. Complete extracted data
    const completeDataPath = path.join(this.outputDir, 'extractedMapData.json');
    fs.writeFileSync(completeDataPath, JSON.stringify(data, null, 2));
    console.log(`📄 Complete data: ${completeDataPath}`);

    // 2. Geographic coordinates only
    const coordinatesData = {
      metadata: data.metadata,
      states: data.states.map(state => ({
        code: state.code,
        name: state.name,
        coordinates: state.coordinates
      }))
    };
    const coordsPath = path.join(this.outputDir, 'stateCoordinates.json');
    fs.writeFileSync(coordsPath, JSON.stringify(coordinatesData, null, 2));
    console.log(`📍 Coordinates: ${coordsPath}`);

    // 3. SVG paths for rendering
    const svgData = {
      metadata: data.metadata,
      viewBox: data.viewBox,
      totalBounds: data.totalBounds,
      paths: data.states.map(state => ({
        id: state.id,
        code: state.code,
        name: state.name,
        path: state.path,
        bounds: state.bounds
      }))
    };
    const svgPath = path.join(this.outputDir, 'svgMapPaths.json');
    fs.writeFileSync(svgPath, JSON.stringify(svgData, null, 2));
    console.log(`🗺️  SVG paths: ${svgPath}`);

    // 4. TypeScript interface
    const tsInterface = this.generateTypeScriptInterface(data);
    const tsPath = path.join(this.outputDir, 'extractedMapData.ts');
    fs.writeFileSync(tsPath, tsInterface);
    console.log(`📝 TypeScript: ${tsPath}`);

    // 5. Summary report
    const summary = this.generateSummaryReport(data);
    const summaryPath = path.join(this.outputDir, 'extraction-summary.md');
    fs.writeFileSync(summaryPath, summary);
    console.log(`📊 Summary: ${summaryPath}`);
  }

  /**
   * Generate TypeScript interface file
   */
  generateTypeScriptInterface(data) {
    return `// Generated on ${new Date().toISOString()}
// SVG Map Data Extraction Results

export interface ExtractedStateData {
  id: string;
  code: string;
  name: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  path: string;
  bounds: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
  center: {
    x: number;
    y: number;
  };
  coordinateCount: number;
}

export interface ExtractedMapData {
  metadata: {
    extractedAt: string;
    source: string;
    totalStates: number;
    dataQuality: string;
    coordinateSystem: string;
  };
  viewBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  totalBounds: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
  states: ExtractedStateData[];
}

// Extracted map data
export const extractedMapData: ExtractedMapData = ${JSON.stringify(data, null, 2)};

// Quick access to state coordinates
export const stateCoordinates = extractedMapData.states.reduce((acc, state) => {
  acc[state.code] = state.coordinates;
  return acc;
}, {} as Record<string, { lat: number; lng: number }>);

// Quick access to SVG paths
export const statePaths = extractedMapData.states.reduce((acc, state) => {
  acc[state.code] = state.path;
  return acc;
}, {} as Record<string, string>);
`;
  }

  /**
   * Generate summary report
   */
  generateSummaryReport(data) {
    const avgCoordCount = Math.round(
      data.states.reduce((sum, state) => sum + state.coordinateCount, 0) / data.states.length
    );

    const statesList = data.states
      .map(state => `- **${state.name}** (${state.code}): ${state.coordinateCount} coordinates`)
      .join('\n');

    return `# SVG Map Data Extraction Summary

**Generated:** ${data.metadata.extractedAt}
**Source:** ${data.metadata.source}
**Total States:** ${data.metadata.totalStates}
**Data Quality:** ${data.metadata.dataQuality}

## Statistics

- **Average coordinates per state:** ${avgCoordCount}
- **Total coordinates extracted:** ${data.states.reduce((sum, state) => sum + state.coordinateCount, 0)}
- **ViewBox dimensions:** ${data.viewBox.width} × ${data.viewBox.height}
- **Geographic bounds:** 
  - X: ${data.totalBounds.minX.toFixed(2)} to ${data.totalBounds.maxX.toFixed(2)}
  - Y: ${data.totalBounds.minY.toFixed(2)} to ${data.totalBounds.maxY.toFixed(2)}

## Extracted States

${statesList}

## Files Generated

1. \`extractedMapData.json\` - Complete extracted data
2. \`stateCoordinates.json\` - Coordinates only
3. \`svgMapPaths.json\` - SVG paths for rendering
4. \`extractedMapData.ts\` - TypeScript interfaces and data
5. \`extraction-summary.md\` - This summary report

## Usage

Import the generated data in your React components:

\`\`\`typescript
import { extractedMapData, stateCoordinates, statePaths } from './data/generated/extractedMapData';
\`\`\`

## Data Quality Notes

- Coordinates are approximated from SVG space to lat/lng
- SVG paths maintain original precision for accurate rendering
- Geographic bounds are calibrated to India's actual coordinates
- All ${data.metadata.totalStates} states and union territories included
`;
  }

  /**
   * Run the extraction process
   */
  run() {
    try {
      console.log('🚀 Starting SVG map data extraction...\n');
      
      const extractedData = this.extractSVGData();
      this.generateOutputFiles(extractedData);
      
      console.log('\n✅ Extraction completed successfully!');
      console.log(`📁 Output directory: ${this.outputDir}`);
      console.log('\n📋 Next steps:');
      console.log('1. Review the generated files');
      console.log('2. Import the data in your components');
      console.log('3. Test the enhanced map viewer');
      
    } catch (error) {
      console.error('❌ Extraction failed:', error.message);
      process.exit(1);
    }
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const extractor = new SVGDataExtractor();
  extractor.run();
}

export default SVGDataExtractor;
