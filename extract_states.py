#!/usr/bin/env python3
"""
Extract state data from Indian map SVG file.
This script parses the SVG file to extract state names and their corresponding IDs,
then outputs the data in JSON format for use in the temple website project.
"""

import xml.etree.ElementTree as ET
import json
import re
from pathlib import Path

def extract_state_data(svg_file_path):
    """
    Extract state ID and name pairs from the SVG file.
    
    Args:
        svg_file_path (str): Path to the SVG file
        
    Returns:
        dict: Dictionary with state IDs as keys and state names as values
    """
    states_data = {}
    
    try:
        # Parse the SVG file
        tree = ET.parse(svg_file_path)
        root = tree.getroot()
        
        # Define the SVG namespace
        namespaces = {'svg': 'http://www.w3.org/2000/svg'}
        
        # Find all path elements
        paths = root.findall('.//svg:path', namespaces)
        
        # Also try without namespace in case it's not properly defined
        if not paths:
            paths = root.findall('.//path')
        
        print(f"Found {len(paths)} path elements")
        
        # Extract state data from path elements
        for path in paths:
            state_id = path.get('id')
            state_name = path.get('name')
            
            # Check if both id and name exist and id follows the state pattern (starts with 'IN')
            if state_id and state_name and state_id.startswith('IN') and len(state_id) == 4:
                states_data[state_id] = state_name
                print(f"Extracted: {state_id} -> {state_name}")
        
        return states_data
        
    except ET.ParseError as e:
        print(f"Error parsing SVG file: {e}")
        return {}
    except FileNotFoundError:
        print(f"SVG file not found: {svg_file_path}")
        return {}
    except Exception as e:
        print(f"Unexpected error: {e}")
        return {}

def save_states_data(states_data, output_file_path):
    """
    Save the extracted states data to a JSON file.
    
    Args:
        states_data (dict): Dictionary with state data
        output_file_path (str): Path to save the JSON file
    """
    try:
        with open(output_file_path, 'w', encoding='utf-8') as f:
            json.dump(states_data, f, indent=2, ensure_ascii=False, sort_keys=True)
        print(f"States data saved to: {output_file_path}")
    except Exception as e:
        print(f"Error saving JSON file: {e}")

def generate_typescript_file(states_data, output_file_path):
    """
    Generate a TypeScript file with the states data.
    
    Args:
        states_data (dict): Dictionary with state data
        output_file_path (str): Path to save the TypeScript file
    """
    try:
        states_by_name = {name: state_id for state_id, name in states_data.items()}
        
        ts_content = '''// Auto-generated from extract_states.py
// Indian states and union territories mapping from SVG map data

export interface StateData {
  id: string;
  name: string;
}

export const INDIAN_STATES: Record<string, string> = {
'''
        
        # Add states mapping
        for state_id, state_name in sorted(states_data.items()):
            ts_content += f'  "{state_id}": "{state_name}",\n'
        
        ts_content += '''};

export const STATES_BY_NAME: Record<string, string> = {
'''
        
        # Add reverse mapping
        for state_name, state_id in sorted(states_by_name.items()):
            ts_content += f'  "{state_name}": "{state_id}",\n'
        
        ts_content += '''};

export const STATE_IDS = Object.keys(INDIAN_STATES).sort();
export const STATE_NAMES = Object.values(INDIAN_STATES).sort();
export const STATE_COUNT = Object.keys(INDIAN_STATES).length;

// Helper functions
export function getStateName(stateId: string): string | undefined {
  return INDIAN_STATES[stateId];
}

export function getStateId(stateName: string): string | undefined {
  return STATES_BY_NAME[stateName];
}

export function getAllStates(): StateData[] {
  return Object.entries(INDIAN_STATES).map(([id, name]) => ({ id, name }));
}

export function isValidStateId(stateId: string): boolean {
  return stateId in INDIAN_STATES;
}

export function isValidStateName(stateName: string): boolean {
  return stateName in STATES_BY_NAME;
}'''
        
        with open(output_file_path, 'w', encoding='utf-8') as f:
            f.write(ts_content)
        print(f"TypeScript data saved to: {output_file_path}")
    except Exception as e:
        print(f"Error saving TypeScript file: {e}")

def create_state_mappings(states_data):
    """
    Create additional mappings for the states data.
    
    Args:
        states_data (dict): Dictionary with state data
        
    Returns:
        dict: Enhanced data structure with additional mappings
    """
    mappings = {
        'states_by_id': states_data,
        'states_by_name': {name: state_id for state_id, name in states_data.items()},
        'state_count': len(states_data),
        'state_ids': sorted(states_data.keys()),
        'state_names': sorted(states_data.values())
    }
    
    return mappings

def main():
    """Main function to extract and save state data."""
    
    print("Starting state extraction script...")
    
    # Path to the SVG file
    svg_file_path = "/Users/<USER>/development/all-india-temple-website/src/assets/maps/in.svg"
    
    print(f"Checking SVG file: {svg_file_path}")
    import os
    if os.path.exists(svg_file_path):
        print("SVG file exists!")
    else:
        print("SVG file not found!")
    
    # Output file paths
    output_dir = Path("/Users/<USER>/development/all-india-temple-website/src/data")
    output_dir.mkdir(exist_ok=True)
    
    simple_output_path = output_dir / "indian_states.json"
    detailed_output_path = output_dir / "indian_states_detailed.json"
    typescript_output_path = output_dir / "extractedStates.ts"
    
    print("Extracting state data from SVG file...")
    print(f"SVG file: {svg_file_path}")
    
    # Extract state data
    states_data = extract_state_data(svg_file_path)
    
    if not states_data:
        print("No state data extracted. Please check the SVG file.")
        return
    
    print(f"\nSuccessfully extracted {len(states_data)} states")
    
    # Save simple mapping
    save_states_data(states_data, simple_output_path)
    
    # Create and save detailed mappings
    detailed_mappings = create_state_mappings(states_data)
    save_states_data(detailed_mappings, detailed_output_path)
    
    # Generate TypeScript file
    generate_typescript_file(states_data, typescript_output_path)
    
    # Print summary
    print("\n" + "="*50)
    print("EXTRACTION SUMMARY")
    print("="*50)
    print(f"Total states extracted: {len(states_data)}")
    print(f"Simple mapping saved to: {simple_output_path}")
    print(f"Detailed mapping saved to: {detailed_output_path}")
    print(f"TypeScript data saved to: {typescript_output_path}")
    
    print("\nSample extracted data:")
    for i, (state_id, state_name) in enumerate(sorted(states_data.items())[:5]):
        print(f"  {state_id}: {state_name}")
    
    if len(states_data) > 5:
        print(f"  ... and {len(states_data) - 5} more states")

if __name__ == "__main__":
    main()
