var ce=(e,r)=>(r=Symbol[e])?r:Symbol.for("Symbol."+e),fe=e=>{throw TypeError(e)};var pe=(e,r,t)=>{if(r!=null){typeof r!="object"&&typeof r!="function"&&fe("Object expected");var l,a;t&&(l=r[ce("asyncDispose")]),l===void 0&&(l=r[ce("dispose")],t&&(a=l)),typeof l!="function"&&fe("Object not disposable"),a&&(l=function(){try{a.call(this)}catch(i){return Promise.reject(i)}}),e.push([t,l,r])}else t&&e.push([t]);return r},de=(e,r,t)=>{var l=typeof SuppressedError=="function"?SuppressedError:function(o,s,n,u){return u=Error(n),u.name="SuppressedError",u.error=o,u.suppressed=s,u},a=o=>r=t?new l(o,r,"An error was suppressed during disposal"):(t=!0,o),i=o=>{for(;o=e.pop();)try{var s=o[1]&&o[1].call(o[2]);if(o[0])return Promise.resolve(s).then(i,n=>(a(n),i()))}catch(n){a(n)}if(t)throw r};return i()};import Ct from"@alloc/quick-lru";import{compileAst as St,env as $t,Features as le,Instrumentation as Nt,optimize as Tt,Polyfills as ae}from"@tailwindcss/node";import{clearRequireCache as Vt}from"@tailwindcss/node/require-cache";import{Scanner as Et}from"@tailwindcss/oxide";import Pt from"node:fs";import R,{relative as De}from"node:path";import ue from"postcss";function Y(e){return{kind:"word",value:e}}function Ie(e,r){return{kind:"function",value:e,nodes:r}}function Fe(e){return{kind:"separator",value:e}}function $(e,r,t=null){for(let l=0;l<e.length;l++){let a=e[l],i=!1,o=0,s=r(a,{parent:t,replaceWith(n){i||(i=!0,Array.isArray(n)?n.length===0?(e.splice(l,1),o=0):n.length===1?(e[l]=n[0],o=1):(e.splice(l,1,...n),o=n.length):e[l]=n)}})??0;if(i){s===0?l--:l+=o-1;continue}if(s===2)return 2;if(s!==1&&a.kind==="function"&&$(a.nodes,r,a)===2)return 2}}function C(e){let r="";for(let t of e)switch(t.kind){case"word":case"separator":{r+=t.value;break}case"function":r+=t.value+"("+C(t.nodes)+")"}return r}var me=92,je=41,ge=58,he=44,Me=34,ve=61,we=62,ke=60,ye=10,We=40,Be=39,be=47,xe=32,Ae=9;function b(e){e=e.replaceAll(`\r
`,`
`);let r=[],t=[],l=null,a="",i;for(let o=0;o<e.length;o++){let s=e.charCodeAt(o);switch(s){case me:{a+=e[o]+e[o+1],o++;break}case ge:case he:case ve:case we:case ke:case ye:case be:case xe:case Ae:{if(a.length>0){let p=Y(a);l?l.nodes.push(p):r.push(p),a=""}let n=o,u=o+1;for(;u<e.length&&(i=e.charCodeAt(u),!(i!==ge&&i!==he&&i!==ve&&i!==we&&i!==ke&&i!==ye&&i!==be&&i!==xe&&i!==Ae));u++);o=u-1;let c=Fe(e.slice(n,u));l?l.nodes.push(c):r.push(c);break}case Be:case Me:{let n=o;for(let u=o+1;u<e.length;u++)if(i=e.charCodeAt(u),i===me)u+=1;else if(i===s){o=u;break}a+=e.slice(n,o+1);break}case We:{let n=Ie(a,[]);a="",l?l.nodes.push(n):r.push(n),t.push(n),l=n;break}case je:{let n=t.pop();if(a.length>0){let u=Y(a);n?.nodes.push(u),a=""}t.length>0?l=t[t.length-1]:l=null;break}default:a+=String.fromCharCode(s)}}return a.length>0&&r.push(Y(a)),r}var He=["anchor-size"],Dt=new RegExp(`(${He.join("|")})\\(`,"g");var m=class extends Map{constructor(t){super();this.factory=t}get(t){let l=super.get(t);return l===void 0&&(l=this.factory(t,this),this.set(t,l)),l}};var jt=new Uint8Array(256);var M=new Uint8Array(256);function v(e,r){let t=0,l=[],a=0,i=e.length,o=r.charCodeAt(0);for(let s=0;s<i;s++){let n=e.charCodeAt(s);if(t===0&&n===o){l.push(e.slice(a,s)),a=s+1;continue}switch(n){case 92:s+=1;break;case 39:case 34:for(;++s<i;){let u=e.charCodeAt(s);if(u===92){s+=1;continue}if(u===n)break}break;case 40:M[t]=41,t++;break;case 91:M[t]=93,t++;break;case 123:M[t]=125,t++;break;case 93:case 125:case 41:t>0&&n===M[t-1]&&t--;break}}return l.push(e.slice(a)),l}var Qt=new m(e=>{let r=b(e),t=new Set;return $(r,(l,{parent:a})=>{let i=a===null?r:a.nodes??[];if(l.kind==="word"&&(l.value==="+"||l.value==="-"||l.value==="*"||l.value==="/")){let o=i.indexOf(l)??-1;if(o===-1)return;let s=i[o-1];if(s?.kind!=="separator"||s.value!==" ")return;let n=i[o+1];if(n?.kind!=="separator"||n.value!==" ")return;t.add(s),t.add(n)}else l.kind==="separator"&&l.value.trim()==="/"?l.value="/":l.kind==="separator"&&l.value.length>0&&l.value.trim()===""?(i[0]===l||i[i.length-1]===l)&&t.add(l):l.kind==="separator"&&l.value.trim()===","&&(l.value=",")}),t.size>0&&$(r,(l,{replaceWith:a})=>{t.has(l)&&(t.delete(l),a([]))}),J(r),C(r)});var Zt=new m(e=>{let r=b(e);return r.length===3&&r[0].kind==="word"&&r[0].value==="&"&&r[1].kind==="separator"&&r[1].value===":"&&r[2].kind==="function"&&r[2].value==="is"?C(r[2].nodes):e});function J(e){for(let r of e)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=U(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=U(r.value);for(let t=0;t<r.nodes.length;t++)J([r.nodes[t]]);break}r.value=U(r.value),J(r.nodes);break}case"separator":r.value=U(r.value);break;case"word":{(r.value[0]!=="-"||r.value[1]!=="-")&&(r.value=U(r.value));break}default:qe(r)}}var Xt=new m(e=>{let r=b(e);return r.length===1&&r[0].kind==="function"&&r[0].value==="var"});function qe(e){throw new Error(`Unexpected value: ${e}`)}function U(e){return e.replaceAll("_",String.raw`\_`).replaceAll(" ","_")}var N=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,sr=new RegExp(`^${N.source}$`);var ur=new RegExp(`^${N.source}%$`);var cr=new RegExp(`^${N.source}s*/s*${N.source}$`);var Ge=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],fr=new RegExp(`^${N.source}(${Ge.join("|")})$`);var Ye=["deg","rad","grad","turn"],pr=new RegExp(`^${N.source}(${Ye.join("|")})$`);var dr=new RegExp(`^${N.source} +${N.source} +${N.source}$`);function k(e){let r=Number(e);return Number.isInteger(r)&&r>=0&&String(r)===String(e)}function z(e,r){if(r===null)return e;let t=Number(r);return Number.isNaN(t)||(r=`${t*100}%`),r==="100%"?e:`color-mix(in oklab, ${e} ${r}, transparent)`}var Ze={"--alpha":Xe,"--spacing":et,"--theme":tt,theme:rt};function Xe(e,r,t,...l){let[a,i]=v(t,"/").map(o=>o.trim());if(!a||!i)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${a||"var(--my-color)"} / ${i||"50%"})\``);if(l.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${a||"var(--my-color)"} / ${i||"50%"})\``);return z(a,i)}function et(e,r,t,...l){if(!t)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(l.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${l.length+1}.`);let a=e.theme.resolve(null,["--spacing"]);if(!a)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${a} * ${t})`}function tt(e,r,t,...l){if(!t.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let a=!1;t.endsWith(" inline")&&(a=!0,t=t.slice(0,-7)),r.kind==="at-rule"&&(a=!0);let i=e.resolveThemeValue(t,a);if(!i){if(l.length>0)return l.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(l.length===0)return i;let o=l.join(", ");if(o==="initial")return i;if(i==="initial")return o;if(i.startsWith("var(")||i.startsWith("theme(")||i.startsWith("--theme(")){let s=b(i);return nt(s,o),C(s)}return i}function rt(e,r,t,...l){t=it(t);let a=e.resolveThemeValue(t);if(!a&&l.length>0)return l.join(", ");if(!a)throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return a}var Rr=new RegExp(Object.keys(Ze).map(e=>`${e}\\(`).join("|"));function it(e){if(e[0]!=="'"&&e[0]!=='"')return e;let r="",t=e[0];for(let l=1;l<e.length-1;l++){let a=e[l],i=e[l+1];a==="\\"&&(i===t||i==="\\")?(r+=i,l++):r+=a}return r}function nt(e,r){$(e,t=>{if(t.kind==="function"&&!(t.value!=="var"&&t.value!=="theme"&&t.value!=="--theme"))if(t.nodes.length===1)t.nodes.push({kind:"word",value:`, ${r}`});else{let l=t.nodes[t.nodes.length-1];l.kind==="word"&&l.value==="initial"&&(l.value=r)}})}var ct=32;var ft=40;function Te(e,r=[]){let t=e,l="";for(let a=5;a<e.length;a++){let i=e.charCodeAt(a);if(i===ct||i===ft){t=e.slice(0,a),l=e.slice(a);break}}return w(t.trim(),l.trim(),r)}var te={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function _(e){return{__BARE_VALUE__:e}}var x=_(e=>{if(k(e.value))return e.value}),h=_(e=>{if(k(e.value))return`${e.value}%`}),P=_(e=>{if(k(e.value))return`${e.value}px`}),Pe=_(e=>{if(k(e.value))return`${e.value}ms`}),H=_(e=>{if(k(e.value))return`${e.value}deg`}),kt=_(e=>{if(e.fraction===null)return;let[r,t]=v(e.fraction,"/");if(!(!k(r)||!k(t)))return e.fraction}),Re=_(e=>{if(k(Number(e.value)))return`repeat(${e.value}, minmax(0, 1fr))`}),yt={accentColor:({theme:e})=>e("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...kt},backdropBlur:({theme:e})=>e("blur"),backdropBrightness:({theme:e})=>({...e("brightness"),...h}),backdropContrast:({theme:e})=>({...e("contrast"),...h}),backdropGrayscale:({theme:e})=>({...e("grayscale"),...h}),backdropHueRotate:({theme:e})=>({...e("hueRotate"),...H}),backdropInvert:({theme:e})=>({...e("invert"),...h}),backdropOpacity:({theme:e})=>({...e("opacity"),...h}),backdropSaturate:({theme:e})=>({...e("saturate"),...h}),backdropSepia:({theme:e})=>({...e("sepia"),...h}),backgroundColor:({theme:e})=>e("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:e})=>e("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),borderOpacity:({theme:e})=>e("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:e})=>e("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...P},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:e})=>e("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...h},caretColor:({theme:e})=>e("colors"),colors:()=>({...te}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...x},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...h},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:e})=>e("borderColor"),divideOpacity:({theme:e})=>e("borderOpacity"),divideWidth:({theme:e})=>({...e("borderWidth"),...P}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:e})=>e("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...e("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...x},flexShrink:{0:"0",DEFAULT:"1",...x},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:e})=>e("spacing"),gradientColorStops:({theme:e})=>e("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...h},grayscale:{0:"0",DEFAULT:"100%",...h},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...x},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...x},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...x},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...x},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Re},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Re},height:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...H},inset:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),invert:{0:"0",DEFAULT:"100%",...h},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:e})=>({auto:"auto",...e("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...x},maxHeight:({theme:e})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),maxWidth:({theme:e})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...e("spacing")}),minHeight:({theme:e})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),minWidth:({theme:e})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...h},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...x},outlineColor:({theme:e})=>e("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},padding:({theme:e})=>e("spacing"),placeholderColor:({theme:e})=>e("colors"),placeholderOpacity:({theme:e})=>e("opacity"),ringColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),ringOffsetColor:({theme:e})=>e("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},ringOpacity:({theme:e})=>({DEFAULT:"0.5",...e("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...H},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...h},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...h},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:e})=>e("spacing"),scrollPadding:({theme:e})=>e("spacing"),sepia:{0:"0",DEFAULT:"100%",...h},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...H},space:({theme:e})=>e("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:e})=>({none:"none",...e("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...x},supports:{},data:{},textColor:({theme:e})=>e("colors"),textDecorationColor:({theme:e})=>e("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},textIndent:({theme:e})=>e("spacing"),textOpacity:({theme:e})=>e("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...P},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Pe},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Pe},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:e})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),size:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),width:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...x}};function re(e){let r=[0];for(let a=0;a<e.length;a++)e.charCodeAt(a)===10&&r.push(a+1);function t(a){let i=0,o=r.length;for(;o>0;){let n=(o|0)>>1,u=i+n;r[u]<=a?(i=u+1,o=o-n-1):o=n}i-=1;let s=a-r[i];return{line:i+1,column:s}}function l({line:a,column:i}){a-=1,a=Math.min(Math.max(a,0),r.length-1);let o=r[a],s=r[a+1]??o;return Math.min(Math.max(o+i,0),s)}return{find:t,findOffset:l}}var bt=64;function K(e,r=[]){return{kind:"rule",selector:e,nodes:r}}function w(e,r="",t=[]){return{kind:"at-rule",name:e,params:r,nodes:t}}function T(e,r=[]){return e.charCodeAt(0)===bt?Te(e,r):K(e,r)}function S(e,r,t=!1){return{kind:"declaration",property:e,value:r,important:t}}function B(e){return{kind:"comment",value:e}}function D(e,r){let t=0,l={file:null,code:""};function a(o,s=0){let n="",u="  ".repeat(s);if(o.kind==="declaration"){if(n+=`${u}${o.property}: ${o.value}${o.important?" !important":""};
`,r){t+=u.length;let c=t;t+=o.property.length,t+=2,t+=o.value?.length??0,o.important&&(t+=11);let p=t;t+=2,o.dst=[l,c,p]}}else if(o.kind==="rule"){if(n+=`${u}${o.selector} {
`,r){t+=u.length;let c=t;t+=o.selector.length,t+=1;let p=t;o.dst=[l,c,p],t+=2}for(let c of o.nodes)n+=a(c,s+1);n+=`${u}}
`,r&&(t+=u.length,t+=2)}else if(o.kind==="at-rule"){if(o.nodes.length===0){let c=`${u}${o.name} ${o.params};
`;if(r){t+=u.length;let p=t;t+=o.name.length,t+=1,t+=o.params.length;let F=t;t+=2,o.dst=[l,p,F]}return c}if(n+=`${u}${o.name}${o.params?` ${o.params} `:" "}{
`,r){t+=u.length;let c=t;t+=o.name.length,o.params&&(t+=1,t+=o.params.length),t+=1;let p=t;o.dst=[l,c,p],t+=2}for(let c of o.nodes)n+=a(c,s+1);n+=`${u}}
`,r&&(t+=u.length,t+=2)}else if(o.kind==="comment"){if(n+=`${u}/*${o.value}*/
`,r){t+=u.length;let c=t;t+=2+o.value.length+2;let p=t;o.dst=[l,c,p],t+=1}}else if(o.kind==="context"||o.kind==="at-root")return"";return n}let i="";for(let o of e)i+=a(o,0);return l.code=i,i}import I,{Input as xt}from"postcss";var At=33;function Oe(e,r){let t=new m(n=>new xt(n.code,{map:r?.input.map,from:n.file??void 0})),l=new m(n=>re(n.code)),a=I.root();a.source=r;function i(n){if(!n||!n[0])return;let u=l.get(n[0]),c=u.find(n[1]),p=u.find(n[2]);return{input:t.get(n[0]),start:{line:c.line,column:c.column+1,offset:n[1]},end:{line:p.line,column:p.column+1,offset:n[2]}}}function o(n,u){let c=i(u);c?n.source=c:delete n.source}function s(n,u){if(n.kind==="declaration"){let c=I.decl({prop:n.property,value:n.value??"",important:n.important});o(c,n.src),u.append(c)}else if(n.kind==="rule"){let c=I.rule({selector:n.selector});o(c,n.src),c.raws.semicolon=!0,u.append(c);for(let p of n.nodes)s(p,c)}else if(n.kind==="at-rule"){let c=I.atRule({name:n.name.slice(1),params:n.params});o(c,n.src),c.raws.semicolon=!0,u.append(c);for(let p of n.nodes)s(p,c)}else if(n.kind==="comment"){let c=I.comment({text:n.value});c.raws.left="",c.raws.right="",o(c,n.src),u.append(c)}else n.kind==="at-root"||n.kind}for(let n of e)s(n,a);return a}function _e(e){let r=new m(i=>({file:i.file??i.id??null,code:i.css}));function t(i){let o=i.source;if(!o)return;let s=o.input;if(s&&o.start!==void 0&&o.end!==void 0)return[r.get(s),o.start.offset,o.end.offset]}function l(i,o){if(i.type==="decl"){let s=S(i.prop,i.value,i.important);s.src=t(i),o.push(s)}else if(i.type==="rule"){let s=T(i.selector);s.src=t(i),i.each(n=>l(n,s.nodes)),o.push(s)}else if(i.type==="atrule"){let s=w(`@${i.name}`,i.params);s.src=t(i),i.each(n=>l(n,s.nodes)),o.push(s)}else if(i.type==="comment"){if(i.text.charCodeAt(0)!==At)return;let s=B(i.text);s.src=t(i),o.push(s)}}let a=[];return e.each(i=>l(i,a)),a}import{normalizePath as Ke}from"@tailwindcss/node";import q from"node:path";var ie="'",ne='"';function oe(){let e=new WeakSet;function r(t){let l=t.root().source?.input.file;if(!l)return;let a=t.source?.input.file;if(!a||e.has(t))return;let i=t.params[0],o=i[0]===ne&&i[i.length-1]===ne?ne:i[0]===ie&&i[i.length-1]===ie?ie:null;if(!o)return;let s=t.params.slice(1,-1),n="";if(s.startsWith("!")&&(s=s.slice(1),n="!"),!s.startsWith("./")&&!s.startsWith("../"))return;let u=q.posix.join(Ke(q.dirname(a)),s),c=q.posix.dirname(Ke(l)),p=q.posix.relative(c,u);p.startsWith(".")||(p="./"+p),t.params=o+n+p+o,e.add(t)}return{postcssPlugin:"tailwindcss-postcss-fix-relative-paths",Once(t){t.walkAtRules(/source|plugin|config/,r)}}}var f=$t.DEBUG,se=new Ct({maxSize:50});function Rt(e,r){let t=`${e}:${r.base??""}:${JSON.stringify(r.optimize)}`;if(se.has(t))return se.get(t);let l={mtimes:new Map,compiler:null,scanner:null,tailwindCssAst:[],cachedPostCssAst:ue.root(),optimizedPostCssAst:ue.root(),fullRebuildPaths:[]};return se.set(t,l),l}function Ot(e={}){let r=e.base??process.cwd(),t=e.optimize??process.env.NODE_ENV==="production";return{postcssPlugin:"@tailwindcss/postcss",plugins:[oe(),{postcssPlugin:"tailwindcss",async Once(l,{result:a}){var F=[];try{let i=pe(F,new Nt);let o=a.opts.from??"";let s=o.endsWith(".module.css");f&&i.start(`[@tailwindcss/postcss] ${De(r,o)}`);{f&&i.start("Quick bail check");let y=!0;if(l.walkAtRules(d=>{if(d.name==="import"||d.name==="reference"||d.name==="theme"||d.name==="variant"||d.name==="config"||d.name==="plugin"||d.name==="apply"||d.name==="tailwind")return y=!1,!1}),y)return;f&&i.end("Quick bail check")}let n=Rt(o,e);let u=R.dirname(R.resolve(o));let c=n.compiler===null;async function p(){f&&i.start("Setup compiler"),n.fullRebuildPaths.length>0&&!c&&Vt(n.fullRebuildPaths),n.fullRebuildPaths=[],f&&i.start("PostCSS AST -> Tailwind CSS AST");let y=_e(l);f&&i.end("PostCSS AST -> Tailwind CSS AST"),f&&i.start("Create compiler");let d=await St(y,{from:a.opts.from,base:u,shouldRewriteUrls:!0,onDependency:G=>n.fullRebuildPaths.push(G),polyfills:s?ae.All^ae.AtProperty:ae.All});return f&&i.end("Create compiler"),f&&i.end("Setup compiler"),d}try{if(n.compiler??=p(),(await n.compiler).features===le.None)return;let y="incremental";f&&i.start("Register full rebuild paths");{for(let g of n.fullRebuildPaths)a.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:R.resolve(g),parent:a.opts.from});let E=a.messages.flatMap(g=>g.type!=="dependency"?[]:g.file);E.push(o);for(let g of E){let A=Pt.statSync(g,{throwIfNoEntry:!1})?.mtimeMs??null;if(A===null){g===o&&(y="full");continue}n.mtimes.get(g)!==A&&(y="full",n.mtimes.set(g,A))}}f&&i.end("Register full rebuild paths"),y==="full"&&!c&&(n.compiler=p());let d=await n.compiler;if(n.scanner===null||y==="full"){f&&i.start("Setup scanner");let E=(d.root==="none"?[]:d.root===null?[{base:r,pattern:"**/*",negated:!1}]:[{...d.root,negated:!1}]).concat(d.sources);n.scanner=new Et({sources:E}),f&&i.end("Setup scanner")}f&&i.start("Scan for candidates");let G=d.features&le.Utilities?n.scanner.scan():[];if(f&&i.end("Scan for candidates"),d.features&le.Utilities){f&&i.start("Register dependency messages");let E=R.resolve(r,o);for(let g of n.scanner.files){let A=R.resolve(g);A!==E&&a.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:A,parent:a.opts.from})}for(let{base:g,pattern:A}of n.scanner.globs)A==="*"&&r===g||(A===""?a.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:R.resolve(g),parent:a.opts.from}):a.messages.push({type:"dir-dependency",plugin:"@tailwindcss/postcss",dir:R.resolve(g),glob:A,parent:a.opts.from}));f&&i.end("Register dependency messages")}f&&i.start("Build utilities");let j=d.build(G);if(f&&i.end("Build utilities"),n.tailwindCssAst!==j)if(t){f&&i.start("Optimization"),f&&i.start("AST -> CSS");let E=D(j);f&&i.end("AST -> CSS"),f&&i.start("Lightning CSS");let g=Tt(E,{minify:typeof t=="object"?t.minify:!0});f&&i.end("Lightning CSS"),f&&i.start("CSS -> PostCSS AST"),n.optimizedPostCssAst=ue.parse(g.code,a.opts),f&&i.end("CSS -> PostCSS AST"),f&&i.end("Optimization")}else f&&i.start("Transform Tailwind CSS AST into PostCSS AST"),n.cachedPostCssAst=Oe(j,l.source),f&&i.end("Transform Tailwind CSS AST into PostCSS AST");n.tailwindCssAst=j,f&&i.start("Update PostCSS AST"),l.removeAll(),l.append(t?n.optimizedPostCssAst.clone().nodes:n.cachedPostCssAst.clone().nodes),l.raws.indent="  ",f&&i.end("Update PostCSS AST"),f&&i.end(`[@tailwindcss/postcss] ${De(r,o)}`)}catch(y){n.compiler=null;for(let d of n.fullRebuildPaths)a.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:R.resolve(d),parent:a.opts.from});console.error(y),l.removeAll()}}catch(Ue){var ze=Ue,Le=!0}finally{de(F,ze,Le)}}}]}}var Cl=Object.assign(Ot,{postcss:!0});export{Cl as default};
