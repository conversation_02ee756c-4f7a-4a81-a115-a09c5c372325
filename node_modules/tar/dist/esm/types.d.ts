export declare const isCode: (c: string) => c is EntryTypeCode;
export declare const isName: (c: string) => c is EntryTypeName;
export type EntryTypeCode = '0' | '' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | 'g' | 'x' | 'A' | 'D' | 'I' | 'K' | 'L' | 'M' | 'N' | 'S' | 'V' | 'X';
export type EntryTypeName = 'File' | 'OldFile' | 'Link' | 'SymbolicLink' | 'CharacterDevice' | 'BlockDevice' | 'Directory' | 'FIFO' | 'ContiguousFile' | 'GlobalExtendedHeader' | 'ExtendedHeader' | 'SolarisACL' | 'GNUDumpDir' | 'Inode' | 'NextFileHasLongLinkpath' | 'NextFileHasLongPath' | 'ContinuationFile' | 'OldGnuLongPath' | 'SparseFile' | 'TapeVolumeHeader' | 'OldExtendedHeader' | 'Unsupported';
export declare const name: Map<EntryTypeCode, EntryTypeName>;
export declare const code: Map<EntryTypeName, EntryTypeCode>;
//# sourceMappingURL=types.d.ts.map