import React from 'react';

// Simple error boundary component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(to bottom right, #f8fafc, #eff6ff)',
          fontFamily: 'Inter, system-ui, sans-serif'
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '0.5rem',
            boxShadow: '0 4px 25px -5px rgba(0, 0, 0, 0.1)',
            textAlign: 'center'
          }}>
            <h2 style={{ color: '#dc2626', marginBottom: '1rem' }}>Something went wrong</h2>
            <p style={{ color: '#6b7280', marginBottom: '1rem' }}>
              {this.state.error?.message || 'An unexpected error occurred'}
            </p>
            <button
              onClick={() => window.location.reload()}
              style={{
                background: '#f97316',
                color: 'white',
                padding: '0.5rem 1rem',
                border: 'none',
                borderRadius: '0.25rem',
                cursor: 'pointer'
              }}
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

function App() {
  console.log('App component is rendering');

  try {
    return (
      <ErrorBoundary>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-center mb-8 text-gradient">
              🏛️ Hindu Temple Locator
            </h1>
            <div className="card text-center">
              <h2 className="text-2xl font-semibold mb-4">
                Welcome to India's Sacred Heritage Explorer
              </h2>
              <p className="text-gray-600 mb-6">
                Discover beautiful temples across India with our interactive map interface.
              </p>
              <button className="btn-primary">
                Start Exploring
              </button>
            </div>

            {/* Debug info */}
            <div className="mt-8 p-4 bg-white rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold mb-2">Debug Information</h3>
              <p className="text-sm text-gray-600">
                ✅ React is working<br/>
                ✅ Tailwind CSS is loaded<br/>
                ✅ Custom styles are applied<br/>
                🌐 Server running on port 5174
              </p>
            </div>
          </div>
        </div>
      </ErrorBoundary>
    );
  } catch (error) {
    console.error('Error in App component:', error);
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: '#fee2e2',
        fontFamily: 'system-ui'
      }}>
        <div style={{ background: 'white', padding: '2rem', borderRadius: '0.5rem' }}>
          <h2 style={{ color: '#dc2626' }}>App Error</h2>
          <p>Failed to render the application</p>
        </div>
      </div>
    );
  }
}

export default App;
