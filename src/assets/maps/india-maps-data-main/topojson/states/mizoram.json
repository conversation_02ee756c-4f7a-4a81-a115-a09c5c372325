{"type": "Topology", "arcs": [[[13063, 17794], [-79, 48], [-60, 37], [-284, 197], [-782, 529], [-404, 443], [-459, 104], [-104, -7], [-542, -41], [-474, -466], [-218, -639], [-86, -134], [-232, -370], [-133, -220], [319, -401], [371, -149], [-432, -291], [-248, -313], [-354, 36], [-167, 288], [-256, 38], [-152, 24], [-338, 109], [-399, -191], [-167, -12], [-173, 312], [-37, 66]], [[7173, 16791], [6, 11], [-5, 10], [7, 109], [9, 35], [-17, 27], [12, 110], [9, 23], [-37, 44], [-14, 56], [-48, 49], [-161, 17], [63, 33], [5, 17], [-10, 75], [-95, 47]], [[6897, 17454], [-87, 98], [36, 131], [-56, 124], [-115, 121], [-21, 118], [9, 110], [-10, 183], [8, 134], [-12, 144], [-3, 129], [57, 98], [-29, 77], [-41, 58], [-38, 117], [-131, 16], [-103, 29], [-88, 144], [48, 72], [96, 20], [19, 78], [54, 68], [142, 12], [34, 67], [27, 129], [-106, 78], [-32, 93], [-5, 66], [82, 42], [126, 20], [125, 61], [50, 110], [-10, 65], [16, 105], [-101, 41], [-179, 27], [-99, 19], [1, 60], [-112, 52], [-82, 38], [-60, 58], [-1, 133], [177, 116], [79, 110], [65, 60], [70, 99], [116, 209], [-69, 45], [-99, 4], [-91, 33], [-59, 69], [-76, 102], [-36, 72], [-42, 55], [-24, 106], [-34, 134], [-101, 48], [88, 64], [91, 52], [34, 162], [-48, 93], [-159, -1], [-79, 43], [71, 66], [62, 73], [81, 93], [79, -46], [136, -7], [84, 87], [-165, 19], [7, 93], [89, 38], [154, 16], [19, 62], [-137, 129], [-51, 75], [-57, 116], [-74, 106], [-1, 100], [-29, 64], [-1, 76], [69, 49], [230, 63], [61, 86], [-84, 140], [-192, 28], [-27, 88], [-131, 27], [-229, 42], [37, 73], [82, 45], [-44, 107], [-2, 1]], [[6146, 24383], [1, -1], [70, 23], [59, 7], [1, 2], [6, 4], [92, 79], [38, 16], [1, 0], [18, -4], [23, -50], [0, -1], [30, -25], [50, -16], [74, -10], [59, 13], [1, 0], [4, 3], [81, 52], [28, 24], [0, 1], [21, 54], [27, 21], [35, 9], [1, 0], [1, 0], [60, -3], [69, -23], [34, -12], [52, -5], [1, 0], [0, 0], [27, 46], [0, 0], [28, 49], [28, 65], [0, 1], [1, 29], [42, -2], [12, -12], [3, 1], [3, 1], [38, 12], [30, -6], [11, -2], [23, 4], [16, -11], [68, 12], [1, 0], [16, -9], [71, -40], [5, 1], [67, 18], [51, 1], [1, -1], [58, -53], [59, -5], [267, 48], [85, 42], [20, 22], [2, 41], [-28, 13], [-1, 0], [-2, 44], [0, 0], [20, 9], [38, -3], [0, 0], [119, -42], [38, 5], [23, 14], [7, 18], [3, 7], [0, 0], [-11, 68], [0, 1], [11, 20], [73, 50], [129, 90], [125, 47], [76, 21], [22, 6], [0, 0], [94, 10], [175, -11], [76, 6], [5, 0], [0, 0], [72, 23], [25, 21], [21, 2], [72, 7], [63, -16], [24, -6], [88, -51], [29, -36], [38, -13], [9, 16], [0, 0], [-16, 57], [0, 0], [1, 1], [16, 20], [56, 19], [0, -1], [46, -81], [34, -15], [6, 17], [42, 113], [58, 25], [1, 1], [-1, 1], [-34, 88], [0, 0], [0, 0], [1, 0], [45, -7], [21, 5], [15, 23], [-1, 34], [0, 72], [-37, 10], [-1, 0], [-5, 17], [0, 1], [25, 41], [0, 0], [1, 1], [-13, 33], [-8, 24], [0, 1], [31, 56], [0, 0], [-19, 34], [1, 0], [33, 30], [11, 5], [1, 0], [0, 0], [13, 59], [74, 12], [0, 1], [3, 44], [1, 7], [4, 45], [28, 63], [16, 35], [0, 1], [-54, 78], [1, 1], [8, 10], [30, 7], [0, 1], [-8, 45], [1, -1], [28, -6], [7, 13], [3, 28], [2, 24], [2, 16], [0, 1], [14, 11], [70, 13], [52, 30], [18, 11], [8, 67], [-18, 43], [0, 0], [-1, 2], [1, 1], [38, 26], [37, 26], [1, 1], [30, 36], [47, 17], [1, 0], [-1, 1], [-1, 2], [-70, 74], [1, 46], [0, 0], [0, 0], [2, 2], [0, 0], [33, 54], [47, 42], [75, 37], [1, 2], [29, 34], [6, 29], [0, 0], [0, 1], [-24, 28], [1, 1], [9, 13], [37, 13], [0, 1], [16, 62], [0, 0], [-8, 26], [-18, -1], [-22, -2], [-7, 12], [1, 0], [27, 3], [7, 3], [74, 29], [41, 5], [5, 13], [3, 10], [0, 0], [1, 0], [-3, 127], [19, 51], [11, 27], [-27, 107], [1, 0], [42, 3], [0, 0], [0, 0], [-1, 2], [-18, 34], [1, 0], [57, 15], [2, 0], [8, 12], [0, 1], [1, 1], [0, 0], [-4, 22], [-11, 7], [-3, 2], [1, 1], [0, 1], [23, 25], [-1, 1], [-3, 10], [-35, -2], [-6, 0], [1, 2], [2, 3], [19, 27], [31, 44], [0, 1], [5, 17], [14, 41], [1, 4], [0, 0], [0, 0], [-15, 116], [-3, 4], [-38, 52], [-49, 18], [-1, 0], [-1, 2], [-49, 56], [-38, 0], [-1, 0], [-1, 0], [-53, -77], [-64, -40], [-45, 11], [-33, 44], [-1, 1], [-44, 34], [-14, 10], [-158, 4], [-41, 4], [-7, 14], [1, 0], [0, 1], [25, 19], [0, 1], [-22, 52], [1, 0], [68, 22], [-1, 1], [-34, 40], [0, 0], [0, 1], [10, 39], [-31, 1], [-27, 2], [-7, 7], [0, 1], [16, 24], [0, 1], [73, 17], [-1, 2], [-49, 37], [-1, 1], [0, 55], [0, 0], [43, 39], [2, 3], [47, 42], [21, 4], [25, 4], [31, -9], [33, 5], [0, 1], [-1, 13], [0, 0], [0, 1], [-21, 24], [-26, 27], [1, 1], [0, 1], [37, 49], [-1, 0], [-45, 40], [-6, 21], [-12, 49], [-1, 1], [-21, 34], [-57, 16], [-3, 1], [-1, 2], [-38, 97], [-25, 5], [-22, -8], [-39, -13], [-33, 20], [1, 1], [58, 71], [49, 19], [6, 2], [80, 15], [12, 41], [-54, -3], [-26, 39], [-38, 12], [1, 2], [22, 37], [22, -21], [2, 0], [27, 6], [57, 32], [0, 0], [58, 13], [55, 40], [0, 0], [27, 48], [36, 42], [110, 126], [1, 1], [92, 54], [-1, 0], [-52, 13], [-29, 53], [-31, 24], [1, 1], [44, 24], [-1, 1], [-10, 34], [60, 1], [5, 0], [15, 13], [0, 0], [0, 0], [20, 101], [-26, 18], [-81, 6], [-19, 13], [-2, 1], [70, 79], [13, 44], [32, -44], [22, -3], [17, 6], [8, 14], [3, 6], [1, 2], [0, 1], [-13, 15], [-12, 13], [0, 0]], [[10593, 29692], [76, -10], [0, 0], [20, 3], [50, 9], [0, 0], [0, 0], [11, 11], [-2, 45], [59, 50], [38, 118], [196, -88], [-14, -19], [18, -18], [28, -5], [38, -44], [134, -37], [33, 14], [13, 28], [34, 24], [59, 10], [92, 28], [26, 8], [14, -1], [30, -2], [2, -1], [11, -8], [49, -61], [8, -10], [6, -4], [30, -22], [0, 0], [174, 65], [80, 79], [76, 23], [86, -26], [48, 1], [59, -26], [57, -43], [45, -5], [40, 21], [395, -156], [-120, -127], [83, -70], [-33, -100], [-14, -87], [37, -146], [169, -28], [192, -14], [-135, -303], [-187, -214], [-95, -92], [-118, -61], [-48, -92], [95, -107], [-119, -108], [-1, -107], [187, -214], [142, -45], [-119, -46], [0, -46], [70, -91], [-23, -91], [-71, -30], [-23, -92], [-70, -30], [-155, 0], [1, -39], [129, -147], [-126, -98], [35, -164], [-79, -46], [-125, -43], [-33, -35], [-4, -42], [33, -79], [-113, -139], [-141, -30], [-65, -30], [20, -84], [25, -26], [140, 0], [74, 18], [189, -14], [149, -64], [73, -66], [122, -38], [97, 27], [49, -34], [73, -20], [50, -49], [264, -55], [191, 43], [142, 75], [95, 30], [95, -153], [70, -77], [95, -45], [118, -77], [-24, -91], [23, -46], [-23, -76], [47, -46], [-71, -46], [24, -46], [165, 62], [23, 61], [118, 15], [24, 46], [141, 46], [95, 107], [0, 77], [118, 91], [94, 16], [175, -52], [47, -76], [142, -76], [-48, -123], [47, -76], [94, -15], [306, 31], [71, -46], [172, -17]], [[15759, 25521], [-207, -406], [-386, -899], [292, -398], [-458, -298], [61, -518], [-254, -358], [-410, -145], [-827, -293], [-511, -106], [10, -705], [9, -460], [1, -36]], [[13079, 20899], [3, -160], [184, -1187], [-199, -970], [243, -719], [-247, -69]], [[15759, 25521], [134, -13], [70, 137], [24, 76], [47, 76], [-47, 92], [71, 61], [165, -31], [94, 31], [94, -15], [260, -153], [0, -61], [48, -91], [-71, -31], [-165, -289], [47, -122], [310, 122], [47, 76], [142, 77], [47, 61], [167, 0], [236, 30], [411, -9], [80, -107], [-103, -24], [27, -102], [75, -43], [-87, -83], [56, -138], [-17, -124], [-5, -77], [-76, -108], [51, -51], [164, -26], [70, -33], [25, -72], [45, -131], [66, -80], [84, -105], [101, -66], [78, -34], [91, -20], [161, -26], [128, -46], [15, -71], [-7, -66], [-27, -73], [-14, -173], [-19, -138], [-3, -129], [16, -70], [8, -86], [-6, -64], [3, -95], [6, -119], [50, -68], [-16, -135], [37, -58], [91, -28], [68, -114], [-16, -108], [-41, -104], [-52, -184], [-4, -74], [17, -90], [31, -72], [56, -85], [65, -63], [16, -61], [38, -71], [-31, -69], [-6, -107], [24, -72], [105, -99], [104, -40], [115, -55], [80, -96], [55, -71], [-62, -51], [-80, -78], [-45, -56], [-51, -84], [-34, -81], [5, -102], [68, -320], [11, -288], [22, -75], [-28, -78], [-99, -130], [-51, -123], [53, -104], [14, -94], [-6, -85], [-36, -85], [-60, -49], [-95, -81], [-84, -88], [-59, -57], [-1, -14], [-238, -80]], [[18806, 18838], [-358, -40], [-232, -49], [-306, -135], [-169, -111], [-213, -57], [-257, 37], [-109, 58], [-284, 150], [-108, -64], [10, -115], [6, -63], [-29, -182], [-112, -15], [0, 12], [1, 77], [-5, 54], [-12, 111], [-20, 14], [-36, 25], [-28, 19], [-14, 9], [51, 77], [21, 32], [32, 29], [24, 22], [81, 75], [49, 55], [23, 26], [11, 13], [35, 39], [6, 38], [2, 12], [3, 18], [8, 53], [-6, 34], [-3, 17], [0, 0], [-8, 52], [67, 94], [25, 70], [15, 45], [-59, 139], [-36, 31], [-234, 207], [30, 88], [71, 74], [36, 37], [36, 36], [-37, 76], [-21, 45], [84, 126], [42, 148], [37, 188], [26, 229], [-55, 192], [-73, 208], [-72, 158], [-52, 111], [-59, 193], [-142, 121], [-140, -41], [-246, -84], [-381, -18], [-137, 88], [-3, 159], [-132, 206], [-238, 76], [-223, -72], [-57, -251], [32, -333], [80, -99], [70, -88], [38, -263], [-118, -203], [-15, -24], [-136, -31], [-142, -33], [-18, -11], [-109, -67], [-98, -61], [-257, -14], [-135, -7], [-3, 0], [-197, 70], [-157, 56], [-164, 220], [-162, -58], [-50, -19], [-14, -1], [-51, 7], [-70, 9], [-85, 6], [-73, 2], [-19, -3]], [[15583, 13481], [-15, 117], [-39, 131], [-77, 272], [-84, 279], [-302, 704], [-574, 188], [-869, 145], [-460, 80], [-210, 380], [-196, 61], [-222, 152], [34, 143], [437, 414]], [[13006, 16547], [29, -1], [23, 6], [56, 12], [149, 49], [372, -5], [37, -1], [74, 0], [139, 1], [66, 16], [27, 6], [86, 39], [100, 41], [152, 12], [205, 10], [19, 7], [101, 41], [85, 67], [170, 104], [237, 48], [379, -18], [156, -38], [56, -22], [31, 1], [20, 1], [2, 16], [6, 22], [6, 19], [4, 18], [10, 22], [1, 8], [46, 16], [9, -4], [12, -3], [9, 1], [14, 10], [11, 11], [2, 4], [79, 68], [12, 0], [5, 7], [-2, 6], [0, 3], [-2, 9], [-10, 9], [-5, 8], [-13, 5], [-80, 91], [4, 10], [17, 5], [26, 2], [7, 8], [-3, 8], [-5, 9], [-14, 7], [-12, 9], [-1, 7], [1, 4], [15, 7], [4, 1], [12, 4], [0, 2], [85, 37], [1, 0], [36, -1], [17, 1], [2, 10], [0, 4], [9, 3], [14, 11], [-1, 14], [-33, 29], [-2, 4], [15, 25], [14, -2], [11, -4], [6, -5], [3, -14], [10, 1], [7, 7], [1, 8], [-5, 8], [-5, 8], [0, 0], [121, 21], [12, -2], [8, 5], [38, -9], [2, -6], [18, -9], [19, 4], [3, 0], [20, -5], [2, -1], [5, -5], [14, -8], [16, -4], [5, 8], [0, 0], [135, 47], [12, 2], [20, -3], [16, -9], [7, -6], [22, -4], [11, 7], [-6, 8], [-13, 8], [-13, 5], [0, 3], [49, 9], [2, 0], [11, -6], [8, -11], [11, -5], [14, 7], [6, 9], [8, 18], [31, -45], [-1, -43], [45, -93], [4, -15], [21, -83], [-2, -73], [75, -117], [16, -26], [31, -112], [31, -38], [34, -37], [19, -7], [35, -14], [79, -9], [26, 1], [96, 3], [48, 33], [231, 143], [244, 46], [127, -68], [3, -3], [6, -3], [143, -76], [29, -37], [76, -55], [68, -17], [26, 0]], [[18251, 16819], [321, -13], [3, -6], [-30, -72], [-14, -88], [5, -81], [10, -76], [36, -65], [23, -92], [-49, -83], [-47, -50], [-6, -98], [37, -126], [18, -98], [81, -137], [85, -120], [104, -130], [1, -57], [-122, -101], [-14, -112], [-22, -170], [-63, -159], [-15, -96], [-75, -97], [-35, -104], [86, -82], [191, 52], [63, -43], [-14, -70], [-193, -134], [-98, -43], [-116, -41], [-67, -47], [-17, -127], [-15, -74], [-18, -57], [-77, -54], [-81, -76], [-23, -53], [118, -29], [-14, -62], [-73, -97], [-130, 0], [-75, -116], [-64, -140], [-68, -97], [-51, -49], [-91, -81], [-47, -37], [-104, 4], [-146, -103], [-70, -64], [-70, -31], [-158, -14], [-159, 13], [-77, 56], [-97, 25], [-149, -43], [-97, -27], [-90, 37], [-109, 55], [-56, 43], [-61, 52], [-40, 64], [-58, 69], [-132, 150], [-102, 51], [-130, 91], [-83, 12], [-38, 10]], [[6146, 24383], [-119, 43], [0, 44], [0, 2], [0, 1], [45, 106], [42, 97], [0, 1], [110, 91], [0, 0], [-1, 1], [-24, 56], [1, 30], [1, 0], [9, 27], [1, 4], [23, 21], [0, 2], [-6, 39], [-132, 64], [-22, 26], [-1, 1], [1, 0], [44, 52], [0, 2], [0, 2], [7, 55], [0, 0], [17, 39], [1, 3], [37, 40], [5, 27], [36, 38], [78, 16], [19, 9], [9, 19], [-1, 0], [-82, 24], [-60, 44], [-38, 8], [-28, -7], [-10, -13], [-2, -3], [0, -32], [21, -45], [0, -2], [1, 0], [-65, 10], [-29, 23], [-2, 2], [-22, 30], [-36, 49], [-17, 77], [-25, 36], [-52, 7], [-166, 0], [0, 0], [-37, 32], [0, 0], [-111, 97], [0, 0], [-69, 10], [-21, 12], [-14, 15], [-1, 1], [0, 1], [-4, 114], [-38, 14], [0, 0], [-1, 0], [-2, 0], [-19, 0], [-133, -1], [-2, -11], [-1, 0], [1, -1], [47, -65], [-2, -21], [-9, -9], [-3, -2], [-55, -48], [-2, -31], [0, -2], [16, -114], [-44, -40], [-52, -92], [-40, 15], [-71, -3], [-27, 10], [1, 2], [59, 113], [1, 0], [18, 34], [-1, 0], [0, 0], [-96, 7], [3, 49], [0, 0], [-1, 2], [-11, 28], [-42, 15], [-18, -1], [-3, 0], [-2, 0], [-1, -2], [-4, -3], [-39, -31], [-10, -23], [-1, -3], [-4, -8], [-2, -13], [0, -2], [-17, -111], [15, -50], [0, -1], [1, -1], [-140, -75], [-41, -11], [-4, 54], [-3, 10], [-1, 2], [-35, 106], [2, 12], [0, 0], [1, 4], [8, 35], [3, 5], [0, 0], [16, 23], [0, 50], [-1, 1], [0, 0], [-31, 65], [1, 1], [56, 112], [0, 0], [0, 0], [9, 54], [2, 19], [0, 1], [1, 6], [-41, 154], [33, 41], [0, 0], [0, 0], [1, 1], [0, 2], [6, 50], [-39, 123], [16, 28], [3, 7], [0, 0], [56, 99], [22, 18], [-6, 15], [0, 0], [-96, 6], [-5, 16], [22, 33], [6, 37], [49, 35], [2, 3], [38, 47], [8, 33], [0, 1], [-24, 74], [-42, 40], [-14, 30], [-183, 105], [-2, 36], [46, 15], [2, 0], [63, 63], [0, 2], [0, 0], [1, 59], [-59, 29], [-1, 1]], [[4571, 27108], [1, 0], [8, 3], [-6, 6], [0, 0], [-27, 26], [104, 156], [1, 2], [0, 0], [-23, 41], [15, 25], [0, 0], [69, 52], [16, 53], [-3, 43], [58, 70], [27, 55], [0, 1], [7, 89], [-14, 35], [3, 36], [0, 0], [29, 67], [2, 2], [28, 30], [128, 23], [9, 1], [93, 27], [286, -58], [102, -7], [56, 14], [36, 32], [9, 2], [31, 6], [131, -1], [35, 0], [0, 0], [33, 11], [60, 39], [25, 30], [40, 95], [0, 1], [4, 82], [1, 1], [17, 57], [37, 73], [0, 2], [6, 65], [0, 0], [-2, 3], [-4, 10], [-6, 3], [-2, 1], [-22, 10], [-7, 27], [0, 1], [25, 101], [0, 1], [-5, 56], [0, 0], [1, 0], [26, 49], [-3, 37], [60, 107], [0, 1], [5, 126], [0, 0], [1, 2], [14, 21], [32, 22], [70, -13], [4, -1], [87, 29], [2, 0], [28, -3], [8, -13], [-16, -1], [-2, 0], [-10, -1], [3, -19], [43, -11], [9, -25], [19, -12], [67, -13], [1, 2], [14, 17], [-12, 31], [58, -34], [2, -1], [3, -2], [13, 6], [-1, 1], [-6, 18], [16, -2], [1, 0], [24, -4], [3, 31], [6, 29], [67, 47], [19, 29], [-1, 24], [15, -1], [0, 0], [12, 7], [26, 14], [2, -2], [2, -1], [21, -18], [17, 4], [14, 36], [19, -19], [29, 5], [129, 51], [80, -25], [69, -4], [4, 30], [66, 25], [44, 34], [17, 20], [0, 1], [1, 68], [36, 18], [5, 16], [-16, 35], [29, 21], [31, 54], [23, 11], [69, 4], [28, 12], [-11, 92], [67, 38], [0, 2], [-1, 64], [0, 0], [113, 156], [0, 2], [-1, 60], [1, 0], [65, 71], [94, 239], [112, 204], [217, 265], [86, 168], [42, 71], [53, 82], [-1, 1], [-13, 71], [0, 0], [91, 57], [89, 85], [11, 25], [231, -417], [35, -99], [15, -43], [7, -8], [29, -36], [31, -90], [60, -93], [66, -147], [56, -99], [8, -14], [5, -4], [37, -29], [42, -57], [7, -17], [16, -35], [-7, -16], [-63, -3], [0, 0], [0, 0], [13, -29], [0, 0], [0, 0], [48, -3], [2, 0], [7, 2], [33, 7], [15, 4], [4, -1], [35, -6], [-8, -21], [-3, -8], [10, -21], [0, 0], [21, 1], [11, 1], [-1, -4], [-3, -17], [44, -8], [44, -9], [0, 0], [0, 0], [15, 3], [16, 3], [35, -54], [11, -18], [7, -4], [54, -28], [82, -27], [10, -2], [16, -16], [0, 0], [5, -13], [-83, -71], [-9, -12], [-17, -25], [4, -18], [2, -7], [11, -9], [36, -26], [-2, -76], [10, -12], [4, -6], [13, -4], [49, -14], [0, 0], [0, 0], [6, 3], [52, 25], [56, 26], [8, -1], [69, -10], [0, 0], [0, 0], [24, 16], [20, 13], [11, 14], [25, 33], [43, 75], [46, 20], [76, 53], [64, 13], [147, -30], [34, 3], [72, 20], [0, 0], [0, 0], [100, 50], [63, -17], [46, 8], [15, -7], [44, -78], [33, -13], [0, 0]], [[11240, 7406], [47, -115], [39, -65], [20, -123], [-84, -383], [8, -194], [59, -89], [101, -93], [2, -59], [-72, -51], [17, -236], [38, -100], [19, -81], [31, -120], [57, -85], [18, -71], [196, -46], [36, -52], [-66, -185], [-97, -64], [-268, 82], [-32, 63], [74, 55], [-63, 40], [-122, 33], [-154, -75], [-69, -59], [-132, -75], [-93, -65], [-87, -65], [-86, -35], [-124, -49], [-66, -37], [-42, -52], [-94, -108], [-104, -193], [-51, -49], [-220, -9], [-174, 100], [-266, -11], [-20, -62], [64, -70], [9, -68], [-23, -60], [43, -104], [114, -76], [51, -81], [-46, -98], [-53, -56], [34, -59], [12, -69], [-37, -108], [17, -70], [26, -57], [26, -53], [19, -94], [8, -76], [17, -121], [-15, -128], [16, -131], [-7, -87], [27, -73], [14, -179], [9, -84], [30, -135], [6, -91], [30, -87], [-44, -127], [-6, -111], [41, -114], [-3, -112], [22, -95], [-9, -91], [62, -121], [41, -88]], [[9911, 1449], [-132, -39], [-72, 70], [-53, 47], [-82, 53], [-180, 41], [-88, 129], [-53, 64], [-111, 25], [-117, 56], [-135, 48], [-106, 33], [-93, -3], [-95, 14], [-135, 74], [15, 88], [-74, 39], [-238, 78], [-135, 40], [-88, 37], [-84, 128], [-246, 38], [-164, 10], [-143, -62], [21, -121], [-16, -194], [-42, -279], [6, -159], [-66, -109], [-48, 213], [-17, 149], [-95, -21], [11, -161], [13, -79], [8, -109], [17, -80], [7, -83], [-16, -55], [6, -104], [55, -109], [-8, -64], [-22, -61], [-15, -83], [-48, -72], [-60, -45], [-152, 7], [-139, 10], [-306, -210], [-61, -72], [-182, -88], [-197, -43], [-97, -13], [-20, 62], [112, 53], [-90, 160], [-170, 114], [43, 124], [43, 62], [-22, 80], [-79, 58], [-3, 59], [61, 116], [1, 57], [-25, 63], [-89, 49], [-16, 156], [-180, 238], [-87, 141], [-84, 122], [-66, 60], [-123, 36], [151, 104], [112, 85], [103, -44], [100, -61], [91, -53], [82, -38], [74, -43], [16, 146], [15, 130], [15, 140], [8, 79], [-70, 62], [-58, 45], [-64, 53], [-51, 41], [-15, 130], [-11, 107], [-12, 91], [-10, 69], [-11, 93], [-10, 89], [-15, 103], [-16, 90], [-15, 133], [-10, 95], [-12, 88], [-11, 105], [-11, 97], [-17, 118], [-25, 188], [-13, 109], [-75, 168], [-55, 113], [-55, 134], [-58, 135], [-67, 140], [-53, 128], [-54, 120], [-51, 125], [-23, 116], [-22, 150], [-20, 153], [-26, 165], [-23, 159], [-21, 143], [-14, 99], [119, 86], [-119, 19], [124, 165], [-103, 121], [-157, 186], [-126, 147], [-74, 86], [-25, 27], [89, 115], [47, 61], [58, 74], [52, 70], [34, 42], [-22, 81], [-43, 163], [-31, 113], [-19, 60], [-8, 48], [-14, 50], [-13, 57], [-23, 71], [-35, 135], [-26, 99], [-74, 281], [-51, 197], [-39, 134], [-28, 97], [-44, 180], [-20, 72], [0, 2]], [[4187, 9670], [12, 26], [1, 2], [14, 49], [68, 32], [43, 67], [105, 59], [76, -33], [191, 18], [109, -34], [61, -57], [169, -91], [162, -165], [115, 38], [57, 41], [4, 89], [119, 29], [98, -212], [41, -4], [18, -27], [22, -54], [50, -26], [5, -99], [-33, -98], [37, -111], [57, -71], [71, -40], [28, -77], [58, -22], [116, -86], [95, -41], [99, -54], [-36, -35], [6, -38], [104, -33], [69, -44], [37, -51], [70, -130], [-26, -111], [-2, -114], [250, 37], [208, 56], [151, -1], [217, -96], [-51, -116], [32, -139], [39, -69], [241, -116], [29, -61], [73, -86], [74, -52], [336, -152], [55, -55], [138, -64], [19, -95], [4, -163], [99, -237], [32, -34], [80, -6], [104, 51], [20, -31], [36, -18], [44, 7], [-32, 187], [-35, 57], [39, 151], [43, 92], [149, 112], [109, 52], [8, 56], [85, 127], [4, 91], [-42, 76], [-12, 58], [-68, 146], [7, 56], [-13, 32], [119, 29], [20, 60], [-31, 48], [3, 64], [81, 71], [86, 6], [148, -29], [58, 5], [186, -21], [134, -41], [90, -94], [106, -74], [97, -52], [52, -45], [99, -47], [103, -98], [96, -63], [264, 9], [70, -93], [9, -63], [74, -219], [69, -80], [73, 3], [188, 59], [97, 56]], [[7173, 16791], [-4, -8], [-27, -9], [-15, 6], [-40, -10], [64, -109], [-15, -71], [79, -51], [-38, -123], [-41, -78], [24, -70], [-33, -56], [14, -10], [55, -2], [-24, -69], [37, -53], [30, -105], [-15, -151], [-16, -67], [32, -36], [-9, -33], [31, -181], [36, -68], [176, -107], [-6, -41], [-63, -79], [55, -5], [-9, -25], [54, -34], [117, -15], [97, 8], [43, 170], [42, 55], [-24, 81], [23, 61], [-29, 74], [-17, 128], [8, 23], [186, -18], [34, -25], [42, -58], [68, 22], [70, -14], [72, 5], [24, -7], [28, -13], [32, -61], [134, 50], [32, 20], [79, 10], [103, -58], [184, 25], [134, -36], [62, -17], [234, -9], [29, 11], [20, 25], [-11, 66], [20, 68], [22, 22], [33, 0], [39, 15], [160, 36], [55, -13], [18, -40], [114, -38], [40, -18], [0, -16], [-2, -4], [-2, -9], [13, -2], [-12, -31], [18, -29], [-10, -21], [33, -47], [50, -24], [11, -17], [39, -17], [52, -43], [20, -53], [-52, -181], [10, -40], [50, -75], [8, -30], [41, -48], [-19, -54]], [[10070, 14940], [-6, -19], [19, -122], [-59, -137], [0, -130], [-22, -28], [-76, -95], [4, -6], [94, -131], [16, -18], [53, -62], [-19, -72], [-79, -122], [9, -13], [79, -103], [38, -50], [81, -109], [10, -158], [3, -11], [36, -119], [0, -17], [0, -92], [43, -192], [16, -75], [59, -245], [10, -43], [113, -193], [6, -10], [-6, -11], [-39, -70], [-14, -27], [14, -98], [5, -39], [18, -130], [2, -14], [-16, -14], [-309, -261], [-25, -20], [-117, -97], [-3, -9], [37, -46], [71, -88], [5, -7], [36, -43], [5, -7], [-6, -6], [-30, -28], [-102, -96], [-114, -103], [-31, -45], [22, -56], [14, 2], [46, 11], [74, 18], [118, 29], [147, -138], [-28, -18], [-70, -46], [-7, -15], [-33, -72], [167, -151], [1, -1], [50, -39], [78, -62], [-11, -108], [0, -7], [33, -71], [32, -66], [77, -48], [107, -57], [75, -41], [96, -31], [32, -60], [3, -5], [40, -40], [78, -88]], [[11020, 10149], [30, -35], [72, -53], [-42, -24], [-85, -87], [-21, -336], [20, -102], [21, -68], [209, -107], [55, -85], [-66, -273], [14, -81], [2, -172], [129, -222], [167, -151], [55, -63], [-57, -101], [36, -209], [-82, -85], [22, -78], [-15, -161], [-72, -137], [-68, -55], [-104, -58]], [[4187, 9670], [-1, -2], [-20, -49], [-95, 32], [-102, 36], [-87, 30], [-111, 32], [-103, 40], [-31, 168], [-33, 177], [-31, 116], [-27, 93], [-76, 90], [-47, 47], [-58, 55], [-53, 56], [-31, 27], [63, 66], [53, 67], [-93, 90], [-51, 58], [161, -8], [-101, 117], [124, 34], [-146, 73], [-41, 22], [98, 83], [24, 21], [-145, 51], [-48, 16], [17, 145], [7, 60], [-98, -31], [-84, -28], [-86, -28], [-103, 171], [-85, 137], [-50, 87], [-62, -15], [-244, -61], [-25, 69], [-3, 46], [42, 75], [-71, 21], [-141, 25], [-95, 16], [-202, 39], [17, 71], [18, 80], [20, 84], [10, 46], [10, 48], [7, 43], [-33, 47], [63, 58], [16, 61], [17, 79], [16, 71], [15, 65], [15, 57], [11, 62], [16, 63], [1, 51], [13, 52], [24, 59], [10, 45], [3, 66], [17, 51], [24, 67], [23, 88], [11, 57], [-36, 61], [-50, 92], [-26, 40], [-23, 39], [-43, 76], [-24, 46], [-32, 15], [-51, -4], [-75, 75], [-85, 46], [-68, 42], [-51, 26], [54, 100], [36, 64], [40, 72], [31, 53], [-51, 28], [-92, 48], [-33, 16], [55, 128], [31, 71], [35, 75], [-26, 72], [-28, 99], [-32, 101], [-15, 61], [-28, 48], [-16, 70], [-12, 55], [-30, 81], [-8, 54], [-25, 74], [-27, 92], [-48, 160], [161, -1], [105, 2], [101, 5], [83, 15], [243, 0], [86, 9], [100, -5], [-10, 23], [-35, 55], [-36, 68], [-26, 44], [-37, 61], [-17, 38], [-27, 40], [114, 1], [110, -3]], [[2402, 16035], [41, -24], [75, 27], [13, -12], [68, -41], [210, 24], [115, 110], [129, 80], [-68, 29], [-4, 31], [165, 8], [121, 18], [57, 134], [50, 71], [0, 23], [55, 38], [39, 121], [15, 6], [89, -18], [49, 52], [-45, 65], [-31, 181], [-132, 88], [21, 21], [-1, 68], [33, -14], [-8, 84], [-75, 18], [-14, 17], [24, 27], [-3, 83], [-17, 65], [87, 59], [23, 52], [21, 5], [167, 8], [67, -31], [56, -52], [87, -19], [82, 32], [23, 72], [48, -23], [22, 1], [13, -18], [95, -41], [61, -56], [155, -2], [0, 0], [126, 11], [74, -21], [75, 15], [10, 1], [91, 3], [82, -13], [17, -9], [-1, -6], [33, -9], [109, -82], [124, 23], [26, 62], [25, 14], [171, -10], [162, 89], [30, 74], [-41, 131], [67, 44], [161, -44], [30, -51], [169, -32], [59, -59], [70, -35], [87, -6], [59, 51], [113, 11], [72, -37], [92, -75], [209, -12], [99, 5], [117, 19]], [[2402, 16035], [-110, 3], [-25, 5], [-64, 70], [-48, 52], [-41, 48], [-56, 65], [31, 102], [29, 91], [19, 59], [-92, 54], [-59, 32], [-60, 33], [-85, 47], [-65, 36], [62, 127], [38, 78], [29, 61], [30, 59], [24, 50], [48, 100], [-48, 49], [-59, -3], [-37, 60], [-153, -32], [-8, 81], [82, 58], [-71, 7], [-136, 12], [36, 145], [9, 42], [-26, 80], [39, -18], [17, 58], [7, 47], [-45, 11], [-112, 22], [-5, 82], [-6, 63], [-5, 49], [-6, 53], [-60, -20], [-95, -31], [-68, 0], [-7, 74], [-3, 66], [-7, 74], [3, 73], [5, 54], [0, 57], [-14, 42], [-74, 20], [-45, 2], [-107, 43], [96, 54], [49, 31], [-25, 26], [-67, 35], [-72, 40], [2, 99], [1, 105], [7, 137], [3, 75], [3, 83], [-87, 50], [-94, 52], [7, 181], [16, 181], [9, 146], [-47, 72], [-36, 62], [-142, 7], [-20, 66], [-98, 14], [0, 94], [3, 84], [-2, 75], [51, 36], [86, 15], [-113, 28], [-174, 20], [61, 159], [-156, 50], [20, 43], [52, 100], [54, 99], [51, 107], [28, 52], [50, 101], [44, 89], [-39, 69], [-36, 68], [-39, 73], [-72, 130], [-63, 18], [-175, 55], [-27, 189], [-17, 153], [-10, 92], [-13, 61], [-6, 72], [-30, 223], [-13, 103], [-12, 51], [-4, 63], [-12, 56], [205, 21], [117, 8], [41, 0], [25, 2], [31, 6], [-11, 13], [-8, 10], [-5, 7], [-4, 5], [-6, 9], [-4, 7], [-11, 20], [-33, 55], [-49, 69], [-38, 51], [-54, 80], [69, 72], [46, 50], [60, 65], [57, 66], [38, 46], [85, -12], [48, -7], [22, -5], [23, -4], [68, -13], [61, -6], [64, -12], [46, -6], [42, -8], [34, 17], [-6, 19], [-7, 19], [-4, 34], [-9, 30], [-8, 21], [-3, 18], [-5, 16], [-7, 26], [-8, 30], [96, -12], [1, 6], [-5, 1], [-10, 2], [-5, 20], [14, 9], [37, -5], [3, 30], [-20, 22], [11, 15], [-3, 6], [-5, 10], [11, 5], [13, -13], [12, 17], [15, 2], [24, 155], [17, 5], [29, -2], [32, 22], [35, -9], [11, 25], [11, 51], [-29, 86], [21, 31], [-17, 21], [-28, 10], [-112, 26], [-51, 51], [-78, 51], [-11, 82], [11, 25], [-11, 9], [-25, 19], [24, 143], [-20, 38], [20, 3], [0, 0], [12, 2], [-12, 112], [43, 12], [-4, 12], [45, -1], [32, 39], [41, -1], [11, 55], [20, -1], [26, 43], [37, 17], [-1, 13], [-9, 6], [-23, 15], [54, 27], [-29, 148], [3, 28], [-46, 28], [-125, 135], [5, 14], [24, 5], [34, -15], [13, 10], [-56, 46], [13, 35], [-40, 31], [43, 27], [1, 13], [-86, 41], [51, 34], [5, 25], [-35, 65], [-2, 46], [26, 32], [36, -15], [21, 13], [-28, 74], [52, 18], [-44, 31], [-38, 66], [23, 28], [17, 5], [33, -10], [36, 12], [-8, 26], [13, 18], [24, 6], [37, 9], [-5, 23], [-12, 1], [-57, 6], [-1, 6], [88, 38], [16, 19], [-10, 33], [5, 51], [-14, 56], [38, 27], [22, 34], [-53, -2], [23, 31], [-4, 21], [47, 29], [1, 19], [-50, 43], [42, 17], [-3, 16], [-52, 34], [-5, 105], [46, 41], [-37, 22], [8, 23], [-18, 43], [1, 42], [17, 14], [51, 8], [-1, 43], [16, 7], [34, -13], [17, 10], [9, 29], [-42, 50], [-41, 154], [-39, 12], [-15, 19], [22, 104], [2, 65], [13, 25], [-7, 33], [-55, 16], [0, 0], [43, 24], [-13, 12], [10, 25], [-78, 44], [-3, 18], [17, 15], [-7, 18], [-19, 10], [-63, 34], [0, 20], [52, 4], [0, 0], [24, 1], [-3, 13], [-93, 39], [-48, 52], [-56, -27], [-49, 46], [-1, 16], [65, 61], [3, 21], [-172, -15], [-42, 5], [4, 22], [1, 8], [12, 7], [-71, 26], [26, 26], [5, 27], [-52, 27], [-1, 27], [9, 25], [-32, 32], [-13, 43], [20, 45], [14, 9], [16, 10], [-8, 26], [114, 12], [116, -17], [59, 15], [1035, -17], [317, 15], [184, -8], [160, 18], [98, -4], [2, -60], [-41, -157], [-26, -164], [-5, -130], [25, -103], [-13, -98], [53, -71], [183, -64], [17, -23], [3, -88], [55, -44], [6, -17], [-48, -79], [22, -123], [15, -13], [21, -2], [51, 20], [35, 3], [111, -115], [109, -2], [97, -87], [29, 3], [19, -22], [14, 3], [29, 36], [43, 13], [36, 11], [0, 25], [24, 10], [80, -24], [21, 5], [10, 20], [-45, 70], [31, 17], [0, 26], [38, 5], [10, 15], [-39, 38], [24, 0], [22, 13], [15, 14], [1, 25], [16, 10], [36, 0], [111, -27], [89, 7], [38, 19], [14, 55], [27, 8], [31, -6], [46, -48], [54, -4], [-14, 27], [-55, 54], [84, 0], [18, 12], [-20, 41], [26, 23], [40, 8], [69, -39], [60, -1], [71, 23], [4, 20], [27, 12], [23, 38]], [[11020, 10149], [22, 0], [1, 20], [7, 27], [10, 19], [-15, 18], [-10, 10], [5, 25], [20, 16], [34, 11], [25, 2], [46, -7], [21, -12], [17, -6], [31, 4], [14, 11], [27, -1], [29, -10], [24, -9], [8, 0], [15, 6], [12, 10], [17, -8], [9, -15], [5, -13], [92, -34], [138, -51], [41, -15], [1, -1], [7, -7], [125, -125], [196, -146], [138, -36], [41, -10], [5, 0], [49, 1], [95, 2], [6, 0], [94, -5], [48, 38], [-5, 72], [58, 64], [18, 104], [73, 40], [28, 54], [157, -9], [47, 150], [229, -27], [173, -22], [86, 18], [38, 8], [0, 75], [108, 26], [147, 50], [77, -18], [52, -11], [-51, -34], [-18, -41], [-4, -8], [138, -73], [37, -17], [77, -35]], [[13935, 10224], [74, -33], [65, -205], [56, -209], [22, -106], [-58, -78], [-77, -75], [-43, -51], [-18, -95], [-30, -64], [-60, -39], [42, -119], [103, -62], [44, -120], [-61, -92], [6, -56], [64, -121], [53, -61], [65, -51], [65, -42], [-61, -149], [157, -111], [87, -10], [113, -77], [45, -57], [64, -128], [23, -107], [20, -154], [-159, -56], [-38, -50], [-74, -35], [-79, -90], [-47, -85], [-101, -64], [16, -108], [-8, -65], [-72, -105], [135, -37], [99, -3], [83, -30], [47, -74], [-50, -73], [-1, -97], [77, -85], [51, -150], [-34, -84], [-29, -58], [61, -67], [64, -74], [61, -58], [181, -77], [100, 19], [210, -30], [44, -57], [68, -96], [94, -61], [48, -78], [-42, -101], [11, -66], [88, -56], [21, -68], [-38, -50], [-16, -101], [69, -81], [-64, -161], [32, -90], [43, -59], [47, -182], [-8, -78], [53, -42], [64, -51], [-169, -59], [52, -83], [18, -56], [12, -86], [6, -75], [-10, -96], [8, -67], [6, -68], [33, -62], [56, -74], [21, -104], [-202, -65], [-100, -73], [-111, -6], [-211, 7], [-82, -9], [-93, -22], [-146, 12], [-84, -84], [91, -78], [75, -73], [19, -68], [-23, -69], [47, -92], [113, -56], [64, -63], [-56, -77], [-57, -137], [-142, -50], [-100, -17], [-160, 0], [-132, 28], [-132, 121], [-47, 115], [-139, 27], [-154, -23], [-82, 7], [-140, 40], [-120, 70], [-166, -2], [-195, -51], [-98, -8], [-119, -68], [-2, -92], [118, -173], [21, -126], [-49, -106], [-19, -79], [-47, -53], [-18, -86], [118, -85], [86, -50], [56, -82], [63, -92], [-14, -136], [-97, -27], [-127, -6], [-96, 25], [-118, 44], [-115, 0], [-153, 9], [-116, -2], [-45, -89], [38, -61], [-18, -95], [-45, -71], [-61, -114], [-71, -107], [-50, -66], [-13, -64], [-1, -132], [27, -89], [114, -149], [32, -106], [-14, -103], [10, -84], [-15, -143], [-44, -49], [-126, -23], [-110, 35], [-109, 112], [-67, 144], [-36, 55], [-106, 43], [-61, 50], [-42, 75], [-6, 83], [-93, 37], [-85, -3], [-77, -48], [-150, -92], [-123, 21], [17, -89], [56, -50], [-114, -81], [-74, -72], [-49, -107], [-42, -76], [-46, -85], [-62, -106], [-21, -102], [27, -88], [-86, -38], [-84, 89], [-18, 330], [-56, 116], [-82, 165], [-35, 97], [-80, 76], [-61, 41], [-103, 54], [-80, 50], [-37, 78], [-19, 123], [-19, 92], [0, 79], [-40, 56], [-73, 27], [-105, -24]], [[13063, 17794], [-52, -379], [-11, -85], [28, -144], [45, -229], [21, -326], [-26, -25], [-62, -59]], [[15583, 13481], [-73, 20], [-59, -25], [-63, -13], [-68, 10], [-104, -60], [-76, -26], [-71, -22], [-97, 36], [-105, 75], [-137, 44], [-91, -13], [-26, -51], [-57, -37], [-65, -71], [35, -71], [13, -78], [-25, -48], [-45, -65], [42, -73], [27, -85], [-93, -78], [-87, 45]], [[14358, 12895], [-64, 33], [-379, 192], [-205, 35], [-28, 35], [25, 32], [-47, 24], [-318, 101], [-255, 21], [-71, 43], [11, 252], [34, 49], [-20, 38], [-59, 43], [-105, -1], [-104, -24], [-88, 0], [-247, 100], [-118, 68], [-77, 20], [-19, 37], [-41, -2], [-21, 21], [-13, 14], [-26, -2], [-52, 52], [-284, 85], [-7, 45], [-74, -4], [-39, 18], [13, 171], [16, 16], [0, 35], [-35, 2], [16, 66], [-22, 17], [21, 77], [-52, 46], [37, 71], [-52, 96], [-30, 14], [-71, 7], [-32, 26], [-151, 2], [-35, 24], [-23, 55], [10, 58], [18, 23], [6, 49], [-38, 4], [-63, -14], [-69, 4], [-43, 7], [-16, 19], [-39, 19], [-47, -61], [-5, -1], [-25, -34], [-76, -20], [-77, -33], [-117, -4], [-62, 11], [-170, -108], [-49, -6], [-99, 8], [-51, 28], [-32, 30], [-24, 2], [-62, -18], [-18, 18], [1, 9], [-50, 5]], [[18806, 18838], [60, 7], [3, -19], [-1, -10], [8, -34], [-1, -7], [-19, -24], [1, -23], [-7, -14], [4, -6], [22, 2], [10, -5], [2, -5], [-6, -18], [0, -20], [-5, -9], [-13, -8], [-26, -4], [-8, -5], [-5, -14], [6, -13], [14, -6], [8, -9], [21, -18], [13, -6], [11, 7], [15, -1], [12, -2], [3, -5], [-4, -6], [-13, -3], [0, -10], [7, -6], [21, 2], [2, -7], [-14, -14], [-7, -8], [-4, -12], [-4, -12], [-1, -22], [-7, -4], [-10, 0], [-24, -4], [-16, -11], [-5, -6], [-1, -9], [6, -9], [6, -10], [-5, -6], [-7, -4], [-3, -8], [2, -9], [10, -9], [3, -6], [-4, -4], [-9, -2], [-12, 5], [-8, -2], [-7, -8], [6, -10], [19, -3], [13, -16], [25, -11], [3, -6], [-3, -8], [-9, -7], [-28, -13], [-9, -9], [-3, -9], [7, -3], [9, 0], [10, -6], [8, -12], [1, -5], [-3, -4], [-4, 1], [-5, 2], [-14, 0], [-19, 10], [-23, 26], [-11, 1], [-9, -7], [-18, -22], [-11, -32], [5, -10], [10, -12], [40, -21], [2, -9], [-4, -5], [-12, -4], [-12, -13], [0, -5], [18, -27], [0, -9], [-9, -23], [-16, -18], [1, -18], [11, -18], [11, -30], [-2, -9], [-10, -15], [-2, -11], [-1, -17], [-25, -7], [-4, -13], [6, -14], [11, -8], [21, -7], [8, -7], [-2, -4], [0, -9], [-6, -10], [-1, -6], [4, -9], [1, -13], [9, -30], [6, -29], [2, -26], [12, -21], [15, -48], [14, -23], [4, -13], [-3, -13], [7, -7], [11, -1], [29, 4], [10, -2], [7, -6], [4, -11], [-1, -16], [0, -17], [8, -10], [52, -26], [13, -7], [0, -7], [-1, -3], [-9, -13], [-9, -23], [4, -20], [-1, -9], [-5, -8], [-12, -5], [-14, 1], [-7, 7], [-9, 8], [-13, 3], [-16, -3], [-14, -15], [-1, -14], [-12, -16], [-1, -15], [-4, -19], [-6, -10], [-30, -16], [-13, -3], [-41, 3], [-37, -3], [-8, -5], [-5, -23], [8, -13], [20, -1], [24, 1], [17, -9], [12, -11], [-5, -5], [-49, -13], [-7, -12], [6, -4], [12, 0], [5, -3], [-1, -7], [2, -14], [0, -6], [-12, -22], [-7, -24], [1, -4], [-7, -10], [-6, -3], [-5, 1], [-15, 6], [-7, 16], [-10, 7], [-18, 4], [-11, -1], [-12, -10], [-2, -17], [-7, -14], [-17, -4], [-7, 3], [-14, 21], [-8, 7], [-11, 0], [-12, -7], [-26, -6], [-27, -10], [-23, 4], [-36, 1], [-18, -4], [-53, -26], [-43, -9], [-33, -4], [2, 4], [1, 5], [-9, 3], [-9, 0], [-18, -8], [-12, -12], [-23, -10], [-30, -9], [-10, -5], [-5, -7], [8, -10], [0, -13], [4, -7], [5, -12], [10, -19], [19, -12], [3, -12], [-4, -18], [-6, -17], [7, -8], [18, -6], [13, -9], [31, -44], [32, -23], [60, -17], [7, -2], [28, -6], [14, -10], [3, -13], [1, -3], [13, -19], [-84, 6], [-146, 3]], [[14358, 12895], [-18, -17], [-37, -7], [1, -23], [23, -19], [32, 3], [45, 11], [17, -10], [-3, -29], [11, -35], [49, -38], [19, -25], [41, -10], [65, 13], [58, -11], [3, -35], [-28, -15], [-9, -55], [38, -45], [40, -2], [10, -24], [-19, -50], [-59, -6], [-67, -16], [-3, -29], [49, -84], [48, -15], [6, -52], [-66, -53], [50, -27], [70, -25], [28, -47], [-22, -56], [3, -59], [11, -70], [72, -48], [58, -21], [-13, -22], [11, -30], [64, 0], [70, -16], [53, -88], [-109, -7], [-135, -4], [-40, 30], [-143, 31], [-64, -17], [-94, -105], [0, -31], [34, -19], [51, -86], [-80, -109], [-42, -48], [-21, -37], [26, -23], [-45, -72], [-83, -35], [-3, -86], [-31, -41], [-23, -113], [14, -35], [-42, -47], [-9, -38], [-68, -10], [0, -24], [46, -20], [19, -16], [-30, -38], [-129, -11], [-9, -32], [12, -28], [100, -9], [0, -33], [-40, -14], [-82, -57], [-74, -23], [-61, -33], [0, -39], [34, -35], [-17, -24], [3, -24], [29, -14], [-9, -41], [-9, -67], [0, 0]]], "transform": {"scale": [6.031475732823288e-05, 8.241726593563134e-05], "translate": [92.25672383937811, 21.944190125242905]}, "objects": {"districts": {"type": "GeometryCollection", "geometries": [{"arcs": [[0, 1, 2, 3, 4, 5, 6]], "type": "Polygon", "properties": {"district": "Aizawl", "dt_code": "261", "st_nm": "Mizoram", "st_code": "15", "year": "2011_c"}}, {"arcs": [[[-6, 7, 8]], [[9, 10, 11]]], "type": "MultiPolygon", "properties": {"district": "Champhai", "dt_code": "262", "st_nm": "Mizoram", "st_code": "15", "year": "2011_c"}}, {"arcs": [[-4, 12, 13]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON>", "dt_code": "263", "st_nm": "Mizoram", "st_code": "15", "year": "2011_c"}}, {"arcs": [[14, 15, 16]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON>", "dt_code": "264", "st_nm": "Mizoram", "st_code": "15", "year": "2011_c"}}, {"arcs": [[-2, 17, 18, 19, -17, 20, 21]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON>", "dt_code": "265", "st_nm": "Mizoram", "st_code": "15", "year": "2011_c"}}, {"arcs": [[-3, -22, 22, -13]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON>", "dt_code": "266", "st_nm": "Mizoram", "st_code": "15", "year": "2011_c"}}, {"arcs": [[-15, -20, 23, 24]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON>", "dt_code": "267", "st_nm": "Mizoram", "st_code": "15", "year": "2011_c"}}, {"arcs": [[-1, 25, -10, 26, 27, -18]], "type": "Polygon", "properties": {"district": "Serchhip", "dt_code": "268", "st_nm": "Mizoram", "st_code": "15", "year": "2011_c"}}, {"arcs": [[-7, -9, 28, -11, -26]], "type": "Polygon", "properties": {"district": "Khawzawl", "dt_code": "996", "st_nm": "Mizoram", "st_code": "15", "year": "2019"}}, {"arcs": [[-19, -28, 29, -24]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON><PERSON>", "dt_code": "994", "st_nm": "Mizoram", "st_code": "15", "year": "2019"}}]}}, "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:OGC:1.3:CRS84"}}}