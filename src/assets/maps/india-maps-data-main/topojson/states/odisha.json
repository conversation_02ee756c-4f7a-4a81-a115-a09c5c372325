{"type": "Topology", "arcs": [[[14461, 9148], [-13, 13], [21, 37], [-66, 57], [-6, 72], [0, 2], [-11, 2], [-273, 40], [-111, -82], [-187, 74], [-75, -51], [-90, 41], [31, 41], [-41, -1], [-26, 56], [-91, 35], [-5, 11], [28, 233], [6, 47], [3, 27], [-21, 22]], [[13534, 9824], [-6, 17], [67, -12], [23, 120], [23, -11], [5, 15], [3, 9], [1, 6], [-5, 7], [-47, 63], [10, 80], [-81, 62], [-23, 39], [13, 32], [38, 60], [56, -18], [2, -1], [49, 33], [35, 22], [20, 13], [1, 30], [4, 111], [0, 0], [2, 0], [32, 0], [0, 1]], [[13756, 10502], [39, -26], [104, -69], [8, 50], [35, -4], [27, -63], [-47, -59], [2, 0], [2, -1], [44, -21], [2, -4], [-34, -35], [47, -21], [18, 25], [164, -83], [42, 77], [88, -9], [-2, -46], [78, -80], [9, -59], [98, -12], [18, -23], [-28, -41], [73, -33], [4, -3], [-16, 56], [53, 10], [9, 37], [0, 3], [-48, 41], [57, 188], [60, -14], [32, 58], [191, -41], [-49, 81], [39, 22], [-4, 66], [41, -18], [46, 33], [107, -82], [49, 25], [4, 7]], [[15118, 10434], [4, 2], [-56, -176], [32, -194], [344, -716], [2, -116], [-26, -96], [-283, -25], [-149, -95], [-16, -54]], [[14970, 8964], [-22, 6], [-18, 5], [-29, 8], [-13, -12], [-40, -5], [-15, 11], [-5, 13], [29, 39], [-4, 9], [-6, 15], [-4, 9], [-5, -1], [-132, -22], [-12, 53], [-130, 60], [-35, -32], [0, 0], [-40, 28], [-3, 3], [-2, -3], [-23, 0]], [[11946, 10202], [-40, -8], [-44, -73], [56, -116], [70, -33], [12, -111], [-29, -143], [83, 36], [53, -16], [28, -47], [57, -14], [74, -131], [49, 24], [199, -14], [6, -83], [-58, -166], [152, -42], [13, 21], [32, -51], [168, -45], [-34, -132], [34, -75], [-19, -42], [0, 0], [-117, 29], [-61, -48], [-149, -18], [6, -34], [22, -10]], [[12509, 8860], [-16, -28], [1, -7], [-60, -96], [-107, 31], [-109, -26], [-64, 37], [-16, -28], [-52, -4], [-22, -79], [-60, 24], [-33, -12], [-26, -25], [-16, -23], [2, -11], [19, -18], [-12, -29], [-53, -28], [-93, -141], [-157, -37], [-39, -75], [-225, 70], [-130, -160], [-116, 76], [-25, 0], [7, -65], [-23, -2], [-49, 40], [-89, -10], [-155, 61], [-118, 5], [-110, 91], [-40, 58], [30, 27], [-81, 77], [-142, 46]], [[10330, 8599], [21, 78], [-58, 129], [74, 146], [34, 7], [20, -80], [41, -17], [-31, -81], [16, -42], [2, 1], [15, 12], [50, 37], [-2, 11], [-11, 57], [0, 2], [77, 158], [-56, 106], [163, 28], [8, 241], [-28, 30], [40, 29], [-49, 13], [-15, 40], [6, 166], [49, 40], [6, 6], [-8, 15], [-14, 27], [-14, 25], [-118, 74], [-100, 75], [-5, 22], [32, 55], [15, 17], [124, -27], [117, 314], [50, 17], [74, -69], [45, 59], [124, -30], [43, 49]], [[11067, 10339], [4, -5], [202, -181], [9, -8], [73, -15], [131, 159], [169, -94], [52, 75], [105, 17], [56, 59], [66, -14], [-5, -38], [62, -84], [-45, -8]], [[14461, 9148], [8, -8], [-18, -46], [-2, -8], [-2, 0], [-144, -2], [-65, -67], [-8, -55], [63, 11], [-41, -51], [27, -46], [-83, -56], [-33, 34], [-8, -37], [-65, -24], [-52, 30], [-46, -43], [-4, -85], [-67, -7], [-12, -62], [-61, -2], [-64, -93], [-129, 46], [-29, 66], [-14, -152], [-95, 1], [0, 0]], [[13517, 8492], [-8, 7], [-74, 67], [-116, 32], [-20, 51], [-49, -20], [-54, 37], [68, 4], [-4, 58], [-37, -42], [-80, -7], [-77, -87], [-83, 10], [-26, -69], [-100, -7], [-1, 2], [-45, 60], [-57, 77], [-33, 45], [-19, 26], [-10, 3], [-45, 15], [-21, 56], [-114, 54], [-3, -4]], [[11946, 10202], [79, 2], [12, 11], [47, 6], [1, 0], [33, -121], [13, 0], [78, 3], [42, -48], [177, -14], [3, 0], [2, 0], [80, 19], [33, 134], [37, -23], [91, 44], [144, -15], [26, 69], [79, -26], [54, 52], [-28, -48], [6, -73], [-65, -41], [-67, -136], [56, -154], [111, -6], [10, 105], [55, 3], [57, 66], [379, -143], [43, -44]], [[7987, 9538], [-8, -11], [-22, -17], [-1, -16], [-4, -80], [0, -1]], [[7952, 9413], [-87, 41], [-8, 0], [-5, 3], [-23, -5], [-65, -11], [-17, -4], [-235, -158], [-183, -52], [-79, 60], [-209, 5], [-112, -76], [-164, 25], [-143, -74], [-7, -3], [-56, -62], [-13, -193], [-116, -236], [45, -54], [-5, -42], [-128, -154], [-110, -68], [-117, -18]], [[6115, 8337], [-11, 15], [-2, 32], [26, 70], [-29, 32], [-3, 83], [74, 82], [18, 86], [37, 31], [-14, 26], [78, 76], [38, 107], [-69, 19], [7, 76], [-57, 2], [-2, 75], [-114, 12], [1, 65], [79, -16], [12, 32], [-157, 60], [18, 85], [120, 91], [-30, 85], [84, 156], [-178, -25], [-179, 19], [-55, 49], [-58, -1], [-63, 65], [29, 128]], [[5715, 9954], [5, 4], [97, -57], [-6, 66], [156, 45], [129, 95], [26, 63], [300, -28], [136, 72], [17, 37], [44, -10], [43, -116], [111, -62], [18, 18], [87, 194], [46, 40]], [[6924, 10315], [22, -1], [226, -135], [-5, -44], [29, -24], [-12, -95], [80, -81], [345, -57], [-7, -63], [50, -57], [-6, -60], [101, 6], [128, -165], [0, 0], [112, -1]], [[3452, 10048], [89, -76], [64, -133], [-17, -145], [31, -109], [-13, -107], [-36, -54], [9, -223], [40, -161], [-34, -86], [40, -11]], [[3625, 8943], [-18, -138], [2, 0], [69, -20], [34, -73], [-11, -46], [41, -26], [-37, -140], [-51, -45], [-14, -203], [-60, -132], [8, -11], [15, -19], [12, -16], [1, -62], [0, -1], [0, 0], [92, -131], [-47, -71], [11, -1], [27, -3], [125, -14], [88, 96], [32, -14], [-7, -53], [-62, -51], [-5, -70], [64, 9], [7, 96], [57, -68], [46, 4], [-15, -143], [-28, 14], [-5, 80], [-50, -12], [-15, -68], [1, 0], [40, -39], [9, -9], [46, 32], [1, -1], [27, -43], [-12, -101], [27, -36], [-40, -110], [-82, -20], [-1, -10], [-3, -50], [-1, -4], [13, -3]], [[3956, 7217], [5, -3], [-24, -193], [-70, 5], [-92, -149], [-42, -6], [14, -86], [-46, -4], [-1, -2], [-2, -3], [-29, -47], [1, -25], [2, -29]], [[3672, 6675], [-208, 50], [-90, -46], [-20, 0], [-143, 80], [-113, 1], [-84, 61], [-222, 62], [-2, 201], [-33, 70], [97, 182], [-31, 116], [32, 16], [-30, 52], [74, 89], [-19, 60], [-85, 85], [57, 160], [-12, 45], [59, 82], [-34, 34], [-20, 107], [-60, -68], [-20, 181], [-86, 34], [-73, 104], [52, 59], [-11, 65], [82, 92], [-22, 53], [15, 54], [-57, 103], [-12, 54], [-3, 44], [15, 9], [-30, 211], [-6, 89], [14, 109], [1, 12], [-17, 4], [31, 46], [38, -6], [3, -55], [112, -2], [41, -118], [107, -12], [4, 5], [12, 16], [61, 83], [1, 148], [199, 117], [192, 281], [-49, 108], [52, 13], [21, 33]], [[6115, 8337], [6, -8], [-6, -2], [-57, -18], [-30, -113], [36, 9], [95, -90], [6, -40], [122, -71], [5, -3]], [[6292, 8001], [-21, -22], [-7, -48], [-13, -30]], [[6251, 7901], [0, 0], [0, -2], [3, -62], [0, -2], [-69, 2], [0, -2], [-2, 0], [-3, -11], [-17, -38], [-118, 37], [-18, 74], [-42, 38], [-153, 51], [-26, -34], [21, 90], [-31, 59], [-1, -1], [-176, -155], [-39, -107], [-58, -65], [-64, -11], [-7, -79], [-170, -38], [-42, -77], [-130, -71], [4, -83], [-39, -78], [-98, -86], [-124, -21], [-50, 78], [-69, -9], [-29, 26], [-84, -44], [-73, 2], [-51, -46], [-248, 26], [-42, -38], [-4, -63], [-246, 56]], [[3625, 8943], [20, -4], [74, -15], [32, 145], [70, 97], [323, 327], [81, -25], [22, -51], [114, 31], [37, -145], [61, 9], [33, 40], [50, -27], [150, 15], [43, 52], [77, -52], [40, 71], [129, 46], [70, 105], [96, -42], [3, -59], [68, -89], [6, -81], [15, 35], [93, 21], [29, 64], [60, -24], [150, 49], [30, 56], [-99, 100], [33, 72], [-15, 78], [-70, 44], [-79, -69], [-16, 43], [-86, 58], [73, 130], [0, 1], [56, 0], [27, 45], [73, -32], [37, 28], [91, -121], [89, 85]], [[7952, 9413], [0, -1], [100, -67], [125, -83], [9, -20], [-6, -134], [0, -1], [2, -3], [29, -45], [26, -14], [119, -65], [13, -6], [3, -1], [122, -8], [147, -102], [124, -37], [217, -204], [339, -48], [13, -14]], [[9334, 8560], [65, -68], [-9, -94], [-57, -58], [-71, 28], [-77, -53], [-4, -52], [-40, -28], [-11, -84], [-33, -31], [29, -30], [-29, -54], [-94, -28], [-79, 33], [-39, -44], [-39, 11], [-73, -38], [-58, 23], [-90, -72]], [[8625, 7921], [-78, -34], [-50, 42], [3, 134], [0, 13], [0, 1], [-26, 62], [-82, 59], [22, 72], [-126, 66], [-19, 86], [36, 54], [-40, 38], [-30, 191], [-33, 62], [-102, 50], [-5, 4], [-2, -1], [-113, -49], [11, -113], [-24, 2], [-42, -105], [-87, -61], [-109, 7], [-27, 46], [-86, -65], [-111, 42], [-123, -6], [-153, -65], [-70, 53], [-115, -5], [-23, 37], [31, 26], [-54, 17], [-24, -95], [-108, 9], [-89, -114], [-60, -2], [-1, -69], [-65, -40], [43, -141], [47, -33], [-42, 8], [4, -57], [-23, -15], [-56, 33], [-16, -107], [-168, 86], [-75, -65], [-44, 46], [-4, 4], [-11, -13], [-14, -15]], [[13517, 8492], [0, 0], [-21, -52], [-18, -3], [-6, -9], [19, -5], [-60, -62], [1, -7], [75, -55], [-23, -31], [26, -37], [-42, -33], [32, -94], [-26, -11], [29, -14], [-18, -65], [25, -9], [-34, -52], [21, -54], [-32, -3], [-11, -37], [-16, 15], [-8, -38], [-2, -8]], [[13428, 7828], [-31, -1], [-69, -3], [-36, -1], [-12, 19], [-83, 30], [-75, -68], [-92, 52], [-40, -36], [-1, -1], [-171, 122], [-2, 1], [-43, -75], [78, -276], [21, -26], [55, -39], [30, -67], [202, -94], [-33, -57], [81, -84], [-60, 15], [-13, -88], [63, -92], [-29, -70], [80, -106], [-5, -8]], [[13243, 6875], [-28, -45], [-126, -6], [-24, 30], [0, 0], [2, 3], [17, 34], [-25, 15], [-13, 7], [-63, -25], [-32, 59], [6, 5], [30, 21], [-2, 11], [-2, 11], [-4, 23], [-13, 11]], [[12966, 7029], [-101, 83], [-31, 26], [-4, 3], [3, 0], [32, 0], [25, 71], [-44, 47], [-44, -36], [17, 45], [-29, 12], [-36, 118], [-40, 4], [-110, 139], [-52, -1], [20, 36], [-78, 113], [15, 25], [33, -44], [-2, 50], [-75, 74], [-6, 56], [-51, -45], [-36, 131], [-116, 26], [-1, 0], [-20, 69], [-49, 5], [-37, 4], [0, -68], [-37, -23], [-2, -1], [-10, 37], [-142, -36], [-108, -245], [-34, 23], [-60, -19], [-54, -102], [-161, 22], [-41, 41], [-5, -34], [-67, 12], [-38, -45], [-142, 19], [-22, -101], [-88, 28], [-46, -38], [-12, -10], [-5, 33], [-41, 56], [-55, -32], [-20, -12], [-56, 17], [-12, 1]], [[10891, 7563], [67, 72], [-11, 101], [62, 52], [-17, 47], [-263, -23], [-207, 65], [-105, -7], [-579, 338], [-196, 41], [-82, 80]], [[9560, 8329], [136, 166], [126, -1], [101, -51], [22, 46], [110, 29], [77, -37], [51, 11], [47, 115], [84, -19], [1, -1], [0, 2], [15, 10]], [[8625, 7921], [29, -43], [-6, -37], [-97, -65], [28, -44], [51, 13], [8, -57], [78, 12], [25, -120], [-27, -102], [4, -6], [119, -228]], [[8837, 7244], [1, -3], [-80, -49], [7, -56], [-96, 15], [-8, 26], [-39, -49], [-61, 13], [-92, 80], [14, 59], [2, 6], [0, 0], [-3, 5], [-19, 31], [-2, 0], [-43, 2], [-1, 0], [19, -165], [-103, -194], [38, -104], [-151, -73], [-16, 22], [-97, -131], [-1, 0], [37, -37], [56, 35], [94, -103], [41, 20], [163, -28], [-6, -295], [-48, -38], [0, -28], [-24, 4], [-11, 9], [-52, 2], [-94, 16], [-56, 54], [-94, 27], [-50, 112], [-88, -89], [-247, -59], [41, -77], [-72, -37], [-24, -55], [22, -37], [-118, -40], [81, -128], [-19, -42], [79, 39], [55, -11], [-34, -86], [28, -58], [110, -67], [23, -63], [-64, -54], [31, -98], [-4, -67]], [[7862, 5400], [-51, 36], [-29, -16], [5, 38], [-9, 3], [-140, -30], [-63, 143], [-67, -21], [-63, 30], [-110, -17], [-44, 29], [-49, -55], [-17, 30], [-102, -15], [-32, 38], [-41, -3]], [[7050, 5590], [-45, 87], [34, 24], [-36, 19], [-33, -4], [-106, -14], [-52, 43], [-25, 72], [-34, -7], [-50, 42], [-64, -30], [-69, 16], [-9, -121], [-58, -66], [-37, -1], [-17, 52], [-76, 59], [-131, -101], [-36, 41], [25, 77], [-32, 105], [-18, 43], [-49, 17], [37, 77], [-6, 120], [-34, 5], [-19, 56], [-40, -35], [-159, 98], [0, 2], [-3, 68], [91, 160], [-166, 87], [-11, 6]], [[5822, 6587], [-2, 1], [15, 102], [63, 70], [53, 12], [33, 102], [39, 8], [10, 89], [44, 42], [-10, 46], [142, -82], [111, 72], [96, -29], [14, 85], [30, -12], [17, 68], [82, 51], [31, -14], [45, 59], [-20, 104], [-141, -9], [24, 38], [-59, 46], [-4, 163], [-47, 189], [14, 50], [-144, 51], [1, 8], [-8, 4]], [[9334, 8560], [226, -231]], [[10891, 7563], [-3, -4], [-13, -58], [46, -10], [-2, -7], [-1, -4], [-4, -15], [-34, -5], [-10, -46], [-182, -10], [-6, -1], [-24, -47], [-2, -3], [-2, 2], [-33, 27], [-13, 10], [-14, -5], [-47, -71], [-15, -29], [2, -12], [34, -43], [-28, -45], [25, -75], [64, -38], [23, -1], [76, 92], [1, 0], [112, 19], [2, 45], [2, 2], [31, 24], [8, 6], [0, 0], [34, -27], [19, 50], [20, 0], [13, 0], [8, 0], [5, -11], [14, -31], [4, -9], [-40, -83], [83, 38], [6, -59], [71, -29], [7, -2], [-41, -111], [-46, 4], [-11, -65], [55, -109], [40, -6], [1, -26], [57, 12], [-58, -106], [62, 7], [36, -39], [13, -40], [-54, -95], [-14, 0], [-34, -48], [-94, 50], [-3, 36], [-62, 14], [-90, -94], [-234, -61], [-90, 19], [-130, -23], [-5, -1], [-107, 90], [-268, -100], [-225, -11], [-13, -1]], [[9813, 6394], [-33, 34], [-57, -1], [37, 108], [0, 0], [-10, 27], [-44, 122], [-1, 0], [-106, 27], [-15, 91], [-125, 112], [-37, 73], [21, 71], [45, 18], [26, 297], [-1, 1], [-183, 91], [-81, -7], [-86, 106], [-14, -3], [-74, -11], [-2, 0], [0, -1], [0, 0], [-67, -218], [-138, -81], [-36, 31], [5, -37]], [[12966, 7029], [1, -2], [-15, -1], [-12, 11], [-132, -22], [-52, 44], [-16, -22], [-109, 46], [-20, -29], [-13, 21], [-64, -19], [-55, 39], [-15, -48], [-47, 4], [-28, 37], [87, 87], [-45, 97], [-98, 62], [-26, -18], [40, -22], [-2, -49], [-33, 3], [-19, 49], [-33, -100], [-54, 7], [-16, -34], [-101, 56], [3, -46], [-33, -21], [18, -21], [-85, 30], [-22, -24], [12, -42], [-16, 46], [-43, -2], [-36, 49], [-44, -105], [49, -88], [-58, -24], [22, -61], [-81, -2], [-2, 92], [-48, 17], [33, -64], [-69, -40], [-18, -95], [51, -48], [-47, -90], [-20, 9], [22, 73], [-64, -69], [-54, 88], [-4, -84], [-23, -8], [24, -48], [-43, -17], [31, -6], [-7, -55], [-37, -6], [5, 31], [-57, -13], [-15, 61], [-56, -79], [41, -40], [-37, -37], [53, 50], [18, -52], [103, -36], [-17, -191], [-171, 47], [1, 50], [-178, -4], [-143, -159], [-75, -28], [-83, -114], [-212, -54], [-8, -72], [-79, -6], [-4, -6], [-12, -16], [-35, -49], [-7, -30], [-3, -12], [-1, -5], [39, -53], [-84, -43], [0, 0]], [[10483, 5704], [0, 1], [1, 17], [-124, 187], [-149, 96], [-32, 74], [-76, 36], [-112, -5], [-56, 48], [-41, 105], [-95, 63], [14, 68], [0, 0]], [[5822, 6587], [-33, 10], [-91, 26], [-24, -118], [-65, 2], [-40, -41], [73, -153], [-24, -65], [47, -51], [-20, -22], [14, -74], [63, -16], [-25, -37], [10, -146], [-72, -125], [-122, -87], [-40, -147], [10, -69], [-42, 29], [-106, -45], [-88, 15], [1, -26], [-60, 39], [9, -74], [-46, -22], [36, -34], [-6, -34], [-43, -26], [25, -35], [-32, -72], [-48, 4], [-64, -97], [-12, -43], [51, -53], [-9, -38], [-40, 27], [-15, 43], [-21, 25], [-8, -13], [-5, -61], [4, -42], [-8, 2], [-36, 9], [-58, 122], [27, 50], [-5, 8], [-58, 67], [-36, -31], [-42, 34], [1, 0], [18, 18], [-32, 30], [15, 43], [-55, -4], [-48, 75], [-79, -30], [-45, -130], [-1, -4], [0, 0], [-72, -32], [-6, -93], [-9, -9], [-27, -29], [-14, -16], [-1, 0], [-121, 6], [-34, -34], [5, -13], [38, -101], [49, -34], [-29, -88], [23, -52], [-43, -123], [-101, -71], [46, -138], [-94, -63], [28, -21], [-19, -31], [-1, -1], [-2, 0], [-45, -17]], [[4093, 4240], [-15, 29], [-6, 9], [-4, -8], [-13, 3]], [[4055, 4273], [-17, 3], [-39, 92], [-104, -2], [72, 62], [4, 80], [42, 79], [33, -1], [-14, 49], [-5, 1], [-66, -6], [-30, 17], [-88, 47], [-63, -50], [-43, 70], [-43, -14], [-38, 57], [-10, 171], [-52, 3], [-20, 52], [-23, -10], [-67, 78], [-2, 55], [-44, 14], [37, 79], [-109, 40], [25, 55], [-138, 18], [-22, 115], [-80, 9], [0, 0], [50, 83], [-63, 62], [86, 40], [59, 109], [-26, 11], [-1, 1], [16, 90], [-26, 40], [-2, 3], [108, 58], [-20, 87], [-1, 0]], [[3331, 6020], [3, 5], [-1, 5], [6, 3], [82, 43], [70, 118], [123, -4], [40, 28], [37, 109], [-29, 7], [-19, 75], [36, 141], [-7, 125]], [[13428, 7828], [58, 24], [11, -47], [101, 13], [18, -19], [4, -4], [51, 50], [54, -51], [10, -10], [2, 15], [16, 7], [66, -11], [12, 35], [161, -108], [58, 20], [71, -26], [59, 40], [78, -52], [154, 51], [166, -37], [92, -98], [71, 5], [148, 110], [0, 3]], [[14889, 7738], [4, 3], [17, -13], [-289, -209], [-29, 36], [30, -49], [-234, -108], [-173, -132], [-159, -261], [-118, -136], [-66, -176], [-99, -105], [-8, 64]], [[13765, 6652], [-79, 14], [-52, 100], [-146, 70], [23, 41], [-63, -4], [-28, -57], [-41, 25], [-62, -19], [-20, 47], [-1, 1], [-1, -1], [-45, -4], [-7, 10]], [[13765, 6652], [8, -72], [106, 51], [-64, -60], [-223, -42], [-219, -122], [-471, -157], [-476, -115], [-570, -208], [-901, -479], [-499, -373]], [[10456, 5075], [-104, 166], [108, 112], [23, 351]], [[4055, 4273], [0, 0], [-7, -38], [-31, 22], [-27, -34], [-28, -128], [-15, 69], [-39, -58], [-31, 12], [-10, 55], [-75, -26], [-32, 26], [-37, -49], [-37, 12], [6, 40], [-65, 51], [-44, -15], [-17, 35], [-30, -41], [-41, 15], [-46, -41], [0, -60], [-6, 4], [-5, 15], [-14, -2], [-5, -1], [-7, -2], [5, -19], [0, -7], [-19, 14], [-7, 6], [-1, 71], [-29, -1], [-48, 65], [-23, -23], [-14, 50], [-3, -6], [-13, -34], [-140, 13], [-81, -68], [-79, 13], [-55, -41], [-79, 44], [-27, -62], [-136, 58], [-34, -50], [-80, 28], [-71, -89], [-29, 26], [-46, -44], [-80, 5], [16, -67], [-64, -12], [-18, 9]], [[2257, 4013], [0, 10], [-57, 19], [-6, 2], [-1, 1], [-3, -5], [-25, -26], [-12, 24], [-4, 12], [25, 17], [-5, 15], [19, 22], [-31, 32], [24, -16], [22, 32], [-28, 18], [12, 58], [-52, 34], [20, 33], [-30, 45], [1, 115], [63, 179], [36, 8], [-43, 53], [-82, 29], [-5, 38], [17, 36], [71, 6], [29, 149], [-50, 30], [-125, -25], [-65, 114], [15, 86], [-28, 14], [6, 54], [-67, 22], [-28, -39], [-101, -10], [0, 0], [2, 6], [51, 106], [2, 39], [-25, 125], [52, 94], [-38, 241], [-1, 6], [57, 251], [-219, 32], [-57, 110], [7, 70], [-216, 104], [-56, 27], [-32, 39], [-13, 16], [-3, 4], [13, 79], [-33, 13], [5, 19], [79, 282], [2, 7], [53, -12], [140, 173], [1, 2], [135, -159], [62, -14], [11, -77], [30, -25], [105, 127], [156, -129], [123, -29], [36, -65], [71, 6], [76, 53], [87, -87], [97, -264], [114, -163], [43, 82], [36, -7], [58, 85], [141, 68], [50, -29], [92, 10], [93, -79], [11, 26], [37, -34], [143, -25], [9, -2], [-5, -7], [-71, -96], [16, -154], [35, -24]], [[7050, 5590], [-42, -27], [35, -204], [7, -16], [108, -71], [-55, -73], [48, -67], [-96, -202], [15, -72], [84, -40], [26, 58], [8, -42], [70, -36], [37, -91], [-40, -125], [16, -49], [-111, -238], [49, -60], [-26, -21], [2, -91], [124, -76], [-50, -43], [-6, -56], [51, -127], [-63, -4], [-16, 39], [-71, 7], [-36, -29], [-4, -61], [62, -64], [-84, -64], [-14, -11], [-117, 68], [-46, -77], [-93, -11], [-27, 44], [-40, -5], [-2, 3], [-1, -1], [-7, 10], [-57, 72], [-21, -21], [-19, -20], [-4, 1]], [[6644, 3697], [-48, 1], [12, -28], [-87, -34], [-6, -13], [29, -130], [29, 13], [41, -65], [8, -42], [0, -4], [2, -8], [-21, -13], [-3, 1], [-3, -2], [-67, 42], [12, -47], [-64, 236], [-65, 44], [57, 61], [-83, 10], [-41, 102], [-67, 69], [5, 55], [-43, 60], [21, 34], [-51, 10], [3, 62], [-35, 31], [-17, -63], [-60, -35], [32, -105], [-69, -29], [-3, -88], [-58, 7], [-4, 79], [-36, -16], [23, -75], [-43, -45], [-13, -95], [-94, 28], [-13, 45], [51, -21], [12, 32], [-21, 96], [-35, -23], [-50, 48], [7, 42], [-48, 6], [29, -42], [-34, 2], [-11, -50], [58, -93], [-10, -76], [-82, 4], [15, -57], [34, -1], [-16, -111], [-40, 7], [-18, 56], [-64, 42], [-3, 39], [-19, 10], [-30, 12], [-73, -20], [-13, 42], [-51, 4]], [[5412, 3698], [42, 90], [-61, 40], [1, 127], [-38, -6], [-79, 69], [-30, -18], [-8, 8], [-9, 7], [-6, 5], [-55, -171], [24, -40], [-10, -23], [-11, -26], [-72, -37], [7, -114], [-100, -89], [-43, 2], [31, 93], [-33, 69], [30, 191], [-28, 15], [12, 47], [-30, 6], [74, 105], [42, 18], [12, -48], [54, 16], [-10, 53], [45, 57], [-84, 29], [-19, -37], [-60, 24], [8, -64], [-71, -9], [-38, -72], [-72, 45], [-17, -132], [-26, -11], [-53, 66], [-75, -92], [-24, -114], [-120, 53], [19, 77], [-53, 28], [-12, 77], [-68, 54], [-4, 99], [0, 6], [-66, -51], [-97, 20], [-51, -22], [-10, 58], [-75, 62], [-2, 2]], [[5412, 3698], [-29, -63], [-78, 6], [23, -79], [44, -14], [12, -49], [-4, -6], [-15, -24], [-15, -24], [6, -9], [59, -20], [5, 120], [1, 0], [22, -2], [18, -2], [0, -3], [12, -60], [-52, 2], [13, -56], [21, -35], [73, -14], [-4, -4], [-32, -33], [101, -72], [-25, -96], [-73, -35], [-38, 37], [-103, -77], [7, -36], [-61, 10], [-67, -36], [-36, -102], [-129, 31], [3, -23], [6, -31], [-1, -3], [-42, -78], [-17, 64], [-79, 60], [-59, -8], [-51, 51], [-63, -52], [19, -32], [-136, -159], [-41, -124], [-113, -54], [7, -39], [-52, -44], [0, -40], [40, -8], [-5, -33], [34, -17], [75, 32], [0, -34], [-64, -44], [43, -90], [51, -10], [67, 37], [19, -20], [-110, -187], [-94, -90], [55, -48], [4, -50], [50, 35], [19, -17], [-28, -33], [52, -48], [8, -4], [-61, -63], [-4, -4], [-92, 33], [-70, -19], [-73, -78], [-84, 2], [-122, 62], [3, 130], [-122, -9], [-14, 39], [-45, 22], [-49, 24], [-14, 8], [-6, -5], [-67, -56], [13, -62], [35, -12], [-16, -63], [-31, -13], [22, -57], [-45, -70], [-297, -130], [-66, 12], [26, -64], [-27, -53], [28, -42], [-41, -41], [-138, 120], [30, 87], [-48, 33], [38, 42], [38, -5], [-14, 75], [53, 23], [-42, 19], [-31, 71], [-163, 53], [0, 2], [21, 36], [37, 68], [11, 20], [2, 3], [-57, 30], [-6, -42], [-53, 65], [41, 37], [-6, 101], [0, 1], [0, 0], [-80, 9], [-50, 44], [4, 67], [-3, -2], [-131, -103], [-27, -92], [0, 0], [0, 0]], [[2872, 2065], [-112, 42], [-1, 6], [-12, 64], [55, 11], [32, 113], [-78, 63], [-3, 24], [64, 34], [-38, 59], [-26, 2], [-58, 3], [-2, 0], [-141, -146], [-69, 24], [-57, 19], [-3, 2], [-3, 4], [-74, 92], [-14, 16], [-10, 13], [5, 12], [-24, 113], [-142, -102], [-29, 95], [-40, -14], [-42, 43], [35, 46], [-37, 85], [-106, 51]], [[1942, 2839], [-1, 1], [-5, 42], [-2, 24], [5, 1], [118, 15], [0, 46], [71, 24], [27, 147], [-23, 131], [1, 1], [44, 25], [-2, 47], [12, 7], [105, 54], [76, -10], [-36, 303], [-85, 149], [42, 89], [-34, 5], [2, 73]], [[2872, 2065], [-182, -195], [43, -55], [16, -111], [-128, -123], [-3, -2], [49, -33], [92, 38], [9, -93], [-44, -53], [-88, -1], [-22, -122], [-34, -29], [35, -21], [-72, -62], [13, -62], [104, -38], [53, -72], [-25, -41], [-68, 37], [-8, -126], [44, -89], [-22, -57], [-27, -21], [-33, 26], [-6, -46], [-59, 32], [9, 43], [-81, -37], [-30, -47], [32, -59], [-11, -74], [-50, -19], [-125, 81], [-76, 6], [16, 25], [-43, 71], [-87, -25], [-10, 49], [-87, 1], [-70, 46], [-41, -58], [-59, 3], [-21, 38], [-2, -1], [-15, -8], [-50, -27], [0, -2], [4, -80], [-3, -1], [-82, -50], [6, -8], [-2, -1], [-5, 9], [-50, -45], [-108, -13], [-46, -45], [-261, -76], [-1, -1], [-58, -94], [-199, -154], [-13, -5], [-14, 42], [-16, 44], [-1, 2], [-34, -50], [-54, -11], [-16, -4], [-53, -115], [-4, -8], [-67, -56], [-119, 48], [-30, -36], [-197, -44], [-44, 79], [-84, -40], [-28, 22], [-52, -57], [-47, 19], [-6, 3], [-1, -8], [-15, -10], [-7, 42], [-1, 3], [41, 71], [-27, 50], [16, 69], [1, 5], [123, -24], [12, 142], [93, 137], [-5, 229], [89, 149], [1, 121], [34, 43], [-52, 101], [65, 166], [-14, 54], [107, 53], [92, 103], [179, 38], [-35, 48], [26, 30], [252, 21], [53, 200], [236, 219], [37, 107], [113, 39], [-21, 33], [26, 68], [110, -2], [36, 63], [-99, 90], [-92, 28], [-2, 87], [15, 9], [4, 3], [58, -56], [75, 2], [56, 91], [-23, 47], [26, 29], [168, 79], [149, -16], [19, 60]], [[9626, 10304], [7, 19], [1, 3], [4, -19], [-3, -1], [-9, -2]], [[7987, 9538], [62, 43], [27, -13], [48, 60], [3, 0], [100, 7], [18, 2], [5, 0], [86, 68], [23, 82], [56, 36], [-69, 47], [56, 30], [26, -22], [118, 146], [140, 21], [147, -81], [166, 69], [94, -63], [-33, 93], [36, 165], [118, 4], [22, 63], [37, 1]], [[9273, 10296], [5, -4], [135, -107], [25, 10], [13, 15], [-29, 74], [2, 1], [-1, 2], [52, 15], [163, -21], [116, -73], [37, 39], [-11, 84], [62, -31], [-4, -70], [37, -2], [21, 128], [-84, 22], [-38, 115], [-78, 84], [164, 113], [87, -33], [63, 31], [1, 1], [16, -4], [43, -9], [-68, 93], [-2, 171], [-44, 101], [16, 72], [3, 1], [76, 36], [-14, 32], [108, -49], [54, 51], [206, 12], [135, 179], [24, 108], [-21, 34], [-45, 63], [-121, 22], [0, 0]], [[10377, 11602], [1, 0], [0, 0], [97, 97], [39, 38], [2, 2], [-1, 0], [-38, 15], [24, 39], [6, 11]], [[10507, 11804], [0, -1], [29, 0], [22, 45], [126, -4], [37, -159], [70, -70], [-30, -20], [9, -26], [72, -47], [-34, -47], [1, -15], [6, -54], [51, 10], [3, -8], [33, -154], [0, -2], [-71, -59], [-7, -46], [-118, 32], [-22, -18], [48, -72], [87, -36], [3, -61], [59, -18], [45, -75], [-47, -140], [-29, 30], [-85, -98], [-32, 13], [-3, 1], [-3, -69], [-1, -18], [0, -1], [45, -31], [30, 36], [173, -95], [64, -126], [28, -62], [0, 0], [1, 0]], [[14970, 8964], [37, -9], [25, 72], [90, 59], [114, -38], [331, 24], [53, -35], [-4, -51], [44, -84], [-738, -555], [-64, -92], [-37, 43], [16, -73], [-28, -65], [-68, 22], [36, -58], [-15, -153], [-58, -47], [41, -55], [32, 58], [21, -56], [71, 45], [36, -34], [25, 156], [30, 33], [-22, 153], [16, -20], [28, -111], [-52, -195], [16, -29], [0, 56], [20, -16], [-19, 29], [21, -5], [13, -74], [-93, -90], [1, -31]], [[8188, 3988], [11, -1], [-2, -3], [-3, -4], [-2, -2], [-1, 1], [-3, 9]], [[8239, 4006], [-1, -7], [-5, 7], [-1, 3], [7, -3]], [[10456, 5075], [-196, -173], [-62, -110], [-270, -179], [-284, -262], [-188, -226], [-126, -247], [-178, 128], [-8, 81], [29, 7], [4, 36], [-104, 22], [-10, -5], [-9, -104], [-199, 8], [39, -97], [44, -6], [0, -43], [93, 29], [33, -55], [-27, -41], [-119, 9], [7, -30], [-39, -56], [-27, -37], [-7, -11], [-15, 18], [-19, 23], [-13, 15], [15, 99], [-27, 3], [-45, -53], [-111, -25], [-93, -98], [-19, -83], [-151, 100], [29, -83], [56, -53], [-59, -26], [-17, -63]], [[8383, 3487], [-31, -33], [-48, 45], [46, 36], [-9, 23], [-58, -12], [-5, -1], [39, 52], [-14, 48], [-94, -26], [32, 57], [-48, -8], [29, 137], [-36, 45], [19, 29], [-27, 4], [107, 133], [-30, 42], [34, 19], [-65, 59], [10, 57], [-24, 26], [107, 40], [-5, 39], [80, 144], [-17, 57], [22, 62], [-33, 86], [14, 63], [52, 46], [-127, 26], [-39, 40], [30, 44], [-55, 33], [5, 51], [142, 43], [-53, 51], [-24, -16], [-1, 56], [-59, 54], [36, 83], [-10, 96], [53, 29], [-4, 61], [-180, 57], [-37, 40], [-8, -88], [-56, -11], [1, -4], [28, -5], [3, -1], [2, 0], [-24, -14], [-86, -49], [-5, -3], [-22, 92], [-26, -61], [-12, 17], [-3, -9], [-9, 26], [-21, 29], [-7, -23]], [[8383, 3487], [-1, -3], [3, -7], [23, -48], [9, -20], [-18, -35], [-4, -6], [-5, 3], [-22, 22], [-1, -14], [-1, -32], [-1, -15], [-108, 20], [51, -42], [-14, -44], [-89, 55], [67, -61], [-28, -39], [-33, -2], [-65, 84], [-17, -20], [7, -70], [82, -62], [-42, -57], [-52, -8], [38, -48], [-47, -9], [-18, -38], [-89, 52], [-6, -35], [-72, -29], [-95, 48], [-22, -26], [-63, 5], [-15, -43], [-85, 31], [-52, -33], [-8, -48], [-42, 20], [-86, -32], [-146, 180], [-91, -23], [-110, 18], [-43, -29], [-95, 70], [-62, -41], [-102, 133], [-17, 23], [-6, 144], [-53, 8], [-14, 135], [-9, 21], [-16, 36], [-48, 108], [-6, 13]], [[15785, 12426], [21, -24], [192, -131], [21, -66], [-30, 60], [-3, -71], [-26, 0], [-14, -52], [-79, 23], [-17, -21], [-20, 53], [-37, -16], [-5, -38], [56, -53], [-77, -21], [4, -44], [-31, 15], [25, -37], [-19, 12], [1, -43], [-48, -25], [-65, 19], [0, 79], [-58, 10], [31, -137], [42, -46], [1, -2], [-54, -27], [-5, -39], [-41, 40], [-40, 0], [-30, -9], [-3, -49], [0, -6], [62, -13], [-22, -70], [-30, 29], [-19, -22], [55, -85], [-2, -1], [-124, -60], [7, 40], [-22, 6], [6, -68], [-101, 54], [-29, -32], [-25, 96], [-27, 1], [-44, 79], [-6, -49], [37, -30], [-61, -66], [-22, 38], [-36, -13], [-37, 30], [-85, -4], [-13, -30], [-152, 24], [-129, -63], [-28, 36], [-52, -57], [-43, 8], [-31, -145], [-76, -46], [-77, 11], [-144, -293], [-2, -3], [-6, -6], [-192, -179], [-77, -72], [-53, -50], [-16, -15], [-122, -58], [-1, 4]], [[13738, 10706], [-2, 12], [-4, 22], [-245, 200], [-5, -57], [-113, -116], [-49, -131], [-53, -29], [-87, 62], [-55, -34], [-2, -47], [-59, 25], [0, 0], [-22, 94], [-47, 25], [43, 42], [0, 76], [51, 27], [-9, 40], [45, 89], [73, 69], [-93, 146], [-339, 192], [35, 48], [-9, 34], [85, 51], [89, 159], [-20, 98], [30, 94], [-69, 4], [-22, 75], [-39, -77], [-30, 9], [4, 89], [-33, 81], [-133, -34], [-82, 71], [-100, 11], [-75, 89], [-174, 47], [-13, 235], [-48, 50], [16, 146], [-127, 80], [-26, 47], [-90, -24], [0, -1], [-23, 22], [14, 39], [-92, 28], [-19, 61], [41, 71], [11, 3]], [[11867, 13019], [183, 50], [164, 128], [3, 2], [36, -29], [-21, -35], [55, -6], [9, -41], [-56, -19], [-62, 22], [-51, -77], [17, -74], [-61, -63], [13, -49], [50, -32], [93, 3], [22, -32], [196, 27], [35, -28], [24, 106], [35, 43], [36, -9], [181, 293], [-35, 51], [96, 176], [-159, 176], [44, 22], [-4, 39], [80, 92], [75, 35], [-79, 12], [37, 67], [-86, 22], [74, 99], [5, 57], [-24, 103], [-78, 25], [-9, 3], [-17, 19], [-32, 37], [-30, 33], [-2, 2], [0, 0], [1, 2], [14, 61], [61, 92], [121, 27], [15, 32], [-27, 31], [61, 68], [20, -11], [37, -19], [0, 0], [6, -3], [137, -212], [0, -1], [247, -40], [50, -63], [120, -33], [39, 17], [109, -193], [101, -111], [194, -98], [8, -31], [33, -2], [6, 67], [2, -1], [45, -7], [90, 66], [1, 2], [1, -2], [36, -60], [53, -13], [14, -67], [18, 22], [39, -30], [76, 10], [1, -4], [36, -70], [47, 15], [93, -45], [0, 0], [10, -42], [14, -53], [13, 4], [30, 13], [2, -4], [3, 1], [36, -57], [115, -17], [0, -1], [-1, -3], [0, 0], [0, 0], [-14, -212], [202, 32], [26, -21], [-6, -61], [33, -14], [-14, -21], [54, -4], [26, -62], [351, -36], [77, -95], [98, -52], [7, -161], [-79, -221], [5, -9], [90, -125], [6, -5], [3, -2], [164, -6], [5, -5]], [[10377, 11602], [-184, 26], [-80, -39], [-52, 113], [-9, 137], [-78, 29], [-63, -28], [-52, 35], [-8, -59], [-34, -17], [-74, 34], [-37, -36], [-111, -21], [-16, 26], [-180, 25], [-23, -59], [33, -39], [-100, -37], [-45, 137], [16, 41], [-80, 106], [-74, -23], [-45, 82], [9, 39], [-1, 1], [-47, 36]], [[9042, 12111], [-10, 8], [-131, 8], [-60, -26], [-95, 35], [38, 53], [-115, 83], [5, 119], [32, 29], [-39, 32], [4, 55], [-99, 65], [11, 28], [84, 16], [-23, 69], [27, 76], [-55, -45], [-18, 43], [23, 146], [53, 79], [-48, 35], [11, 85], [-53, 23], [2, 139], [-54, 160], [-161, -53], [-93, -82], [-57, 13], [-65, -61], [-112, -40], [-61, 19], [5, -43], [-35, -16], [7, -272], [0, -2]], [[7960, 12889], [1, -20], [-84, -95], [-13, 0], [-85, 1], [-98, 72], [-85, -10], [-56, 46], [-31, -64], [-48, 23], [-6, -29], [-45, -1], [52, -82], [-25, -81], [-48, -9], [-95, 60], [-60, -94], [-80, -55], [-51, 114], [-170, 10], [-49, -65], [-78, -1], [39, -40], [-96, -44], [-6, -58], [-35, 23], [-6, -95], [-56, 39], [-154, -153], [-52, -94], [-81, 11], [-9, 37], [-139, 53], [-33, 89], [-92, -70]], [[6086, 12307], [-33, 16], [32, 64], [-46, 73], [10, 45], [76, 74], [-41, 49], [-93, 17], [13, 39], [-62, 55], [12, 87], [33, 39], [-44, 42], [-1, 8], [-5, 59], [-1, 6], [1, 1], [15, 23], [14, 21], [1, 0], [0, 0], [106, -31], [9, 6], [46, 47], [0, 1], [0, 0], [-117, 107], [-4, 4], [120, 150], [5, 7], [54, 153], [7, 6], [10, 11], [44, 43], [72, 34], [60, 28], [19, 9], [147, -19], [23, 29], [81, 111], [5, 1], [17, 22], [17, -14], [8, 2], [39, 12], [-8, 40], [-4, 19], [0, 0], [0, 1], [45, 35], [72, 58], [73, -21], [91, 67], [240, 40], [9, 80], [93, 125], [0, 95], [-186, 102], [-7, 24], [-2, 10], [-2, 4], [59, 50], [32, -9], [8, -2], [133, -34], [143, -121], [81, -10], [0, -148], [42, -55], [112, -91], [148, 3], [114, -111], [381, 30], [4, 63], [150, 50], [-26, 36], [12, 37], [153, 43], [258, 22], [105, -45], [230, -2], [25, 85], [142, 20], [20, -50], [53, -17], [120, 54], [16, -88], [9, 1], [65, 14], [36, 30], [-12, 28], [91, 45], [13, -42], [212, 97], [7, 22], [32, -3], [54, 24], [-7, -20], [16, -11], [8, -1], [1, -5], [3, -46], [-48, -46], [74, -150], [-27, -134], [52, -35], [-10, -88], [72, 5], [-3, -71], [-127, -61], [20, -53], [-79, -192], [13, -34], [-68, -65], [-3, -124], [-121, -81], [-1, -30], [45, -9], [65, 81], [31, 7], [38, 8], [17, 4], [119, -29], [65, -108], [95, 16], [7, 1], [19, -41], [14, -21], [-9, 40], [35, 13], [1, -2], [18, -45], [75, -27], [21, -96], [46, -25]], [[10638, 12874], [2, -1], [-14, -38], [-2, -4], [126, -115], [31, -10], [66, 11], [177, -71], [-39, -234], [0, 0], [-64, -106], [-86, -16], [-52, 55], [-127, -104], [-51, 51], [-73, -178], [41, -73], [-84, -90], [23, -38], [-4, -108], [-1, -1]], [[10638, 12874], [101, 222], [12, 14], [5, 11], [35, 34], [58, 57], [23, 23], [30, -4], [90, -10], [-1, 92], [0, 18], [11, 2], [23, 3], [53, 8], [30, -13], [231, -104], [69, -31], [13, -12], [65, -65], [48, 17], [101, -52], [151, 44], [6, 2], [69, -98], [-2, -3], [8, -10]], [[13738, 10706], [-6, -3], [-14, -57], [53, -77], [-15, -67]], [[15785, 12426], [-7, 29], [-21, 81], [16, 26], [33, 52], [80, -5], [66, 37], [27, 132], [1, 2], [46, -41], [23, -19], [41, 20], [77, -31], [12, 24], [-21, -62], [49, -60], [-25, -46], [-16, -23], [1, -4], [34, -78], [-7, -65], [60, -80], [-18, -26], [-1, -1], [34, -32], [8, -8], [10, -9], [42, 24], [35, -56], [69, 3], [7, -30], [48, 23], [41, -65], [6, -10], [4, -8], [33, 36], [28, -30], [126, 6], [10, -76], [23, -11], [-25, -17], [69, -6], [7, -148], [-28, -100], [39, -13], [4, -8], [21, -90], [-2, -38], [-135, -65], [-76, -110], [-71, 86], [-127, 11], [97, -43], [63, -93], [-97, 28], [-382, -18], [-201, -84], [-171, -150], [-51, 9], [-48, -102], [-280, -218], [-247, -442]], [[7960, 12889], [0, 0], [13, 1], [35, -4], [2, 5], [30, 60], [70, 8], [96, 1], [94, -46], [-26, -24], [-7, -114], [-46, 15], [27, -84], [-123, -285], [1, -93], [-112, -187], [-65, -19], [-33, 95], [-42, 29], [-204, -57], [-18, -41], [-115, 42], [-75, -51], [-88, 8], [-54, -55], [-46, 91], [-40, 6], [-14, -43], [-57, 23], [10, -74], [-39, 13], [-42, -85], [-33, -12], [85, -79], [-7, -351], [-11, -24], [-188, -15], [-50, 1], [-47, 10], [-148, 33], [-6, 1], [-25, -84]], [[6662, 11504], [-189, 190], [-96, 225], [-6, 4], [-16, 9], [-108, 60], [-222, 63], [-4, 1], [-4, 1], [-57, -47], [-53, -169], [-109, -91], [-1, 0]], [[5797, 11750], [-190, 112], [39, 61], [79, -7], [83, 144], [-40, 22], [16, 44], [-36, 65], [44, 19], [20, 73], [137, -58], [0, 1], [-22, 83], [-3, 12], [13, 14], [22, 12], [127, -40]], [[3452, 10048], [-2, 2], [-5, 4], [40, 49], [-24, 32], [-3, 4], [35, 34], [-43, 47], [7, 30], [1, 0], [319, 38], [105, -74], [18, 40], [140, 39], [6, -28], [65, 13], [16, -47], [55, 57], [98, 9], [28, -25], [-11, 50], [58, 47], [2, -55], [62, 23], [-21, -39], [63, -6], [-48, -53], [12, -34], [71, 20], [171, -106], [148, -18], [98, 101], [70, 1], [62, 171], [20, 218], [80, 50], [56, -30], [21, 52], [-58, 89], [1, 4], [-2, 2], [41, 154], [-8, 22], [-1, 2], [8, -3], [6, -2], [290, -99], [12, 3], [24, -9], [21, 23], [14, 16], [10, 10], [-32, 27], [6, 116], [-71, 116], [-53, 16], [-44, 163], [96, 174], [-8, 130], [35, 61], [168, 108], [21, -15], [-19, -82], [34, -43], [27, -1], [21, 70], [42, -5], [1, -1], [-7, 40]], [[6662, 11504], [47, -85], [84, -30], [9, -20], [100, -250], [-16, -37], [-96, -36], [95, -82], [-22, -250], [-1, -3], [3, -7], [12, -27], [2, -5], [37, 5], [-6, -41], [0, 0], [2, -4], [56, -124], [-33, -103], [-27, -89], [16, -1]], [[9273, 10296], [2, 1], [-12, 75], [-64, 44], [15, 57], [77, 7], [19, 2], [3, 1], [31, 68], [-46, 16], [20, 60], [-137, 126], [-71, 12], [-76, -26], [23, -31], [-56, -10], [-43, -77], [-50, 5], [-26, -45], [-102, -40], [4, 223], [-56, 157], [-7, 2], [-75, 25], [-53, -21], [5, -35], [-35, 17], [9, 65], [-52, 12], [-27, -7], [-163, -39], [-23, 128], [-1, 18], [60, 83], [-19, 67], [101, 45], [2, 1], [-8, 81], [44, -7], [36, 71], [57, -24], [24, 38], [-1, 2], [-32, 104], [58, -18], [2, 28], [66, -40], [23, 47], [75, -7], [-105, 77], [-19, 56], [-66, -2], [-44, 49], [46, 133], [94, -10], [17, -2], [1, 0], [68, 73], [173, -15], [25, 76], [-20, 50], [42, 12], [36, 57]]], "transform": {"scale": [0.000361854444814947, 0.0003260183788232065], "translate": [81.389054, 17.812726]}, "objects": {"districts": {"type": "GeometryCollection", "geometries": [{"arcs": [[0, 1, 2, 3, 4]], "type": "Polygon", "properties": {"dt_code": "378", "district": "<PERSON><PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[5, 6, 7, 8]], "type": "Polygon", "properties": {"dt_code": "383", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-1, 9, 10, -6, 11]], "type": "Polygon", "properties": {"dt_code": "382", "district": "Jajpur", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[12, 13, 14, 15, 16]], "type": "Polygon", "properties": {"dt_code": "392", "district": "Subarnapur", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[17, 18, 19, 20]], "type": "Polygon", "properties": {"dt_code": "394", "district": "<PERSON><PERSON>pad<PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-15, 21, 22, 23, -19, 24]], "type": "Polygon", "properties": {"dt_code": "393", "district": "Balangir", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-14, 25, 26, 27, -22]], "type": "Polygon", "properties": {"dt_code": "391", "district": "<PERSON><PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-7, -11, 28, 29, 30, 31, 32, 33]], "type": "Polygon", "properties": {"dt_code": "381", "district": "Cuttack", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-23, -28, 34, 35, 36, 37, 38]], "type": "Polygon", "properties": {"dt_code": "390", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-27, 39, -33, 40, 41, -35]], "type": "Polygon", "properties": {"dt_code": "385", "district": "Nayagarh", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-32, 42, 43, -41]], "type": "Polygon", "properties": {"dt_code": "386", "district": "<PERSON><PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-20, -24, -39, 44, 45, 46, 47]], "type": "Polygon", "properties": {"dt_code": "395", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-30, 48, 49, 50]], "type": "Polygon", "properties": {"dt_code": "380", "district": "Jagatsinghpur", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-31, -51, 51, 52, -43]], "type": "Polygon", "properties": {"dt_code": "387", "district": "<PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-47, 53, 54]], "type": "Polygon", "properties": {"dt_code": "397", "district": "Nabarangapur", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-38, 55, 56, 57, -45]], "type": "Polygon", "properties": {"dt_code": "396", "district": "Rayagada", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-46, -58, 58, 59, 60, -54]], "type": "Polygon", "properties": {"dt_code": "398", "district": "Ko<PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-60, 61]], "type": "Polygon", "properties": {"dt_code": "399", "district": "Malkangiri", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[[62]], [[-8, -34, -40, -26, -13, 63, 64, 65, 66]]], "type": "MultiPolygon", "properties": {"dt_code": "384", "district": "<PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-5, 67, -49, -29, -10]], "type": "Polygon", "properties": {"dt_code": "379", "district": "<PERSON><PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[[68]], [[69]], [[-36, -42, -44, -53, 70, 71]]], "type": "MultiPolygon", "properties": {"dt_code": "388", "district": "Ganjam", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-37, -72, 72, -56], [-69], [-70]], "type": "Polygon", "properties": {"dt_code": "389", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[73, 74, 75]], "type": "Polygon", "properties": {"dt_code": "376", "district": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-66, 76, 77, 78, 79, 80]], "type": "Polygon", "properties": {"dt_code": "374", "district": "Sundargarh", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-2, -12, -9, -67, -81, 81, -75, 82]], "type": "Polygon", "properties": {"dt_code": "375", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-3, -83, -74, 83]], "type": "Polygon", "properties": {"dt_code": "377", "district": "Balasore", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-79, 84, 85, 86]], "type": "Polygon", "properties": {"dt_code": "371", "district": "Jharsuguda", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-16, -25, -18, 87, -86, 88]], "type": "Polygon", "properties": {"dt_code": "370", "district": "Bargarh", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-65, 89, -77], [-63]], "type": "Polygon", "properties": {"dt_code": "373", "district": "Deogarh", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}, {"arcs": [[-17, -89, -85, -78, -90, -64]], "type": "Polygon", "properties": {"dt_code": "372", "district": "Sambalpur", "st_code": "21", "year": "2011_c", "st_nm": "Odisha"}}]}}, "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:OGC:1.3:CRS84"}}}