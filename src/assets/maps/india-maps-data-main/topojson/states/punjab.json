{"type": "Topology", "arcs": [[[4624, 9792], [66, 26], [62, -97], [66, 10], [-24, -66], [143, 40], [78, -54], [115, 2], [25, -46], [111, 29], [-15, -125], [36, 15], [18, -62], [2, -6]], [[5307, 9458], [-9, -41]], [[5298, 9417], [-58, -33], [-4, -1], [-1, -4], [-57, -137], [-77, -14], [75, -113], [-84, -220], [117, -247], [2, -74], [-67, -22], [61, -177], [-47, -91], [-71, 21], [41, -118], [-150, -194], [-61, -177], [3, -1]], [[4920, 7815], [-22, -50], [0, -1], [0, 0], [-131, -32], [-66, -126]], [[4701, 7606], [0, 0], [-97, 115], [-105, 61], [-36, -21], [-77, 65], [34, 53], [-89, 54], [2, 61], [-177, -52], [-45, 79], [-59, 20], [31, 43], [-9, 15], [-13, 20], [-19, 31], [-4, 1], [-105, 23], [-33, 45], [-122, -15], [-16, 35], [34, 60], [2, 4], [-89, 39], [-33, 52], [-4, 5], [-95, -18], [-50, 52], [14, 23], [-46, -9], [-2, 1], [-37, 45], [-30, -2], [-67, -4], [-22, 84], [-62, 25], [8, 36], [-86, 33], [9, 50], [-40, 7], [-1, 3], [-48, 123], [52, 24], [96, 23], [102, 70], [0, 0], [1, 8], [5, 45], [-2, 0], [-104, -44], [-68, 47], [11, 101], [45, 34], [0, 1], [-39, 101], [-28, 16]], [[3188, 9274], [-16, 29], [50, 55], [152, -47], [-7, -41], [125, -25], [29, 48], [59, -22], [86, 91], [47, -27], [22, 44], [43, -49], [55, 85], [107, 1], [51, -53], [75, 57], [13, 138], [40, 4], [25, -85], [36, 19], [-12, -44], [94, 40], [3, 36], [51, 22], [19, 125], [43, 7], [71, 97], [-38, 56], [19, 49], [55, 4], [139, -96]], [[5298, 9417], [-10, -44], [19, 2], [153, 22], [15, 2], [20, -7], [190, -157], [84, 17], [90, -122], [59, -37], [6, -4], [32, -21], [176, -56], [130, -84], [153, -346], [64, -130], [18, -14], [-8, -6], [-6, -5], [-11, -8], [-34, 39], [-95, -30], [73, -127], [150, -474], [29, -16], [-2, -62], [87, -107], [28, 8], [3, -74], [200, -326], [-12, -54], [42, -44], [8, -71], [109, -157], [0, -50], [-64, -65], [-1, -15], [5, -68], [10, -16], [82, -42], [3, -125], [2, -2], [61, -15], [212, 40], [20, -45], [43, 22], [0, 0]], [[7431, 6543], [-2, -16], [-2, -20], [68, -24], [-5, -49], [65, -42], [29, -58], [22, -44], [5, -10], [-54, -48]], [[7557, 6232], [-38, -33], [-252, 111], [8, -65], [-44, -105], [37, 32], [29, -39], [-35, -39], [-81, 20], [-39, -28], [44, -172], [-123, -11], [-43, 24], [-1, 45], [-58, -22], [-23, 83], [-40, -8], [-1, -1], [-11, 12], [-47, 47], [-12, 13], [-3, 17], [-22, 129], [-112, -45], [17, 47], [-83, 60], [-8, 62], [-59, -49], [-8, -6], [-32, 53], [-124, -12], [-32, 69], [-36, -20], [-48, 58], [-45, -19], [-32, -13]], [[6200, 6427], [-2, -1], [-24, 55], [-53, -31], [-28, 27], [3, 7], [45, 125], [28, -8], [79, 83], [-46, -16], [-38, 53], [-67, -19], [-22, 85], [-62, -10], [12, -28], [-36, -20], [-62, 57], [-43, -29]], [[5884, 6757], [-5, 16], [-1, -1], [-15, 50], [122, 83], [-65, 183], [-35, -6], [-4, -1], [-26, 73], [-131, 86], [-9, 82], [-94, 45], [3, 99], [-25, -6], [-43, 74], [-38, 121], [-10, 4], [-49, -3], [-33, 40], [-45, -53], [-80, 6]], [[5301, 7649], [-70, 5], [0, 0], [0, 0], [-8, 41], [-20, -25], [16, 91], [-66, -6], [-56, 55], [0, 0], [-2, -6], [-19, -45], [-3, -8], [-153, 64]], [[4701, 7606], [-26, -49], [-104, -40], [-21, -20], [-56, -49], [-144, -222]], [[4350, 7226], [-123, 42], [-69, -41], [-102, 31], [-52, 111], [-51, -70], [-73, -9], [-41, -49], [-62, 104], [-105, 20], [-69, -43], [-38, 37], [-84, -6], [-50, -78], [-32, 83], [-91, -111], [-47, 99], [-111, 1], [-7, 107], [-168, -80], [-67, 21], [-12, 73], [-76, 53], [-65, -42], [-135, 15], [-70, -89], [-82, 40], [-37, -65], [-58, 29], [-99, -128], [-10, -25], [-9, 1]], [[2255, 7257], [-110, 5], [36, 12], [-9, 62], [88, 24], [19, 90], [-38, 40], [29, 25], [-74, 18], [-51, 111], [-62, 15], [-46, 193], [-36, 24], [19, 41], [-102, 67], [-42, 92], [45, 30], [3, 44], [172, 65], [-25, 78], [49, 65], [-29, 31], [47, 41], [-3, 86], [106, 109], [4, 79], [89, -22], [92, 143], [162, 5], [2, 65], [112, 9], [-2, 45], [40, -31], [116, 57], [40, -16], [32, 153], [74, 50], [-15, 26], [53, 11], [-9, 23], [19, -28], [34, 37], [-33, 40], [68, 44], [69, -41]], [[5884, 6757], [2, -8], [-73, -40], [-29, -87], [-49, -25], [-3, 5], [-32, 71], [-79, 7], [-4, -3], [65, -99], [-121, -4], [65, -110], [-83, 22], [-13, -84], [30, -56], [-38, -14], [27, -15], [-28, -30], [41, -27], [46, 56], [2, 2], [4, -9], [37, -88], [-79, -88], [0, 0], [64, -113], [200, -1], [14, 40], [-3, 47], [68, 100], [1, 0]], [[5916, 6206], [-31, -65], [195, -58], [66, -75], [40, 13], [0, -62], [38, 22], [45, -208], [43, -19], [34, -119], [23, -12], [16, -129], [7, -51]], [[6392, 5443], [-129, 22], [-16, 2], [-22, -44], [-96, 12], [-197, -48], [-18, -2], [-39, 17], [-117, 50], [-406, -150], [-36, -7], [-29, -6], [-158, 27], [-18, -36], [-69, 37], [-101, -31], [-25, 48], [-36, -23], [-25, 31], [-52, -5], [-88, -10], [-30, 63], [-3, -1], [-2, 3], [-54, -12], [-16, 19], [-37, -12], [-11, -4]], [[4562, 5383], [-2, 19], [-1, 1], [-51, -43], [-43, 82], [-101, 18], [-41, 101], [-86, 9], [6, -54], [-79, 27], [-81, -63], [9, 126], [-86, 18], [-15, 4], [29, 65], [-64, 44], [-52, -14], [6, 22]], [[3910, 5745], [4, 14], [-71, 95], [-151, 49], [0, 0], [0, 2], [5, 48]], [[3697, 5953], [7, 0], [3, 12], [68, -1], [26, 24], [41, 45], [8, -1], [173, -15], [64, 74], [54, -31], [24, -89], [122, -17], [21, 146], [56, 15], [20, 55], [59, 3], [74, 92], [59, -22], [13, 51], [27, -33], [39, 50], [88, 20], [16, -75], [52, 43], [65, -21], [34, 68], [-28, 97], [-78, 35], [16, 63], [-27, 42], [106, 26], [42, 147], [-65, -13], [-16, 41], [74, 1], [34, 47], [-58, 40], [-36, -53], [-73, 13], [1, 152], [49, 13], [-8, 54], [24, -24], [26, 34], [-27, 97], [-40, 1], [-11, 80], [102, 6], [49, -94], [70, -43], [-13, 77], [34, 73], [-23, 65], [83, -4], [4, 45], [-49, 57], [133, -27], [6, 4], [30, 93], [51, -28], [20, 30], [-32, 45], [9, 40], [24, -8], [1, 1], [-1, 2], [-12, 76]], [[4350, 7226], [-37, -34], [-109, -99], [-25, 15], [-32, -139], [-32, 0], [-13, 49], [-57, -7], [-16, -68], [25, -25], [-96, -93], [-22, -66], [25, -31], [-56, -74], [5, -62], [-41, -36], [-85, 24], [-42, -122], [33, -16], [-67, -22], [-4, -89], [-67, -70], [-37, -149], [-1, -2], [-28, -15], [-225, -121]], [[3346, 5974], [0, 0], [-206, 27], [-166, -105], [-92, 20], [-39, -31], [-4, -54], [-35, 56], [-146, -42], [-1, -55], [-64, -8], [-40, -110]], [[2553, 5672], [-52, 35], [36, 38], [-41, 135], [-70, -14], [-23, 36], [-70, -59], [-17, 54], [-88, 2], [-18, -22], [43, -104], [-30, -42], [-147, -9], [-14, 136], [-118, 36], [1, 182], [28, 12], [20, 103], [-28, 61], [48, 79], [-36, 83], [65, 117], [-48, 32], [69, 84], [10, 111], [100, 57], [37, 133], [70, 14], [10, 38], [62, -52], [37, 36], [-49, 68], [42, 48], [-76, 73], [28, 29], [-45, -10], [-35, 63], [1, 2]], [[7431, 6543], [2, 1], [100, 54], [-46, 77], [144, 9], [-37, 36], [-2, 87], [-37, 2], [4, 84], [114, 10], [21, 117], [49, -101], [5, 16], [34, -59], [-33, -27], [121, -232], [99, -94], [-75, -83], [123, 108], [55, -140], [93, 25], [-11, -71], [48, 11], [11, 79], [25, -13], [-6, -97], [114, 77], [5, 3], [75, -98], [-73, -93], [15, -41], [106, 55], [43, -14], [-22, -18], [34, -33], [0, 0], [-140, -113], [28, -19], [-23, -75], [33, -18], [-32, -91], [105, -28], [8, -30], [-64, -111], [14, -86], [-36, -34], [56, -83], [-13, -91], [149, -111], [112, 3], [2, -1], [78, -92], [-8, -25]], [[8798, 5175], [-5, -15], [-32, -2], [-103, -46], [-22, -23], [-226, -89], [-31, -59], [18, -62], [-34, -22], [-35, 15], [11, -17], [-95, -49], [37, -75], [-19, -120], [-86, -64]], [[8176, 4547], [-111, -83], [-79, -2], [-22, 15], [-56, 36], [-23, 145], [-8, 3], [-5, 74], [-74, 1], [-1, 25], [-4, 77], [-68, 7], [-52, 86], [-60, -25], [0, 31], [-59, 9], [-18, 3], [-13, 2]], [[7523, 4951], [-20, 175], [34, 20], [-2, 8], [-1, 9], [-33, 198], [-17, 7], [-1, 1]], [[7483, 5369], [-1, 7], [63, 8], [-4, -56], [61, 5], [43, -43], [416, 24], [64, 47], [12, 27], [-159, 134], [-127, 239], [11, 75], [-137, 132], [-1, 3], [-3, 3], [-132, 231], [-30, 25], [-2, 2]], [[8319, 4876], [-9, 4], [11, 41], [-36, -8], [10, -59], [24, 22]], [[7483, 5369], [-2, 0], [-34, 14], [-55, 23], [0, 0], [39, 16], [-38, 39], [-3, -3], [-38, -41], [-23, 26], [-98, -28], [-42, 35], [-20, -45], [-70, 20], [19, -35], [-23, -14], [-115, -24], [-22, 44], [-509, 24], [-36, 53], [-21, -30]], [[5916, 6206], [2, 2], [174, 72], [6, 2], [-4, 7], [-45, 88], [1, 22], [2, 33], [15, 15], [66, -26], [5, -3], [-4, -103], [14, 7], [111, 48], [2, 2], [-6, 2], [-38, 17], [-17, 36]], [[1752, 1502], [-462, 30], [0, -4], [-4, 1], [0, 4], [-1240, 68], [-26, 141], [55, 141], [219, 260], [-8, 289], [-16, -15], [-24, 282], [-89, 185], [-157, 147], [261, 239], [29, 73], [-146, -18], [33, 22], [-29, 78], [21, 90], [40, -23], [8, -89], [43, -13], [-2, 105], [57, 71], [33, 38], [84, 0], [-25, 72], [59, -25], [25, 58], [56, -89], [46, 16], [58, 98], [-54, 20], [88, 91], [-19, 119], [28, 29], [40, -42], [29, 100], [67, -12], [207, 221]], [[1037, 4260], [618, -287]], [[1655, 3973], [11, -5], [-2, -57], [-162, -41], [25, -138], [80, -50], [-86, -84], [31, -47], [-23, -56], [-91, -2], [-7, -77], [-50, -67], [-258, -67], [350, -91], [-2, -15], [-11, -61], [-119, 18], [-21, -63], [54, -151], [-68, -76], [-18, -162], [-46, 34], [-59, -101], [71, -51], [115, 43], [106, -16], [-69, -287], [120, -37], [20, -83], [98, -123], [-96, 52], [-50, -91], [72, -48], [78, -143], [-26, -27], [141, 5], [30, -58], [-4, -29], [-117, -32], [80, -187]], [[4562, 5383], [-7, -3], [11, -30], [10, -75], [-9, -14], [17, -91], [11, -42], [39, -1], [-14, -68], [47, -10], [40, -74], [-120, -180], [28, -80], [60, -33], [-73, -261], [43, -58], [-55, -44], [-2, -76], [44, -66], [-36, -81], [26, -28], [-15, -49], [84, -31], [-12, -87], [19, -20], [17, -18]], [[4715, 3863], [-14, -66], [47, -65], [-60, -76], [-121, -56], [-1, 0], [-4, 2], [-75, 27], [-15, 25], [-39, 23], [-61, -63]], [[4372, 3614], [-30, -30], [-79, 45], [-107, -49], [-48, 32], [-31, -34], [-23, 4], [-39, 8], [-7, 153], [-44, 102], [-58, 10], [-78, -75], [22, -146], [-134, 36], [-71, -113], [-91, 10], [-36, -49]], [[3518, 3518], [-54, -8], [-12, -8], [-2, 6], [-4, 20], [-22, 100], [17, 59], [-158, 131], [34, 82], [-27, 57], [34, 225], [-6, 29], [-125, 20], [-35, 78], [6, 119], [0, 2]], [[3164, 4430], [9, -2], [13, 120], [30, 62], [25, 13], [-5, 31], [55, 116], [-14, 182], [30, 52], [241, 19], [32, 94], [116, 89], [48, 108], [28, -12], [-21, 72], [22, 88], [-155, 102], [-4, 73], [32, 19], [33, -77], [66, 1], [7, 43], [-40, 37], [63, -9], [-14, 41], [64, 16], [42, -29], [43, 66]], [[7523, 4951], [1, -1], [-21, -12], [-42, -23], [37, -94], [-92, -45], [24, -82], [-84, -80], [32, -40], [54, 25], [47, -54], [18, -44], [-92, -36], [-16, -46], [91, -94], [44, -45], [11, -12], [-11, -10], [-50, -45], [-88, -11], [-173, 14], [-95, -43], [-44, 43], [-14, -65], [-53, -25], [2, -56], [-112, -93], [28, -66], [-47, -36], [-8, -93], [-30, -6], [-37, 5]], [[6803, 3781], [-6, 1], [-43, 6], [-1, 13], [-6, 56], [-5, 1], [-95, 23], [0, 0], [-23, 98], [-55, 2], [-230, -177], [-45, 88], [26, 20], [-18, 43], [-110, -7], [1, 50], [-48, 31], [0, 39], [79, 30], [-8, 45], [-50, 62], [-83, -23], [-57, 82], [-54, -19], [-4, -85], [-90, -27], [-13, 53], [-52, -3], [1, -46], [98, -96], [-24, -32], [-87, 52], [-70, -21], [-107, -105], [-1, -1], [-63, 11], [-18, -32], [23, -26]], [[5565, 3887], [-71, 15], [-20, -13], [-87, 36], [-70, -24], [-77, -91], [-93, 39], [-41, -37], [-10, -9], [-10, 63], [-1, 1], [0, 0], [-84, -43], [-43, 35], [26, 58], [-93, 26], [-106, -79], [-70, -1]], [[8798, 5175], [14, 6], [30, -23], [73, -85], [18, -22], [24, -99], [94, -94], [-2, -35], [100, -34], [-4, -99], [36, -46], [-58, -147], [-1, -2], [-35, 33], [-35, -39], [-31, 46], [35, 41], [-2, 3], [-8, 11], [-78, -29], [-16, -6], [0, 33], [-34, 57], [-85, -94], [-105, -33], [-15, -66], [80, -72], [25, -113], [76, -25], [39, -59], [41, 20], [50, -30], [5, -1], [59, 54], [2, 1], [1, -7], [13, -57], [43, 14], [19, -37], [75, 81], [-16, -143], [43, 12], [32, -60], [45, -11], [10, -54], [39, -5], [-18, -63], [-32, -5], [31, -25], [-34, -75], [23, -41], [-35, -40], [-49, 22], [132, -99], [-2, -79], [-91, -63], [33, -67], [-5, -9], [-11, -17], [0, 0], [-11, -18], [-18, -32], [-35, -58], [62, 22], [-24, -37], [27, -1], [12, -1], [7, -26], [-8, -68], [83, -24], [-98, -116], [-2, 0], [-47, 27], [23, 47], [-52, 44], [7, 55], [-84, 33], [-28, 76], [-73, -98], [-104, 12], [-37, 37], [31, 32], [-32, 29], [-90, -45]], [[8845, 3289], [-2, 25], [34, 56], [-16, 69], [38, 21], [11, 104], [55, 58], [-103, 65], [-6, 39], [1, 19], [53, -2], [-22, 28], [80, 141], [-37, 39], [-9, 10], [-21, -27], [-45, -25], [-55, -86], [-46, 62], [-48, -11], [-13, -53], [-12, -5], [-14, 3], [-13, 15], [9, 65], [-81, -39], [8, 103], [-53, 50], [-14, 13], [-29, -32]], [[8495, 3994], [-4, -2], [-11, 47], [-3, 29], [-5, 4], [-46, 37], [-76, 62], [-35, -7], [-68, 94], [-8, 64], [-1, 3], [-51, 5], [0, 0], [-23, 101], [14, 14], [-30, 60], [28, 42]], [[3518, 3518], [103, -120], [-61, -1], [-41, -60], [6, -58], [91, -63], [-26, -103], [-34, -1], [-43, 11], [-71, 75], [-78, 0], [-28, -59], [-124, -83], [-142, 86], [-68, -53], [-97, 65]], [[2905, 3154], [0, 0], [-45, 31], [0, 0], [-154, -24], [-2, 58], [8, 46], [-11, 16], [-81, 111], [103, 100], [-6, 35], [-72, -2], [-72, 73], [-45, 12], [-29, 21], [58, 139], [-196, 141], [-11, 7], [-31, 67], [-39, 84], [-69, 41], [-54, 33], [-23, 14], [-160, 16], [-42, 4], [-19, -8]], [[1913, 4169], [13, 8], [-55, 13], [-20, 4], [-26, 282], [152, 80], [66, 80], [167, 62], [85, 100], [69, -66], [52, 58], [333, -69], [38, -77], [73, -20], [21, -80], [66, 10], [70, -99], [147, -25]], [[8495, 3994], [-10, -10], [-8, -9], [-102, -73], [-64, -103], [-123, -26], [-45, -88], [-59, 55], [0, 1], [0, -1], [4, -87], [-35, -30], [23, -202], [-87, 33], [-4, -71], [-30, 18], [-77, -58], [-12, 18], [-41, 57], [-32, -59], [-1, 1], [-75, 86], [-92, -42], [-75, 143], [1, 154], [-138, 17], [-84, 83], [-37, -65], [-46, 75], [-143, -106], [-31, 21], [-23, -44], [-35, 34], [-56, -77], [-50, 81], [-37, -60], [-2, 1], [-66, 28], [0, -1]], [[6803, 3688], [-31, 36], [31, 57]], [[1655, 3973], [73, 46], [9, -9], [58, -57], [-12, 45], [111, 62], [-55, 35], [5, 26], [3, 10], [2, 12], [64, 26]], [[2905, 3154], [0, 0], [-92, -207], [-210, 108], [-74, -35], [8, -2], [255, -72], [-188, -104], [86, -80], [-55, -31], [-50, -112], [23, -69], [-42, -122], [69, -17], [2, -64], [-98, -56], [-52, 29], [-93, -18], [-83, -124], [60, -71], [106, -2], [-21, -105], [88, -63], [-32, -115], [70, -75], [-51, -97], [14, -46]], [[2545, 1604], [-71, -116], [-94, -63], [0, 0], [-15, -56], [23, -21], [-177, 24], [-201, 137], [-16, -29], [2, 6], [-242, 15], [-2, 1]], [[6803, 3688], [10, -11], [-25, -118], [-54, -50], [76, -113], [-119, -83], [10, -30], [-65, -70], [13, -25], [-77, -140], [-67, 17], [-4, -107], [110, -50], [75, 46], [124, -107], [71, 43], [69, -35], [103, 32], [1, -68], [39, 22], [74, -92], [-29, -41], [-37, 10], [1, -68], [-51, -63], [-39, 30], [-48, -54], [-47, 15], [-48, -61], [-2, -2], [-89, 17], [-38, -61], [-50, -81], [75, -129], [-34, -27], [51, -32], [-22, -43], [36, -17], [-13, -29], [45, 13], [9, 2], [1, 1], [23, -47], [-73, -148], [20, -33], [-94, 44], [-41, -53], [25, -11], [-32, -5], [2, -76], [-109, -163], [62, -42], [-1, -46], [-113, -53], [-20, -145], [88, -59], [25, -80], [40, 50], [68, -44], [31, -107], [178, 58], [-38, -145], [-12, -10], [-24, -20]], [[6843, 994], [-7, -5], [-38, -58], [-5, -45], [-50, -36], [-48, -72], [-7, -19], [-7, -1], [-18, -5], [-14, -4], [-30, 44], [-129, -107], [-114, 8], [-4, 3], [-23, 2], [-100, 87], [-23, -29], [-88, 11], [-11, 64], [-91, 103], [-5, 67], [-24, -7], [-37, -23], [-13, 8], [-99, 59], [-1, -1], [-68, -63], [-1, 1], [-2, -1], [-109, 31]], [[5677, 1006], [162, 79], [7, 69], [-56, 30], [21, 211], [-98, 61], [-73, -1], [-10, -59], [-92, -36], [-36, 82], [-71, 22], [20, 173], [125, 107], [0, 19], [-37, 73], [-6, 9], [-78, -61], [-33, 23], [6, 135], [-54, 70], [98, 112], [-62, 4], [-27, 99], [-61, -25], [-89, 29], [-1, 2]], [[5232, 2233], [-65, 122], [6, 68], [0, 8], [86, 68], [-4, 45], [92, -41], [107, 77], [99, 7], [33, -36], [-11, 35], [127, 125], [-165, 54], [-5, 8], [-22, 28], [-34, 46], [-2, 42], [-2, 45], [-1, 14], [15, -3], [78, 56], [-56, 135], [12, 119], [-72, 77], [80, 66], [9, 52], [-53, 30], [2, 55], [66, 55], [-86, 121], [100, 175], [-1, 1]], [[5232, 2233], [-37, 13], [-3, -3], [-46, -35], [-46, 35], [-42, -7], [-56, 17], [-33, 46], [-82, 113], [-3, 5], [-2, 0], [-144, -29], [-80, 83], [-37, -29], [-14, -10], [-14, 9]], [[4593, 2441], [6, 7], [-35, 12], [53, 84], [-41, 48], [23, 74], [-30, 24], [8, 54], [0, 0], [-68, 12], [14, 55], [-38, 23], [-50, -7], [-3, 24], [-5, 61], [92, 36], [-64, 50], [28, 101], [-87, 3], [0, 146], [-69, 88], [-55, -20], [-35, 101], [135, 197]], [[4593, 2441], [-34, -46], [-2, -3], [-1, -1], [5, -84], [0, -5], [-3, -1], [-41, -12], [-3, -49], [30, -15], [-27, -41], [0, 0], [-98, -71], [-84, 16], [-101, -41], [28, -166], [43, -9], [-79, -96], [31, -60], [66, -17], [23, 41], [37, -7], [-83, -91], [21, -62], [-93, 13], [-43, -85], [-111, 67], [-56, -29], [2, -106], [-35, -50], [39, -44], [10, -99], [91, -24], [2, -69], [-2, -2], [-16, -14], [-66, -58]], [[4043, 1121], [-71, -63], [-7, -5], [0, -2], [-20, -90], [24, -62], [-49, -33], [-49, 117], [-106, 0], [2, 53], [54, 37], [-1, 1], [-88, 82], [76, 92], [-47, 121], [-2, 5], [-6, 2], [-10, 3], [-21, 5], [-52, -142], [-71, 30], [-75, -70], [-78, 4], [-57, 99], [-90, 45], [-71, 163], [-87, -32], [15, 39], [-47, 56], [-101, -40], [-52, 55], [-54, -1], [11, 55], [-38, 23], [-2, 1], [-99, -75], [-148, -39], [-76, 57], [-5, -8]], [[8845, 3289], [-21, -11], [-7, -49], [3, -7], [-5, -2], [-57, -25], [-16, -7], [-1, -5], [9, -66], [5, -10], [38, -2], [-35, -60], [79, 17], [10, -59], [-116, -97], [-69, 5], [-21, -59], [-143, -81], [-37, -92], [-28, 29], [-66, -40], [-28, 32], [-75, -26], [7, -81], [-36, -60], [7, 0], [102, 86], [21, 17], [12, 10], [13, -14], [6, -6], [4, -4], [-23, -44], [96, -50], [-36, -55], [116, -30], [8, -62], [-45, -54], [-39, 33], [50, 33], [-26, 34], [-69, 29], [-44, -73], [103, -66], [29, -62], [-2, -1], [1, -1], [-12, -13], [-120, -125], [-71, -9], [-95, 55], [-19, -62], [115, -25], [-94, -95], [-106, -14], [-22, 76], [-68, 16], [-47, 8], [-3, 3], [-11, 3], [-1, 13], [-8, 94], [-63, 13], [-3, 0], [-26, 55], [-4, 9], [-1, 2], [37, 56], [-60, 97], [-3, -1], [-102, -79], [71, -94], [-58, -38], [27, -68], [-117, 13], [-36, 99], [-52, -32], [82, -67], [-49, -71], [-69, -22], [-78, 72], [-35, -31], [-97, 58], [-59, 86], [-83, 43], [52, -109], [-24, -63], [66, -17], [47, -62], [-123, -212], [-1, -33], [60, -25], [-70, -40], [-13, -114], [-104, -179], [48, -20], [-6, -105], [24, -25], [53, 21], [111, -57], [0, -3], [-4, -44], [-3, -31], [-1, 0], [-31, -11], [-28, -9], [-10, -12], [-4, -31], [-17, 6], [0, 0], [-49, 18], [-4, 1], [-63, -84], [-11, -15], [-87, 4], [-76, -95], [-4, -4], [-10, 16], [-54, 50]], [[5677, 1006], [-53, -221], [-7, 1], [-73, 83], [-1, 0], [-42, -2], [-53, -2], [-3, -1], [-11, -3], [-21, -7], [-28, -89], [-3, -2], [-61, 15], [-78, -46], [-55, 114], [-75, 11], [-46, -55], [-12, 64], [-1, 0], [-4, 3], [-173, 116], [-3, -11], [-20, -84], [-114, -42], [-9, -3], [-2, -9], [-26, -106], [-51, -33], [0, 0], [12, -62], [-59, 0], [-84, -64], [44, -124], [-82, 38], [-1, 1], [-12, -39], [-36, -108], [-67, -31], [0, 0], [-3, -7], [-17, -55], [1, 0], [93, -78], [-50, -98], [-2, -5], [0, 0], [-68, 7], [-24, -43], [-117, -29], [10, 62], [-52, 80], [42, 84], [-161, 94], [14, 87], [-59, 55], [118, 77], [-17, 134], [15, 36], [9, 14], [53, -30], [47, 80], [-2, 6], [-45, 137], [-54, 15], [6, 145], [-62, 38], [1, 7]], [[1037, 4260], [51, 124], [87, -6], [23, 42], [55, 3], [-81, 139], [129, 25], [16, 37], [-37, 19], [77, 122], [2, 71], [85, 31], [47, -28], [4, 37], [54, 11], [-65, 50], [15, 71], [97, 61], [78, -6], [-38, 58], [41, -10], [16, 39], [-47, -6], [9, 40], [71, 6], [124, 120], [75, -35], [131, 102], [-13, 92], [80, 139], [68, -18], [20, -45], [215, 53], [90, 82], [37, -8]], [[3346, 5974], [-15, -23], [146, -95], [6, 11], [-8, 76], [53, 42], [114, -39], [57, 26], [-2, -19]], [[4624, 9792], [-161, 114], [138, 39], [-4, 125], [34, 40], [-30, 40], [-86, -3], [19, 41], [-90, 27], [42, 52], [21, 129], [276, -112], [1, 0], [112, 35], [3, 50], [64, 26], [17, -18], [-45, -25], [6, -52], [93, -46], [-27, -60], [45, -38], [23, -20], [20, 7], [5, 2], [4, 1], [23, 100], [-9, 113], [63, 77], [113, 77], [279, 47], [75, 76], [35, -20], [18, 100], [47, -2], [-44, 26], [-3, 51], [145, 43], [2, 2], [10, 8], [164, 135], [9, 25], [2, 2], [24, -25], [178, -190], [94, -218], [-179, 7], [-1, -1], [-81, -81], [-10, -66], [-102, -27], [12, -68], [-53, -21], [-84, -133], [-408, -136], [-63, -57], [0, 0], [58, -20], [6, -35], [-38, -24], [-7, -93], [98, 25], [9, -73], [26, 1], [-10, -57], [28, -13], [-36, -81], [-111, -69], [-33, -81], [-26, -8], [-17, -4]]], "transform": {"scale": [0.0003242653137058807, 0.0002697326551308029], "translate": [73.879848, 29.54208]}, "objects": {"districts": {"type": "GeometryCollection", "geometries": [{"arcs": [[0, 1, 2, 3, 4, 5]], "type": "Polygon", "properties": {"dt_code": "035", "district": "Gurdaspur", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-3, 6, 7, 8, 9, 10, 11]], "type": "Polygon", "properties": {"dt_code": "038", "district": "Hoshiarpur", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-5, 12, 13, 14]], "type": "Polygon", "properties": {"dt_code": "049", "district": "Amritsar", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-11, 15, 16, 17, 18, 19, 20]], "type": "Polygon", "properties": {"dt_code": "037", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-14, 21, 22, 23]], "type": "Polygon", "properties": {"dt_code": "050", "district": "<PERSON><PERSON>", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-8, 24, 25, 26, 27, 28], [29]], "type": "Polygon", "properties": {"dt_code": "051", "district": "Rupnagar", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-9, -29, 30, -17, 31]], "type": "Polygon", "properties": {"dt_code": "039", "district": "<PERSON><PERSON>", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[32, 33, 34]], "type": "Polygon", "properties": {"dt_code": "701", "district": "Fazilka", "st_code": "03", "year": "update2014", "st_nm": "Punjab"}}, {"arcs": [[-19, 35, 36, 37, 38, 39]], "type": "Polygon", "properties": {"dt_code": "042", "district": "Moga", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-18, -31, -28, 40, 41, 42, -36]], "type": "Polygon", "properties": {"dt_code": "041", "district": "<PERSON><PERSON><PERSON>", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[[-26, 43, 44, 45]], [[-30]]], "type": "MultiPolygon", "properties": {"dt_code": "052", "district": "S.A.S. Nagar", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-39, 46, 47, 48]], "type": "Polygon", "properties": {"dt_code": "045", "district": "Faridkot", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-27, -46, 49, 50, -41]], "type": "Polygon", "properties": {"dt_code": "040", "district": "Fatehgarh Sahib", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-35, 51, -48, 52, 53]], "type": "Polygon", "properties": {"dt_code": "044", "district": "Sri Muktsar Sahib", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-42, -51, 54, 55, 56, 57]], "type": "Polygon", "properties": {"dt_code": "053", "district": "<PERSON><PERSON><PERSON>", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-37, -43, -58, 58, 59]], "type": "Polygon", "properties": {"dt_code": "054", "district": "<PERSON><PERSON><PERSON>", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-38, -60, 60, 61, -53, -47]], "type": "Polygon", "properties": {"dt_code": "046", "district": "Bathinda", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-45, 62, -55, -50]], "type": "Polygon", "properties": {"dt_code": "048", "district": "<PERSON><PERSON><PERSON>", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-57, 63, -61, -59]], "type": "Polygon", "properties": {"dt_code": "047", "district": "<PERSON><PERSON>", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-20, -40, -49, -52, -34, 64, -23, 65]], "type": "Polygon", "properties": {"dt_code": "043", "district": "Ferozepur", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[-1, 66]], "type": "Polygon", "properties": {"dt_code": "773", "district": "Pathankot", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}, {"arcs": [[[-10, -32, -16]], [[-4, -12, -21, -66, -22, -13]]], "type": "MultiPolygon", "properties": {"dt_code": "036", "district": "Kapurthala", "st_code": "03", "year": "2011_c", "st_nm": "Punjab"}}]}}, "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:OGC:1.3:CRS84"}}}