{"type": "Topology", "arcs": [[[15346, 16909], [57, -86], [5, -136], [56, -43], [-43, -149], [69, -101], [32, 9], [-32, -35], [24, -39], [4, -185], [50, -71], [2, -65], [154, -33], [1, -1], [29, -165], [85, -33], [11, -4], [-5, -19], [4, -18]], [[15849, 15735], [-1, 1], [-16, -25], [-145, -6], [-11, -170], [-45, -23], [66, -96], [-110, 14], [-2, -93], [-100, 13], [0, -32], [-40, -9], [-2, -67], [-29, -31], [-115, 10], [-36, 79], [-70, -10], [10, -65], [-93, -11], [-4, 3], [-125, 80], [-7, 82], [-76, 95], [-27, -8], [-31, 77], [-40, -3], [-33, 39], [-42, -22], [5, -49], [-43, -30], [21, -99], [-86, -42], [-119, 4], [48, -107], [-41, -59], [21, -35], [-21, -49], [-64, -18], [-65, -67], [-55, 8], [-80, -39], [9, -77], [-58, -58], [-1, -8], [-1, -8]], [[14195, 14824], [-2, -12], [-33, -14], [-13, 15], [-156, -38], [-40, 31], [-109, -20], [-73, -160], [-83, -64], [-83, 11], [-23, -96], [56, -57], [-9, -93], [-27, -16], [-2, -62], [-82, -41], [64, -100], [91, -36], [32, -53], [-14, -42], [-87, -42], [-45, 19], [-103, -27], [-15, -43], [29, 2], [-28, -13], [17, -40], [75, -1], [34, -68], [44, 16], [56, -113], [-80, -75], [-115, -24], [-24, -31], [-15, -99], [6, -1], [22, 10], [32, -28], [-8, -85], [-25, -24], [-53, 2]], [[13406, 13312], [-43, 2], [-145, -70], [-42, -16], [-58, 85], [55, 48], [-5, 4], [-41, 33], [-159, -128], [-50, 34], [-80, -24], [-9, 59], [-208, 4], [-18, 31], [-82, -48], [-79, -5], [-14, -58], [-67, -34], [10, -36], [-90, -33], [-63, -69], [11, -111], [-36, -19], [-11, -90], [-113, -75], [-11, -51], [46, -32], [-12, -138], [-136, -20], [-15, 18], [-3, 27], [-7, 61], [-58, 20], [-18, 43], [-77, 12], [-134, 165], [-97, -89], [-90, 17], [-123, -60], [-45, 31], [-40, 108]], [[11249, 12908], [2, 1], [3, 1], [19, 60], [-26, 59], [53, 65], [-96, 166], [35, 10], [-14, 68], [28, 31], [-35, 105], [11, 130], [49, 34], [92, -25], [179, 82], [30, -65], [71, -59], [-105, 28], [1, -66], [86, 19], [-70, -77], [56, -36], [41, 23], [11, -29], [68, 31], [53, 121], [83, 72], [144, -8], [131, 65], [14, 51], [-35, 119], [10, 77], [33, 7], [42, 82], [-47, 86], [30, 19], [-15, 82], [147, 131], [-47, 136], [49, 81], [-7, 73], [119, 58], [45, 116], [-95, 141], [-108, 14], [-79, -36], [-96, 104], [-30, -10], [-13, 23], [44, 46], [-35, 81], [-122, -36], [-4, 54], [-55, 26], [-167, -42], [-87, 128], [5, 80], [127, 56], [-52, 70], [15, 137], [-27, 89], [139, 170], [-75, 244], [290, 114], [-47, 78], [-90, 1], [31, 64], [25, 17], [-2, 33], [0, 0]], [[11979, 16478], [1, 1], [24, 50], [22, 15], [14, 9], [29, 20], [74, 8], [3, 1], [3, 0], [50, 0], [13, -39], [8, -2], [37, -6], [238, 163], [47, 64], [101, 29], [46, -162], [101, -39], [121, 30], [46, -26], [104, 36], [34, -51], [92, 3], [9, 69], [147, -9], [71, 49], [73, 9], [11, -73], [48, -17], [69, 26], [-7, 72], [-72, 47], [-6, 47], [39, 70], [56, 46], [147, 43], [106, -15], [-10, -35], [68, -100], [45, 2], [13, 40], [124, 8], [19, 61], [86, 23], [23, -124], [-40, -42], [-4, -86], [243, 74], [64, -7], [58, -78], [35, 39], [14, 20], [3, -1], [80, -26], [-6, 20], [70, 54], [-8, 136], [84, 0], [43, -39], [17, 92], [20, 4], [-42, 53], [9, 39], [101, -3], [60, -43], [0, -56], [-6, -2], [-53, -21], [0, -1], [-1, -3], [-3, -51], [52, -16], [34, 0], [22, 17], [6, 50], [55, 8], [8, 84], [65, 85], [0, 1], [60, -45], [5, -86], [55, -82]], [[15849, 15735], [80, -77], [97, -21], [-10, -46], [135, -44], [0, -57], [-56, -56], [26, -40], [15, 4], [93, 23], [0, 0], [95, -58], [43, -9], [51, 9], [9, 1], [11, -52], [46, 0], [37, -102], [57, -8], [7, -57], [38, 13], [35, -73], [102, -52], [-26, -110], [102, -48], [-22, -64], [63, -11], [43, -110], [10, -26], [6, -16], [-36, 0], [-40, 0], [14, 68], [-100, 8], [-38, -51], [-24, 9], [-22, -88], [-57, 6], [-79, -51], [-18, -55], [115, -19], [12, -44], [-34, -44], [24, -11], [60, 34], [39, 78], [46, -34], [-40, -58], [-44, 12], [23, -16], [-2, -64], [-55, 23], [-3, -1], [-49, -38], [-9, -26], [63, -26], [-15, -35], [-59, 15], [-3, -47], [87, -19], [-45, -34], [-24, -94], [-66, 38], [-37, -45], [1, -5]], [[16521, 14034], [-2, 4], [-5, -4], [-54, -36], [-1, -28], [-26, 20], [-334, -237], [-144, -203], [20, -38], [-2, -5], [-83, -193], [-49, -5], [-15, 37], [13, 75], [-59, 13], [-64, -51], [-66, 24], [-6, -24], [-55, -9], [-74, 36], [-69, -58], [-98, 7], [-27, -75], [-91, -77], [-87, 62], [-51, -56], [6, 110], [-33, 37], [-99, 8], [-12, 36], [-93, 26], [-66, 139], [75, 21], [-5, 134], [-118, -20], [9, 93], [-37, 95], [-50, 9], [-18, 79], [-29, 20], [4, 120], [-71, 2], [-56, 197], [-42, -5], [-54, 72], [-24, 61], [43, 51], [-15, 75], [52, 17], [-3, 44], [-99, 74], [-91, -8], [-1, 26], [79, 54], [-4, 3], [-1, 2], [-24, 19], [-67, -13], [-46, 35], [-7, -2]], [[6138, 13992], [-24, -28], [31, -60], [23, -43], [18, 41], [60, -45], [3, 2], [7, 3], [24, 27], [5, 14], [-17, 24], [81, 118], [54, -9], [59, 62], [57, 1], [97, -61], [55, -75], [131, -69], [26, -57], [-30, -38], [31, -132], [33, -28], [-161, -127], [-12, -9], [-2, -76], [6, -7], [112, -4], [108, -44], [1, -1], [80, -57], [14, -52], [190, 142], [112, -47], [-102, -270], [-54, -90], [-52, -22], [60, -21], [-5, -3]], [[7157, 12951], [-24, -14], [-28, -16], [-30, -39], [-8, -57], [1, -61]], [[7068, 12764], [-1, -11]], [[7067, 12753], [-10, 2], [-1, -11], [-73, 34], [-3, -36], [-87, -51], [-47, 41], [8, 56], [-59, 77], [17, 39], [-101, 71], [60, 59], [-200, 99], [-63, -54], [-65, -74], [-83, -53], [-110, 29], [-70, -98], [-12, -151], [-40, 31], [-112, -16], [-60, -117], [29, -116], [-9, -173], [62, -105], [113, -38], [72, -178], [-3, -144], [105, 13], [22, -124], [77, -18], [128, -85], [-41, -144], [65, -46], [-48, -46], [25, -16], [19, -142], [-31, -96], [106, -58], [60, -82], [-3, -82], [-34, -22], [28, -57], [-36, -65], [-75, -13], [-26, -40], [37, -21], [-4, -58], [73, -90], [-2, -35], [-42, 2], [-80, -111], [68, -97], [-15, -18], [99, -114], [-179, -293], [127, -136], [-15, -296], [-17, 0]], [[6611, 9486], [-66, 1], [-47, -14], [-100, -79], [-228, -59], [-25, 119], [-115, 100], [-53, -45], [-11, -71], [-28, -1], [-38, 37], [-82, 6], [-9, 41], [-91, 69], [-36, 17], [-69, -26], [-105, 61], [-30, 36], [23, 38], [-48, 147], [-57, 59], [-45, 12], [-136, -55], [-82, -84], [-51, 47], [-45, 111], [-18, -15], [-19, -30], [-280, -219], [-50, 43], [-28, 81], [-33, 9], [-181, -70], [-78, -102], [18, -65], [-125, -222], [-37, -14], [-130, 57], [-42, -29], [29, -52], [-38, -32], [-113, -10], [-32, -58], [-127, -45], [-22, -59], [171, -2], [9, -68], [72, -78], [28, -96], [-67, -73], [-64, -3], [-151, -96], [71, -134], [-24, -44], [-45, 33], [-2, 71], [-185, 223], [-323, 29], [-80, 70], [13, 129], [-89, 2], [-68, -42], [-240, 5], [-157, -75], [-176, 7], [-16, 52], [-42, 15], [-116, -160], [-162, 6], [-24, 31], [-49, -4], [-5, 28], [-49, -19], [-74, -109], [0, -1], [-52, 79], [-153, 28]], [[1682, 8925], [0, 0], [88, 275], [-30, 126], [56, 229], [-29, 510], [-145, 103], [-157, 39], [-436, -63], [-163, 30], [-78, 26], [-185, 163], [-537, 256], [-66, 177], [74, 591], [189, 500], [295, 289], [412, 341], [181, 276], [236, 187], [273, 700], [240, 274], [179, 101], [195, 219], [346, 72], [211, -76], [225, -255], [18, -270], [192, -308], [288, -45], [88, 11], [767, 363], [455, 107], [726, 15], [535, 222], [13, -118]], [[16521, 14034], [11, -32], [129, -38], [-31, -30], [23, -32], [-33, -87], [40, -31], [156, -19], [32, 38], [37, -13], [3, -6]], [[16888, 13784], [-4, -2], [12, -10], [27, -45], [0, -12], [-10, -26], [-25, -17], [-24, -101], [-39, 4], [-58, -143], [-100, -79], [-30, -111], [-54, -47], [17, -43], [-60, -9], [11, -80], [46, 23], [11, -23], [-62, -60], [25, -24], [-36, -188], [117, -3], [5, -80], [-84, 9], [-90, -58], [-76, 5], [-46, -104], [34, -57], [-22, -27], [-70, 34], [-53, -38], [-37, 41], [-42, -72], [-43, 14], [-7, 51], [-94, 4], [-51, -63], [7, -47], [-129, 5], [-31, -93], [-83, 10], [-25, -67], [-46, -29], [-76, 6], [23, 60], [-41, 44], [-95, -10], [-132, -154], [-45, -5], [-41, 33], [-161, -10], [35, -28], [-8, -144], [-159, -52], [-3, -6], [-15, -30], [-19, -39], [7, -118], [1, -1]], [[14940, 11772], [-46, -11], [-1, -40], [-37, -13], [-40, 24], [-180, -11], [-11, 35], [-56, 3], [15, 48], [-126, 31], [-29, 35], [12, 95], [-132, 12], [43, 80], [35, 0], [85, 107], [58, -7], [23, 73], [-8, 39], [-140, 53], [21, 120], [-96, 25], [-82, -24], [-27, -66], [-225, -14], [-19, -33], [-105, -21], [1, -45], [-49, -16], [-33, 73], [34, 28], [-14, 78], [-71, 15], [69, 98], [-37, 111], [-150, 0], [-10, 107], [-65, 21], [-43, -19], [-12, 37], [-49, -21], [-26, 24], [-5, 109], [110, 76], [-2, 46], [-12, 33], [-37, -24], [-28, 70], [-66, -36], [-12, 74], [32, 48], [-82, 88], [91, 25]], [[17270, 13760], [-42, 19], [7, 14], [-45, 42], [19, 96], [-85, 31], [47, 179], [-59, 23], [-9, 69], [43, 69], [51, -17], [51, 57], [-53, -14], [-41, 69], [149, -14], [12, 43], [47, 9], [83, -53], [-3, -67], [68, -30], [58, 42], [34, 110], [-137, 85], [1, 0], [-1, 0], [20, 30], [70, -16], [60, 64], [-100, 68], [-60, 2], [18, 29], [-61, 18], [1, 1], [29, 39], [49, -4], [34, -14], [53, -11], [32, -24], [-3, -50], [38, -18], [50, 6], [22, 16], [51, -7], [74, 9], [53, 16], [3, -9], [8, -24], [5, -19], [12, -7], [30, -3], [-5, -12], [31, -19], [-7, -56], [-91, 27], [-41, -57], [138, -84], [-31, -21], [-72, 17], [-17, -47], [109, 3], [33, -39], [49, 30], [37, -17], [-13, -172], [10, -21], [23, -2], [41, 21], [54, 69], [204, 46], [-36, 60], [8, 158], [77, -5], [73, 115], [71, -19], [48, 62], [83, 17], [15, 40], [-21, 63], [51, 19], [-10, 58], [58, -5], [49, 35], [7, -1], [56, -9], [-5, -35], [87, -15], [-36, -49], [101, -88], [48, -9], [-7, -22], [57, -11], [-101, -417], [18, -31], [-16, -174], [51, -12], [-3, -65], [-33, 2], [12, -155], [-17, -39], [-56, 8], [-10, -48], [23, -32], [-68, -207], [5, -42], [77, -52], [-69, -6], [-10, -1], [32, -32], [-20, -25], [40, -65], [49, 3], [25, 50], [1, 1], [75, -47], [15, -10]], [[19199, 13243], [0, 0], [1, 0], [4, -35], [8, -64], [-99, -8], [57, -16], [-83, -23], [30, -50], [-17, -27], [56, -2], [-9, -48], [-32, 1], [37, -46], [32, 24], [59, -53], [56, 12], [7, 25], [-58, 34], [5, 35], [130, -26], [0, -1], [-9, -35], [-38, 8], [91, -41], [-1, -2], [-20, -28], [-53, 11], [-5, -49], [-38, 8], [-9, -35], [86, -57], [20, -109], [-31, -23], [-32, 18], [35, 45], [-40, 23], [-40, -51], [-130, 34], [1, 38], [-77, 35], [-22, 84], [-61, 12], [8, 21], [42, -11], [5, 37], [-73, -1], [-46, -82], [42, -50], [40, 23], [30, -18], [-44, -84], [96, -48], [43, -81], [6, 0], [67, 26], [53, -31], [-3, -61], [63, 16], [-16, -37], [79, -21], [7, 0], [27, 37], [116, -98], [37, -15], [20, -6], [140, 22], [1, -1], [2, 0], [38, -21], [0, -5], [-1, -15], [0, -15], [-10, -6], [-107, -65], [-44, 11], [-66, -183], [-74, -21], [-36, -49], [-73, 16], [-19, -45], [119, -67], [-18, -56], [52, -25], [-14, -27], [-107, -11], [-69, 20], [-7, 34], [-145, 15], [-31, -67], [11, -43], [-7, -2]], [[19144, 11771], [-78, -20], [-84, 72], [5, 40], [37, 15], [-40, 148], [0, 0], [-19, -32], [-51, 28], [-25, -60], [-40, -6], [21, -48], [-9, -29], [-16, -52], [-36, 1], [-7, -89], [12, -41], [28, 4], [-27, -127], [-47, 15], [-24, 64], [26, 59], [-46, 46], [-23, -77], [-163, -17], [-101, 22], [-41, -64], [-74, 1], [-37, 44], [14, 46], [-25, 11], [73, 106], [-158, 36], [-79, -13], [-17, -135], [-21, 16], [-70, -49], [-61, -1], [-83, -109], [-57, 36], [-20, 116], [-25, 3], [-46, -17], [-16, -129], [-24, 7], [-77, -77], [-182, 29]], [[17411, 11544], [-1, 0], [3, 245], [-37, 87], [-6, 13], [-16, -20], [-37, -42], [-50, -59], [-69, -80], [-34, 9], [-29, 32], [10, 158], [-73, -9], [13, 104], [-29, 10], [-31, -71], [-25, -3], [31, 84], [-41, 30], [52, 134], [17, -2], [17, 91], [46, -6], [19, 86], [57, 68], [-13, 34], [54, 86], [26, -3], [2, 129], [67, -19], [-96, 38], [62, 57], [9, 85], [-35, 27], [19, 31], [-75, 29], [18, 43], [-49, 188], [30, 31], [62, -12], [40, 25], [8, 78], [-34, 32], [35, 6], [-22, 43], [35, 20], [-43, 183], [55, 23], [27, 55], [-12, 47], [59, 5], [-82, 61], [19, 32], [-63, 45], [-26, -44], [-5, 2]], [[16888, 13784], [24, 9], [9, 45], [0, 0], [88, -27], [31, -34], [-8, -43], [55, -13], [58, -59], [59, 64], [46, -14], [20, 48], [0, 0]], [[17411, 11544], [-1, -10], [-1, -36], [-5, -5], [-45, -42], [-11, -11], [-25, -23], [-18, -18], [-28, -70], [-14, -90], [-1, -11], [66, -24], [-38, -5], [10, -56], [-51, -48], [49, -42], [4, -48], [58, -14], [-53, -81], [53, -30], [-32, -62], [17, -26], [-40, -14], [-34, 31], [-57, -24], [-25, -92], [-39, -33], [36, -14], [-14, -31], [27, -13], [-43, -26], [74, -21], [-52, -29], [-27, -69], [-22, 9], [13, 70], [-20, 9], [-61, -68], [100, -235], [-40, -28], [-29, -83], [-5, -152], [-2, 1]], [[17085, 9980], [-21, 7], [-113, 42], [-44, -47], [-32, 15], [-20, -54], [-99, 35], [-41, -61], [-95, 21], [-29, -61], [-84, 66], [-290, 34], [-53, 27], [26, 33], [-20, 26], [-62, -22], [-7, -35], [26, -20], [-51, -18], [32, -132], [-64, -8], [-17, -63], [-35, 56], [-46, -58], [-144, 56], [-17, -67], [-81, -8], [-116, 24], [7, 80], [-59, 32], [-72, -67], [-51, 53], [-119, 40], [-54, -39], [-64, 36], [4, -34], [-112, 16], [-82, 96], [-15, 113], [-98, -12], [-12, 20], [-86, -56], [0, -39], [-127, -39], [-7, -105], [-43, 10], [-30, -61], [-7, -1]], [[14561, 9811], [-1, 0], [-17, -1], [0, 32], [-40, -11], [2, 9], [12, 78], [-4, 3], [-93, 64], [12, 84], [-56, 21], [-56, -43], [-33, 29], [50, 153], [-51, 48], [10, 87], [-114, 26], [-24, 75], [-86, -44], [-18, 14], [-5, 5], [-11, 8], [0, 107], [-56, 8], [0, 27], [4, -1], [144, -3], [21, 60], [-65, 39], [-115, -48], [0, 87], [-34, 36], [-26, 125], [-1, 6], [57, 18], [44, 18], [2, 45], [54, 72], [0, 1]], [[14067, 11045], [3, -1], [35, -3], [52, 15], [-1, 147], [387, -38], [-6, 113], [107, 9], [2, 95], [99, 16], [37, 50], [-90, 61], [-2, 81], [287, 34], [85, -25], [-2, 64], [-4, 1], [-12, 5], [-32, 11], [-13, 55], [0, 3], [-2, 3], [-7, 7], [-19, 23], [-22, -3], [-9, 4]], [[7067, 12753], [-3, -23], [26, -39], [32, -13], [152, 170], [91, -67], [15, 106], [57, 56], [40, -9], [90, 63], [21, 59], [136, -6], [139, 96], [26, -33], [91, -3], [47, -43], [8, -57], [89, -105], [88, 198], [57, -46], [38, -113], [89, -72], [31, 43], [-19, 72], [154, 57], [43, -81], [-17, -73], [-61, -39], [12, -115], [21, -28], [122, 24], [-49, -89], [-3, -5], [90, 19], [18, -102], [95, 96], [26, -131], [95, -61], [137, 114], [104, 22], [18, -4], [63, -23], [52, -54], [22, -264], [1, 0], [0, 0], [-3, -6]], [[9348, 12244], [-62, -145], [-61, -50], [42, -49], [35, -152], [4, -82], [-48, -69], [103, -194], [-77, -86], [31, -88], [-33, -61], [35, -79], [84, -47], [41, 23], [35, -141], [48, -11], [4, -34], [89, -21], [95, 2], [39, 34], [85, -56], [76, 47], [41, -5], [79, 29], [26, 20], [28, 0], [28, 0], [22, 0], [16, -36], [8, 2], [123, 41], [8, -8], [81, -81], [154, 19], [57, -71], [35, 14], [14, -109], [58, 3], [14, -87], [41, -57], [-71, -122], [12, -62], [44, -37], [-35, -87], [59, -24], [45, -113], [0, -84], [51, -100], [121, -9], [39, -68], [87, -23], [46, -85], [53, -25], [9, -66], [12, -16], [19, -25]], [[11237, 9713], [-67, -64], [-11, -78], [22, -14], [-97, -55], [-13, -129], [-52, 20], [41, -19], [23, -73], [-7, -64], [-55, -87], [45, -97], [-53, -107], [0, -41], [71, -110], [-29, -53], [-121, -5], [-54, 58], [-60, -47], [-35, -83], [-99, 12], [-24, 55], [19, 85], [-66, 77], [-69, -59], [-69, 11], [10, -46], [-51, -27], [-96, -9], [12, -111], [-46, 13], [-29, 54], [-84, -2], [-83, 132], [-57, -18], [-17, 107], [-25, -6], [-45, -16], [2, -46], [-12, -8], [-77, -54], [20, -82], [37, -25], [-11, -47], [-77, -24], [-34, -51], [11, -73], [-30, 9], [1, 48], [-136, 69], [-14, 31], [21, 13], [-27, -2], [-22, 41], [-40, -33], [-79, 31], [3, -39], [-41, -44], [19, -57], [-58, -6], [-67, 41], [-55, -49], [-161, -42], [-23, -143], [-46, -34], [-74, 4], [-9, -42], [-51, -18], [3, -40], [-39, -56], [-218, -33], [-22, 17], [-40, -3], [-12, -7], [-11, -37], [-3, -36], [-10, -5], [0, -2]], [[8584, 8083], [0, 0], [-66, -31], [-74, 90], [-70, -10], [-1, 47], [-66, 68], [-23, 13], [-47, 27], [-53, 30], [-45, 26], [-105, 60], [-15, 83], [203, 99], [-24, 47], [13, 29], [-37, 17], [61, 134], [-51, 162], [29, 36], [-145, 127], [-96, 138], [-3, 4], [-118, -40], [-70, 64], [-41, -20], [-34, 48], [-88, 12], [-110, -70], [28, -47], [-35, -68], [-96, -34], [-84, 58], [-188, -137], [-146, 107], [-50, 90], [-85, 47], [6, 51], [-69, 55], [15, 21], [-73, -24], [-26, 11], [-35, 30], [-59, 53]], [[7157, 12951], [34, -15], [-38, -87], [-78, -12], [-7, -73]], [[19199, 13243], [56, 239], [30, -15], [21, 40], [42, 9], [60, -64], [66, 33], [-24, 82], [-42, -16], [-9, 30], [-76, 23], [48, 121], [75, -20], [87, -81], [37, 21], [31, -51], [58, 34], [4, 58], [1, 27], [1, 0], [76, -21], [-53, -52], [20, -16], [56, 39], [-19, 29], [28, 43], [1, -4], [21, -62], [56, -13], [19, -31], [13, 5], [60, 77], [37, -45], [0, 0], [70, -32], [-6, -102], [26, -49], [3, -7], [-42, -22], [3, -43], [95, -22], [29, -150], [-91, -128], [45, -45], [25, 17], [36, -24], [-54, -55], [2, -58], [28, -19], [-23, -47], [121, -40], [35, -45], [2, -60], [93, -58], [-43, -99], [43, -37], [5, -54], [84, -38], [88, 0], [58, -73], [41, -7], [6, -36], [79, -41], [112, 39], [-2, -67], [-59, -45], [46, -31], [-1, -83], [41, 1], [2, 0], [3, -7], [29, -38], [-7, -22], [25, 0], [-15, -71], [48, -4], [40, -42], [-38, -45], [-20, -25], [-41, -14], [-6, -2], [-6, -2], [-46, 28], [-25, -31], [9, -35], [-38, -21], [-2, -73], [-122, -19], [-33, -38], [-50, 12], [-31, -30], [52, -21], [-5, -51], [60, 7], [53, 6], [9, 1], [0, -4], [6, -26], [3, -14], [-31, -78], [41, 25], [58, -45], [55, -2], [19, -31], [29, 42], [60, -24], [36, 86], [1, -1], [34, -17], [-9, -41], [56, -85], [47, -10], [18, 94], [2, 10], [18, -1], [12, -1], [4, 0], [31, -64], [-8, -4], [-25, -11], [21, -14], [-33, -5], [-5, -35], [-54, 14], [-119, -42], [-10, -38], [-29, 32], [-150, -109], [-26, 20], [-23, -47], [-238, -68], [-77, -42], [-3, -38], [-81, -25], [6, -49], [-82, -48], [80, -83], [9, -97], [56, 29], [16, -15], [11, -11], [14, -12], [-30, -37], [-9, -26]], [[20421, 10617], [-14, -3], [-3, -2], [-28, -33], [-138, -68], [-93, 15]], [[20145, 10526], [0, 0], [-3, 1], [11, 127], [-33, 31], [52, 86], [-18, 25], [-37, 9], [-111, -80], [-96, 117], [-129, -119], [-24, 49], [33, 43], [-106, 63], [-26, -29], [-52, 33], [-86, 113], [-15, -33]], [[19505, 10962], [-60, -11], [-41, 91], [3, 69], [-50, -16], [-72, 68], [33, 26], [-18, 93], [-29, 4], [-1, 85], [-70, 74], [11, 22], [70, -8], [3, 23], [-45, 33], [-64, 48], [3, 31], [44, 22], [-19, 43], [33, 19], [-88, 42], [-4, 51]], [[14067, 11045], [-7, 0], [23, 150], [-46, 11], [-12, 40], [-107, 9], [-4, 5], [-14, 14], [-14, 15], [-132, 8], [-70, -148], [-32, -174], [-37, -38], [-77, -12], [17, -172], [-43, 4], [-43, -63], [-112, 13], [-89, -170], [149, -42], [-21, -99], [-52, -54], [-36, -103], [-14, 11], [-7, -56], [-41, -25], [-21, 18], [-79, -74], [-2, 105], [-67, 72], [-200, -289], [-32, -31], [-92, -90], [-51, 47], [-31, -49], [-156, 3], [-1, -33], [-95, -98], [-1, -1], [-44, 40], [-57, -20], [-2, 0]], [[12315, 9769], [-1, -1], [-139, 38], [-121, -68], [5, -31], [-82, -49], [-7, 42], [-45, 17], [-44, 76], [-51, -34], [0, -84], [-102, 25], [-70, -34], [-18, 40], [-114, -10], [-36, 34], [-41, -45], [-186, 16], [-12, 10], [-14, 2]], [[9348, 12244], [125, -58], [14, 79], [103, 58], [49, -67], [-18, -54], [55, -70], [-8, -25], [102, -116], [57, -66], [15, -17], [11, -13], [3, -3], [53, 71], [-42, 27], [44, 100], [14, 16], [206, 153], [146, 47], [-23, 83], [90, 40], [-51, 55], [-4, 35], [6, 89], [50, 34], [-6, 52], [10, 28], [40, 52], [14, 12], [86, -39], [84, 19], [46, 83], [-34, 59], [115, 77], [24, -17], [88, -113], [139, 45], [94, -109], [89, 26], [110, 89], [5, 2]], [[19505, 10962], [-57, -12], [-197, 37], [-34, -39], [-62, 13], [-24, 29], [0, 124], [-130, 162], [-176, 74], [-18, 7], [-7, -4], [-68, -39], [-7, -148], [-71, -73], [18, -49], [-228, -135], [-180, -179], [-47, -11], [-51, 28], [-14, -48], [-72, 4], [-15, 27], [-160, -184], [-1, -1], [0, 0], [3, -2]], [[17907, 10543], [-55, -83], [25, -55], [-113, -178], [-39, -119], [19, -61], [95, 33], [78, -164], [-35, -67], [-41, -11], [-17, -83], [-77, -80], [-22, -104], [-162, 88], [-4, 76], [-40, 57], [-191, -39], [-15, 52], [-164, 64], [-8, 7]], [[17141, 9876], [1, 1], [-60, 52], [3, 51]], [[20145, 10526], [-4, -58], [67, -101], [-53, -1], [-14, -41], [30, -28], [94, 2], [2, 0], [-14, -49], [-34, 9], [-54, -77], [-103, -34], [-66, -74], [-87, 1], [-70, -73], [37, -139], [-3, -5], [8, -31], [26, -101], [2, -5], [43, 5], [88, 15], [9, -2], [38, -12], [18, -6], [6, -9], [23, -31], [8, -12], [-18, -52], [41, 3], [5, -56], [33, 0], [14, -34], [-1, -1]], [[20216, 9529], [-93, -51], [-45, -71], [-12, -34], [-32, -34], [-42, -45], [-44, -48], [-125, -78], [-32, -21], [-14, -8], [-68, 24], [-7, 1], [-6, -1], [-111, -31], [-24, -39], [-10, 9], [0, 0], [-46, -95], [-170, -34], [-64, -62], [-19, -60], [-130, -79], [-121, -29], [-1, 0], [-39, -129], [-87, -46]], [[18874, 8568], [-6, 54], [-35, 41], [-4, 4], [-15, 18], [-45, -8], [-40, 96], [-59, 22], [-80, 128], [-41, -1], [-56, 73], [-1, 2], [-88, -42], [-31, 49], [-72, 3], [-130, 90], [24, 21], [-11, 29], [56, 34], [-1, 81], [-137, -16], [46, 49], [-8, 98], [49, -7], [44, 58], [75, -54], [17, 71], [99, 29], [30, 48], [-27, 46], [19, 28], [233, 133], [22, 29], [-31, 29], [19, 25], [83, 46], [40, -22], [106, 92], [94, 39], [11, 101], [83, -46], [73, 26], [-5, 59], [-85, 10], [0, 8], [3, 11], [101, 55], [13, 7], [1, 0], [-2, 3], [-55, 62], [-18, 126], [-1, 4], [-90, 37], [-119, -41], [-36, -64], [-63, 13], [-115, -153], [-20, -27], [-33, -43], [-21, -18], [-59, 9], [-79, 20], [-88, 89], [-79, 3], [-3, 42], [-62, 54], [10, 39], [-38, 22], [-40, -42], [-288, 194], [-1, 0]], [[20421, 10617], [-1, -3], [78, 18], [2, 2], [27, 36], [15, 21], [0, 1], [-38, 55], [35, 163], [50, -9], [-10, -45], [23, -20], [125, 6], [87, 52], [5, -30], [25, 41], [140, 42], [30, 51], [28, -22], [23, 27], [41, -7], [6, 104], [19, 28], [54, -10], [18, 45], [84, -44], [44, 20], [102, -32], [119, -83], [32, 19], [-7, 78], [24, -30], [61, -2], [23, -38], [58, 36], [41, -48], [70, 39], [40, -49], [-15, -38], [29, -5], [-37, -39], [14, -23], [1, 2], [45, 57], [29, -4], [-18, 120], [83, -21], [43, -11], [31, -8], [1, -1], [2, 0], [-24, 63], [8, 26], [42, -12], [1, 0], [1, 9], [1, 20], [0, 5], [-17, 11], [-9, 7], [3, 3], [71, 21], [18, -1], [157, -50], [0, 0], [1, 0], [15, 17], [22, 25], [36, -55], [41, -3], [1, 26], [12, -11], [29, -30], [-98, -133], [-62, -16], [-25, 28], [-6, 7], [58, -137], [8, -4], [2, -4], [-11, -2], [-117, -15], [-4, -8], [-3, -6], [-8, -13], [44, -48], [4, -6], [-5, -36], [-1, -1], [-168, 29], [-15, 3], [-3, -10], [-18, -55], [-5, -16], [3, -3], [52, -54], [-70, -56], [-16, -151], [-40, -14], [-48, 39], [-141, 32], [-120, -103], [-128, -1], [-23, -126], [-122, -10], [-50, -45], [21, -55], [-31, -77], [-103, 5], [-99, -40], [-47, -104], [-130, 3], [-109, -71], [-50, -74], [-141, -51], [-52, -69], [-258, -23], [17, -120], [-162, 1], [-3, 1], [-12, 9]], [[8584, 8083], [37, -70], [-35, -54], [-7, -11], [-9, 4], [-12, -5], [-55, 30], [-62, -65]], [[8441, 7912], [-5, -5], [-50, 21], [-16, -65], [0, -1], [-63, -32], [-1, -3], [-5, -40], [80, -59], [10, -47], [-180, -153], [-7, -6], [29, -71], [52, -29], [4, -73], [-40, -24], [-71, 35], [-126, -3], [-15, -31], [-80, 23], [-110, -133], [-6, -38], [42, -54], [-100, -76], [-34, 8], [-7, -52], [-54, -16], [-56, -79], [-30, 7], [-74, -56], [-105, 9], [-67, 47], [-4, -42], [-44, -13], [-41, -148], [-58, -7], [-123, 85], [-80, -35], [-147, 93], [-142, -45], [-46, 128], [-85, 52], [-57, -23], [-142, -140], [-4, -62], [-153, -167], [79, -86], [-36, -105], [-121, -69], [35, -34], [-12, -27], [-125, -103], [-28, -170], [-32, -61], [-45, -16], [1, -48], [-38, -54], [35, -28], [-6, -41], [-90, 56], [-74, -16], [-28, -122], [-113, -113], [-125, 137], [-121, -34], [-81, -117], [-58, -28], [-17, 77], [-56, 32], [-37, -12], [-12, -50], [-35, 27], [-147, -80], [-68, 15], [-19, -41], [-103, -63], [20, -79], [-47, -46], [-31, 25], [-31, -45], [55, -58], [1, -73], [-61, -60], [-48, 9], [5, -23], [-82, -65], [-26, 18], [42, -146], [-130, -151], [-56, -139]], [[4364, 4585], [-194, 74], [-120, 121], [-99, 263], [-228, 374], [-126, 604], [-351, 374], [-38, 157], [-184, 186], [35, 371], [-29, 16], [17, 417], [-35, 75], [-33, 31], [-99, 7], [-201, -91], [-367, -28], [-303, 115], [-56, 140], [-186, 190], [-191, 317], [-42, 418], [148, 209]], [[18874, 8568], [-2, -3], [-27, -14], [-84, -104], [0, -3], [-5, -6], [-3, -75], [-8, -13], [-34, -36], [-77, -10], [-21, -2], [-6, -1], [18, -26], [8, -18], [-7, -10], [-58, -35], [-42, -20], [-135, 12], [-53, 6], [-1, -2], [-52, -109], [-98, 3], [-2, 0], [-6, -8], [-86, -82]], [[18093, 8012], [-1, -1], [-178, 8], [5, -74], [-95, -122], [-92, -12], [0, -1], [-2, 0], [-77, -50], [-21, -4], [-7, -14], [-19, -12]], [[17606, 7730], [-88, 109], [-23, 101]], [[17495, 7940], [10, 9], [-15, 14], [-7, 6], [-6, 5], [-41, -15], [-29, 47], [-23, -37], [-50, 66], [-4, -34], [-98, 74], [-4, 157], [82, 10], [0, 298], [-38, 17], [-23, -49], [-94, 13], [-20, -85], [-56, 11], [-49, -64], [-3, 3], [3, 12], [-93, 81], [-34, -41], [-53, 23], [73, 112], [-3, 33], [47, 12], [2, 1], [-5, 26], [-1, 6], [0, 1], [-2, 0], [-49, 1], [-5, 8], [10, 104], [2, 8], [-1, 2], [-5, 9], [-19, 32], [-27, 24], [-11, 10], [-3, 3], [-21, -48], [-68, -13], [-87, 20], [0, 129], [20, 1], [7, 69], [-63, 35], [27, 68], [96, 5], [-5, 85], [188, -32], [6, -1], [39, 30], [8, 7], [1, 0], [-25, 64], [88, 100], [-56, 95], [45, 42], [-28, 39], [55, 115], [5, 77], [0, 8], [-54, 54], [98, 66], [12, 8]], [[17495, 7940], [-57, -46], [-12, -47], [-49, 68], [-78, -31], [-52, 46], [-17, 72], [-56, 19], [-23, -65], [-40, -21], [-11, -159], [-54, 80], [-79, 6], [-4, 52], [-154, 22], [18, 147], [-107, 8], [-7, 42], [-103, -39], [-97, 12], [-12, -129], [-97, -24], [-13, 45], [28, 43], [-77, 74], [-45, -50], [-68, -9], [-26, -142], [-47, -7], [-27, 67], [-35, -16], [-32, -182], [-88, 14], [-47, -24], [-24, 22], [-96, -34], [-70, -126], [23, -17], [-51, -56], [-1, -1], [-61, 21], [-15, 52], [18, 20], [-84, -40], [-9, 5], [-61, 38], [-2, 1], [0, 1], [-40, 102], [-79, -48], [-32, 12], [-31, -46], [-6, 6]], [[15306, 7678], [-29, 29], [-24, 40], [-14, -1], [-80, 82], [-40, -73], [-36, 30], [-139, 19]], [[14944, 7804], [50, 97], [-2, 59], [36, 29], [-2, 1], [-46, 9], [-69, 73], [-106, -14], [6, 87], [-55, 43], [-1, 23], [8, 88], [59, 36], [77, -18], [11, 125], [119, 67], [-55, 127], [-68, 44], [63, 87], [24, 89], [-121, 39], [-45, -31], [-27, -101], [-47, -56], [-66, 79], [-78, 23], [1, 127], [-77, 65], [-11, 112], [-32, 21], [1, 48], [-66, 39], [-9, 86], [45, 19], [17, 64], [-30, 104], [49, 60], [-36, 75], [2, 8], [97, 39], [4, 124], [1, 3], [-3, 5], [-1, 3]], [[12315, 9769], [1, -1], [-12, -102], [44, -87], [0, -154], [-135, -124], [54, -74], [74, -26], [9, 0], [141, 115], [68, -39], [28, -37], [-75, -71], [16, -87], [-19, -55], [-70, 15], [-56, -42], [-45, 11], [-34, -47], [-38, 13], [0, -43], [74, -74], [-146, -156], [-18, -35], [19, -38], [-49, 11], [-114, -72], [-11, 88], [-37, 15], [-25, -77], [45, -91], [-111, -144], [80, -10], [-25, -41], [37, -49], [1, -1], [-61, -105], [-56, -23], [-24, -69]], [[11845, 8063], [-2, -6], [-136, -72]], [[11707, 7985], [-3, -2], [-35, 25], [-111, 81], [-71, -93], [-49, 7], [-3, -5], [-9, -86], [-51, -36], [17, -70], [-62, -77], [21, -125], [-17, -36]], [[11334, 7568], [-16, -36], [-99, -64], [-93, -197], [-55, -205], [-134, -209], [-29, -129], [-27, -40], [-57, 5], [-29, -61], [-133, -106], [5, -44], [-25, 14], [-23, -18], [-38, -43], [-11, -19], [28, -78], [-32, -25], [-37, -162], [-55, -19], [-25, -57], [-166, -127], [-17, -37], [-7, -17], [-1, -1]], [[10258, 5893], [-10, -2], [-193, -42], [-83, -129], [-13, -28], [-13, -12], [-59, -55], [5, -62], [-41, -45], [52, -41], [-50, -153], [0, 0], [-30, -5], [-69, -102], [-24, 27], [-28, -13], [-6, -62], [-74, -83], [-19, -113], [-60, 3], [-161, -106], [0, 0]], [[9382, 4870], [63, 87], [1, 27], [-7, 71], [-51, 94], [-59, -24], [-50, 60], [-70, 8], [-57, 73], [-43, 2], [-122, 101], [11, 114], [107, 31], [40, 105], [140, 87], [54, 78], [21, 90], [-242, 189], [-15, -58], [26, -108], [-66, -46], [-57, 32], [-9, 131], [-30, 9], [-14, -33], [-74, 15], [-19, 125], [39, -12], [3, 34], [38, -1], [-2, 77], [-9, 9], [-69, -14], [-18, 66]], [[8842, 6289], [-1, 6], [8, 1], [105, 14], [16, 2], [2, 5], [23, 96], [-57, 146], [-93, 73], [51, 42], [47, -11], [45, -67], [39, 26], [-43, 63], [0, 0], [10, 18], [9, 16], [2, 4], [47, -34], [45, 38], [35, 52], [30, 58], [1, 3], [-19, 35], [97, 65], [-20, 69], [-83, 50], [0, 1], [7, 139], [-189, 139], [-24, 57], [-89, 31], [5, 34], [-48, 66], [32, 42], [-100, 11], [-21, 37], [37, 24], [-22, 75], [-170, 81], [-101, 101], [-14, 15]], [[15306, 7678], [-7, -7], [9, -15], [-97, -78], [35, -94], [-12, -92], [0, -1], [-187, -247], [27, -98], [-16, -63], [0, -1], [-98, -73], [-55, -103], [-46, 42], [-2, -2], [-55, -53], [2, -18], [9, -20], [49, 0], [45, -71], [46, -43], [54, -116], [61, -15], [202, -155], [21, -179], [-14, -137], [-113, -150], [123, -155]], [[15287, 5734], [-14, -73], [-89, -9], [-152, -15], [-1, 11], [0, 0]], [[15031, 5648], [-3, 42], [-141, 18], [-223, -15], [-28, -2], [-12, -1], [-1, 9], [-3, 2]], [[14620, 5701], [2, 7], [-5, 43], [37, 101], [-202, 44], [-5, 60], [-111, 0], [-44, -31], [-82, 23], [-20, 33], [-127, -87], [-34, 103], [-53, 31], [-24, -79], [-28, 52], [-68, 3], [45, 99], [-48, 28], [-82, -41], [-54, -80], [-98, 20], [-11, 3], [4, 19], [-13, 38], [-19, 9], [-12, 79], [-66, 37], [-34, -117], [-65, -3], [34, 33], [-15, 59], [-28, -4], [-50, -79], [-88, 14], [-113, -119], [-93, 23], [-35, -49], [-70, 44], [45, 88], [-117, 15], [6, 56], [-40, -1], [-82, -106], [-37, 36], [-127, 30], [-5, 1], [1, 46], [-62, -33], [35, -34], [-4, -40], [-19, -39], [-50, 11], [-35, -106], [-53, 10], [-74, -76], [-30, 8], [-26, -98], [-27, -2]], [[12244, 5783], [-12, 3], [-68, 3], [-19, -6], [-5, -8], [8, -38], [-93, 21], [-3, 0], [-1, 90], [-20, 5], [22, 22], [-60, 84], [-170, 32], [-14, 39], [-86, -25], [37, 109], [67, 15], [59, 164], [-45, -5], [-22, -21], [-40, 15], [-33, 12], [-49, -13], [-7, -3], [-21, 70], [-1, 3], [-24, -1], [19, 105], [-37, 51], [58, -46], [83, -1], [9, 45], [-8, 17], [-15, 4], [-88, 21], [-41, 51], [12, 48], [-37, 68], [1, 0], [47, -5], [22, 35], [90, 26], [3, 4], [-50, 25], [10, 62], [-106, -4], [-5, -1], [-5, 42], [32, 33], [67, -3], [-40, 31], [10, 73], [132, -20], [-23, 25], [55, 39], [-62, 43], [8, 86], [-76, 30], [168, 112], [-17, 60], [36, 52], [-124, 87], [11, 77], [62, 54], [225, 49], [-21, 73], [54, 29], [-26, 75], [37, 20], [-49, 34], [-58, -24], [33, 106], [-3, 1]], [[12037, 8039], [27, 14]], [[12064, 8053], [49, 6], [37, -35]], [[12150, 8024], [147, 118]], [[12297, 8142], [21, 17], [37, 18], [26, -28], [72, 43], [4, 53], [-47, 8], [1, 41], [152, 75], [0, 0]], [[12563, 8369], [9, -10], [75, -73], [21, -11], [139, -5], [105, -49], [22, -79], [-23, -49], [38, 17], [10, 12], [14, -1], [56, -2], [16, 34], [-48, 41], [5, 34], [114, 4], [32, -30], [235, 47], [235, -138], [137, 20], [88, -22], [34, 27], [104, -56], [103, 3], [32, -58], [58, 46], [61, -37], [-10, -43], [-72, -65], [71, -65], [99, -13], [-22, -121], [57, -49], [-10, -54], [59, -21], [21, 30], [56, -9], [16, 51], [38, 7], [130, -94], [106, -19], [21, 54], [40, -10], [2, 66], [0, 1], [107, 124], [0, 0]], [[8842, 6289], [-48, 42], [-67, -27], [-11, -4], [-9, 8], [-32, 7], [-101, 97], [-41, -54], [-8, -26], [-11, 0], [-70, -95], [0, -99], [0, -1], [-30, -30], [-56, -57], [-20, -93], [-69, -54], [-19, -74], [-35, -18], [-5, -84], [-66, -1], [-37, -87], [-28, 1], [-9, -46], [-171, -186], [-129, 1], [-55, -54], [20, -105], [-63, -28], [48, -150], [-23, -34], [6, -137], [-2, 0], [-194, -15], [-37, 61], [-69, -29], [-157, -265], [11, -67], [50, -50]], [[7305, 4536], [0, 0], [-39, -44], [-45, 3], [-82, 54], [-41, -37], [-61, 44], [-2, 2], [-91, -51], [5, -24], [-81, 19], [-38, 64], [-66, -14], [-89, 58], [-13, 137], [0, 2], [-67, 16], [-118, -63], [-48, 12], [-25, -26], [11, -82], [-124, -68], [-62, 45], [8, 65], [-120, 28], [-36, -44], [19, -103], [-33, -62], [-119, 51], [12, 84], [-49, 61], [-141, -49], [-73, 4], [-135, -65], [-121, 110], [-45, -28], [-44, 48], [-80, -23], [-29, 21], [-91, -7], [-45, -4], [-31, -19], [-226, -136], [-48, 15], [-28, 86], [-87, -37], [-66, -92], [0, 0], [-1, 0], [-40, 15], [-216, 83]], [[17606, 7730], [0, -1], [-41, -97], [-5, -13], [-2, 1], [-1, -3], [-98, -11], [-7, -8], [3, -78], [56, -55], [-71, -53], [10, -112], [-17, -31], [-5, -103], [-11, -18], [0, -9], [-45, -67], [-9, -13], [0, -1], [-68, -4], [-75, -58], [-38, 42], [-14, -7], [-8, -7], [-21, -153], [-39, -19], [-7, -58], [-60, -34], [52, -44], [-1, -1], [-44, -44], [53, -39], [-5, -48], [-6, 1], [-60, 11], [-54, -48], [-60, 61], [-44, -9], [-14, -65], [-101, -38], [30, -97], [-85, -61], [-23, -76], [-5, 5], [-137, 153], [-179, -158], [-139, -2], [-23, 23], [35, 16], [-20, 35], [-68, 24], [-36, -165], [-113, -96], [29, -72], [-50, -60], [53, -39], [17, -13], [2, -1], [-8, -10], [-124, -155], [-41, -53], [-31, -7], [-65, -52], [-58, 28], [1, 15], [-58, 44], [-35, 14], [-102, -35], [0, -1], [-10, -66]], [[15506, 5635], [0, 0], [-231, 23], [12, 76]], [[18093, 8012], [44, 28], [-71, -107], [-39, -55], [0, -3], [4, -182], [-56, -1], [-70, -54], [3, -14], [10, -47], [3, -11], [2, -1], [1, -3], [60, -37], [-18, -79], [36, -41], [-14, -141], [37, -93], [-9, -57], [0, 0], [100, -128], [0, -86], [1, -1], [20, -14], [57, -44], [9, -23], [12, -48]], [[18215, 6770], [-54, -3], [-51, 41], [-83, -13], [-62, 39], [0, -1], [-17, -42], [-123, -21], [-10, -67], [-131, 24], [-23, -25], [-30, 31], [-60, -48], [-50, 9], [-1, 0], [-97, -381], [-92, -24], [0, -6], [-2, -158], [-33, -4], [-4, -52], [-57, -18], [-2, -4], [-9, -16], [-14, -25], [-14, -54], [64, -145], [204, -328], [60, 9], [62, 69], [116, -8], [5, -46], [1, -8], [6, 14], [28, 23], [32, -21], [41, -34], [15, -76], [65, -2], [40, -90], [46, -12], [4, -27], [-2, -7], [-13, -6], [-20, 10], [4, -19], [-25, -12], [13, -47], [-122, -1], [4, -81]], [[17824, 5107], [-59, -5], [1, 28], [-7, 2], [-8, 2], [-19, 6], [-17, -23], [-103, 104], [-69, -20], [-47, -79], [-68, -7], [11, -46], [-25, -32], [-42, 9], [10, 93], [-45, -7], [5, -37], [-83, 6], [16, -45], [-20, -94], [-47, -75], [-125, -28], [-2, 2], [-30, 55], [0, 23], [33, 68], [-40, 11], [-58, -103], [-128, -62], [-12, -26], [-4, -9], [-1, -3], [108, -111], [-98, -67], [-16, -140], [46, -137], [-44, -27], [-27, 23], [-48, -57], [-164, 37], [-28, -59], [-63, 13], [-17, 3], [0, -16]], [[16490, 4277], [-8, -6], [-17, 7], [-11, 9], [21, 74], [-93, 32], [-18, 66], [-57, 13], [-35, -36], [3, 22], [-28, 22], [49, 39], [10, 59], [0, 0], [-2, 45], [-59, 12], [21, 175], [1, 10], [-81, 72], [-30, 3], [-14, 12]], [[16142, 4907], [18, 31], [26, 96], [20, 69], [8, 30], [-3, 87], [-160, 46], [-120, 91], [-74, 58], [-65, 113], [-81, 38], [-16, 73], [-67, 24], [-122, -28]], [[9382, 4870], [0, 0], [-11, -200], [-140, -139], [-1, -77], [-35, -66], [-64, -117], [-69, -64], [-22, -79], [-32, -23], [2, -8], [0, -3]], [[9010, 4094], [-84, -82], [-1, -1], [34, -49], [-80, -101], [17, -15], [46, -32], [0, -8], [1, -14], [0, -18], [-39, -21], [-1, -1], [-124, -69], [-51, 57], [-96, 42], [-4, -1], [-6, 2], [-248, -18], [-62, 4], [-101, 129], [-15, 150], [-275, 43], [-95, 118], [-14, -46], [-34, -10], [-40, -121], [-29, 2], [18, -121], [-110, -6], [-70, 188], [29, 101], [-64, 17], [-71, -38], [-89, 161], [-59, 16], [-5, -49], [-87, -16], [-24, 53], [-53, -14], [-47, 73], [75, 63], [55, 4], [42, -43], [57, 14], [1, 2], [28, 83], [0, 1], [-2, 0], [-28, 13]], [[18215, 6770], [8, 0], [3, -46], [64, -45], [43, 20], [76, -100], [79, 8], [74, -66], [83, -26], [244, 38], [115, -27], [70, -59], [-16, -51], [35, -6], [23, 1], [34, 23], [37, 25], [149, -16], [36, 71], [65, 3], [1, 1], [30, 45], [108, -17], [101, -64], [114, -6], [-9, 59], [30, 8], [-6, -28], [21, -3], [29, 43], [-26, 5], [25, 126], [37, 5], [109, 121], [59, 25], [137, -61], [4, -9], [-47, -63], [0, -10], [-2, -4], [3, -3], [39, -54], [-62, -112], [66, 18], [5, -7], [27, -89], [1, -5], [-18, -53], [-50, -3], [2, -29], [144, -114], [39, -104], [-68, -11], [-18, -71], [-6, -105], [41, -32], [-35, 22], [-91, -74], [-87, -21], [-23, -6], [-4, -11], [-5, -3], [-96, 95], [-10, 9], [-80, -25], [-164, 14], [-19, -78], [-44, 5], [-5, 25], [-65, -82], [-69, -4], [-26, -30], [-218, 57], [-66, -24], [-17, -45], [-235, -27], [-2, -98], [-48, -14], [28, -55], [-15, -64], [71, -79], [2, -42], [76, -8], [-1, -7], [-18, -75], [95, -54], [-51, -61], [-4, -1], [-5, -2], [-73, -25], [-178, -6], [-73, -34], [11, -48], [108, -109], [1, -2], [4, -12], [19, -62], [90, 9], [41, -44], [102, 78], [14, -2], [10, -1], [20, -3], [27, -59], [57, -11], [6, -42], [85, -25], [7, -2], [78, -186], [1, -29], [7, -208], [1, -12], [-36, -89], [-46, 2], [-12, -54], [-6, -33], [-1, -1], [-45, -13], [-18, -46], [-2, -7], [-52, -3], [-15, -33], [-116, 33], [-17, 4], [-13, 15], [-6, 7], [-4, 4], [2, 174], [-41, 7], [-45, -70], [-56, 80], [-97, -38], [-20, -8], [94, -192], [-21, -88], [-3, 0]], [[18855, 3969], [-67, -5], [-3, -23], [-144, 60], [-125, -127], [-87, 102], [-89, -73], [-90, 30], [-35, 85], [-51, 30], [14, 78], [-83, 19], [-31, 39], [1, 149], [-45, 60], [29, 221], [-104, -31], [-32, 28], [-4, 15], [-76, 234], [-9, 246], [0, 1]], [[10258, 5893], [21, -16], [14, -11], [6, -46], [-83, -154], [46, -56], [33, 50], [74, -34], [-63, -106], [66, -60], [10, -112], [79, -4], [2, -59], [37, 18], [66, -84], [95, 35], [110, -58], [-13, -69], [-48, -9], [-44, -114], [13, -49], [49, -25], [-64, -58], [20, -57], [34, -10], [-5, 32], [54, 21], [25, -21], [-11, 30], [58, 61], [-39, 22], [12, 35], [73, -86], [27, -83], [38, -6], [6, -41], [57, 32], [17, 53], [47, 9], [-2, 20], [-45, -23], [-29, 27], [14, 49], [-22, 22], [34, 70], [58, -5], [-29, 31], [62, 58], [-71, 88], [108, 126], [149, -91], [43, 48], [39, -21], [33, -110], [46, 24], [-21, 133], [111, 57], [64, -79], [91, 41], [35, -104], [79, -21], [63, 59], [2, 2], [45, -13]], [[11934, 5251], [0, 0], [-14, -66], [-83, 11], [64, -53], [-57, -102], [-1, -129], [29, -33], [-36, -50], [170, -68], [4, -70], [-44, -5], [-6, -53], [48, 7], [30, -37], [45, 19], [13, -55], [-24, -85], [42, 4], [33, 96], [44, -25], [24, -70], [-98, -3], [4, -67], [53, 23], [21, -44], [67, -23], [-22, -84], [30, -33], [-11, -59], [-50, 0], [2, -41], [-31, 7], [11, -58], [-65, -6], [-49, -99], [82, -53], [52, 76], [27, -6], [0, 38], [-23, -3], [11, 56], [30, -19], [1, -69], [38, 68], [21, -40], [44, -6], [31, 66], [52, -3], [-31, -77], [-51, -44], [-1, -100], [87, 27], [48, -44], [13, -84], [-60, -7], [1, -50], [-26, -23], [68, -34], [21, -93], [25, -5], [-29, -42], [151, 12], [1, -3], [5, -21], [8, -26], [0, -36], [-2, -1]], [[12671, 3454], [-131, -64], [-9, -22], [54, -87], [-46, -74], [-58, -14], [-144, 85], [-156, 30], [-57, -19], [30, -103], [73, -55], [18, -58], [-107, -75], [30, -80], [75, -84], [4, -5], [10, -54], [-71, -120], [77, -91]], [[12263, 2564], [-10, -42], [-1, -1], [-95, -60], [-27, -17], [0, 0], [-52, 136], [-89, 54], [-25, 106], [-4, 3], [-101, -65], [-45, 8], [-61, -71], [-73, 38], [-119, -33], [-208, 50], [-124, -78], [-27, -64], [-47, -7], [-133, 42], [-28, -37], [-60, 37], [-43, -26], [-109, 52], [-56, -51], [-20, 30], [-33, -20], [-28, 11], [-23, 9], [-95, 37], [-27, -1], [-93, -75], [-5, -4], [-2, -2], [-76, -13], [-82, -174], [-29, 1], [-18, -86], [-86, -8], [-72, -100], [-58, 13], [-29, 20]], [[9950, 2176], [-7, 28], [-14, 59], [-9, 38], [-1, 5], [21, 42], [36, 85], [1, 0], [15, 20], [8, 16], [4, 0], [4, 6], [63, 21], [13, 14], [-1, 4], [-17, 22], [4, 134], [-42, 13], [24, 52], [-8, 95], [-112, 115], [2, 68], [-60, -17], [-31, 31], [-24, -161], [-52, -28], [-36, -71], [-44, 3], [-58, -52], [-34, 57], [-93, 54], [53, 106], [-141, 60], [-12, 38], [-74, 38], [-125, 157], [-23, 44], [30, 30], [-2, 41], [48, 34], [-22, 11], [-2, 0], [6, 9], [25, 43], [19, 32], [37, 19], [8, 7], [27, 102], [87, -23], [18, 108], [14, 16], [43, -33], [52, 93], [-52, 37], [-71, -61], [-48, -4], [-194, 121], [68, 105], [-3, 109], [-27, 13], [12, 43], [-23, 22], [-134, -97], [-80, 7], [-1, 0], [-5, 38]], [[18855, 3969], [17, -37], [-33, -43], [36, -109], [-49, -36], [23, -103], [38, -52], [17, -90], [66, -82], [60, -20], [26, -76], [64, -30], [-30, -50], [-50, 11], [-38, -171], [-52, 6], [-1, 3], [-28, 6], [-173, -49], [-117, 152], [-78, -2], [-17, -28], [-53, 28], [-58, 65], [67, 145], [-17, 119], [-74, -58], [-60, 20], [-25, -34], [-44, 1], [-13, -57], [-68, 16], [26, -60], [-63, -40], [-16, -52], [33, -38], [-126, -60], [-36, 25], [-23, 108], [-62, 14], [-25, 52], [-48, -49], [-91, 34], [11, -36], [-21, -23], [-141, 113], [-92, 17], [-88, -45], [-16, -67], [-137, 24], [-39, -15], [0, 0], [-45, 183], [25, 77], [-14, 22], [-35, 63], [-29, -64], [-24, -1], [-6, -64], [-49, -5], [-17, -35], [30, -113], [-79, -107], [-3, -27], [-6, -55], [18, -16], [9, -4], [0, -4], [1, -1], [16, -117], [-13, -11], [3, -72], [-218, -64], [-84, -119], [-118, -17], [10, -79], [45, -50], [-9, -129], [-66, -24], [-46, -69], [-109, -21], [-23, -30], [-39, 33], [-45, -9], [-32, -37], [-20, 15], [-38, -34], [-62, -3], [-30, -52], [-42, 5], [-3, 71], [-30, 35], [-45, 32], [-75, 2], [-11, -76], [89, -47], [-44, -44], [6, -85], [-81, 5], [30, -78], [-30, -113], [0, -1], [-37, 101], [-41, 7], [-13, 40], [-182, -10], [14, 43], [-58, -1], [-18, 27], [6, 39], [46, 8], [-39, 62], [-152, 90], [-14, 22], [-70, 48], [-26, 7], [2, 9], [28, 182], [0, 3], [121, 123], [0, 0], [0, 0], [191, -99], [114, 23], [79, 58], [4, -5], [39, -32], [-2, -48], [30, -2], [8, -62], [40, 1], [61, 29], [3, 45], [59, 1], [-7, 49], [28, -7], [24, 41], [0, 62], [138, 40], [-5, 100], [-47, -18], [-28, 40], [-32, -22], [-27, 70], [-31, -2], [-50, 81], [59, 107], [6, 80], [138, 72], [-40, 32], [10, 104], [-20, 44], [-113, 7], [-68, 99], [-5, 6], [5, 122], [-28, 3], [-3, 53], [70, 29], [-20, 51], [99, 84], [8, 53], [63, -29], [-10, -80], [66, -55], [166, 79], [28, 141], [1, 2], [-60, 46], [-1, 0], [7, 31], [48, -14], [8, 6], [21, 41], [0, 20], [0, 18]], [[12263, 2564], [3, 2], [30, 15], [42, 22], [43, -62], [119, -12], [33, -93]], [[12533, 2436], [-41, -8], [-7, -1], [0, -1], [4, -47], [-59, -19], [-77, -73], [-88, -59], [-97, -54], [-78, -42], [-143, -44], [-3, -1], [-1, -4], [-16, -37], [-19, -44], [9, -50], [-44, -43], [39, -86], [-19, -43], [21, -95], [-27, -21], [-5, -79], [-135, -27], [-6, -92], [-74, -54], [-47, -120], [-24, -111], [19, -77], [-132, -164], [-18, -13]], [[11465, 927], [-55, -37], [-10, -82], [-1, -3], [-38, -3], [-70, -5], [-4, -1], [0, 12], [-33, 87], [-120, 163], [-4, 61], [-108, -50], [-79, 70], [-89, -128], [-39, 86], [-19, 44], [-5, 3], [-125, -57], [-52, 35], [35, 138], [40, 32], [-60, 61], [9, 103], [34, 10], [-17, 76], [32, 78], [-103, 89], [-110, 4], [-127, -135], [-75, 74], [39, 34], [-31, 14], [27, 155], [-87, 7], [-5, 41], [-57, 44], [-4, 52], [-60, 25], [-28, 51], [-33, 17], [-76, -57], [-1, 1], [-47, 74], [41, 66]], [[12533, 2436], [2, -6], [46, 2], [116, 29], [18, 20], [84, 15], [13, -7], [7, -4], [6, -3], [-3, -45], [106, -76], [-35, -128], [79, -16], [-9, -65], [43, -3], [-23, -140], [27, -51], [-93, -23], [34, -67], [0, -97], [-54, -110], [228, 16], [33, 41], [75, -31], [33, 90], [1, 3], [5, -4], [27, -20], [40, -30], [23, -67], [94, -59], [30, -1], [39, -42], [28, -30], [6, -6], [-4, -5], [-33, -39], [37, -59], [1, -3], [-21, 18], [5, -41], [4, 2]], [[13548, 1394], [-27, -62], [-73, 1], [-16, -76], [-65, 25], [-2, 1], [-3, -3], [-48, -46], [-8, -8], [-67, -64], [-48, 21], [-2, -23], [-72, -12], [15, -17], [-28, -66], [-68, -23], [-9, -128], [-29, -42], [-46, 0], [0, -78], [-66, -19], [2, -35], [39, 14], [43, -126], [71, 52], [27, -53], [62, 16], [76, -78], [97, 54], [59, -16], [39, -91], [-60, -32], [27, -33], [78, 9], [3, 10], [38, 9], [1, 1], [-2, -2], [-84, -95], [-113, 27], [-166, -166], [-172, -38], [14, -58], [-100, -69], [-107, -10], [-81, 67], [-119, 19], [-114, -126], [-55, -25], [-2, 18], [-89, 80], [0, 2], [22, 67], [-59, 130], [-59, 64], [-113, 17], [-56, -112], [-77, 40], [-59, 31], [-2, 18], [-2, 29], [-2, 34], [39, 54], [-5, 124], [-120, 92], [-138, -13], [17, 44], [-30, -8], [-11, 74], [-120, 18], [-15, 84], [-26, 29], [-17, 11]], [[11707, 7985], [14, -33], [76, -82], [9, -60], [-114, -45], [-34, 12], [-69, -44], [-79, -88], [90, -16], [-1, -8], [-1, -5], [-1, -12], [-47, -35], [-8, -8], [-8, -46], [-98, -44], [-11, -4], [-29, 81], [-62, 20]], [[12563, 8369], [-1, 1], [-5, 122], [-80, 38], [-66, -11], [-17, -9], [-3, -35], [-100, -110], [45, -134], [-30, -8], [-29, 112], [-27, -49], [-97, 2], [95, -175], [49, 29]], [[12150, 8024], [73, 75], [-79, 73], [-16, -4], [-64, -115]], [[12037, 8039], [-10, -6], [-118, 40], [-30, 8], [-34, -18]], [[12244, 5783], [-5, 0], [44, -16], [14, -12], [26, -53], [59, -16], [3, -6], [-38, -109], [-32, 27], [-77, -56], [-68, 39], [-100, -26], [-2, -93], [-40, -86], [-55, -27], [-26, -82], [-13, -16], [0, 0]], [[16142, 4907], [-70, -1], [-1, 0], [-189, -107], [-66, -89], [-15, -6], [-56, -22], [-8, -4], [-45, -2], [-67, 91], [-86, -29], [0, 0], [-40, 26], [-113, -72], [-88, -19], [-489, 106], [-93, 1], [-23, -44], [-4, 9], [-52, 109], [-33, 15], [80, 155], [94, 31], [-9, 49], [-61, 12], [-2, 28], [138, -11], [84, -113], [45, 52], [205, 89], [30, 38], [9, 35], [-6, 21], [-123, -43], [-120, 21], [-45, -55], [-117, 55], [9, 70], [70, 28], [-90, 150], [67, 14], [-9, -37], [59, -71], [23, -124], [57, 195], [-51, 69], [-5, 7], [-2, 2], [1, 5], [9, 65], [1, 10], [7, 4], [11, 7], [35, 20], [33, 1]], [[14620, 5701], [0, -2], [-42, 28], [-54, -40], [34, -85], [-88, -56], [24, -15], [-31, -26], [-41, -7], [8, -256], [-75, -21], [3, 23], [-31, 6], [-27, -36], [-26, 11], [11, -25], [-54, -52], [-206, 37], [-27, 18], [13, 46], [-54, -15], [-40, 31], [-35, 127], [-55, 21], [-29, -36], [-28, 109], [-41, 18], [-24, -27], [-20, -174], [84, -170], [0, -3], [5, -76], [1, -21], [0, 0], [-59, -24], [22, -47], [10, -13], [27, 8], [0, 56], [48, -4], [89, -81], [41, 28], [8, -3], [4, 2], [109, -39], [26, 7], [37, 10], [12, 3], [24, -115], [-62, -1], [0, -110], [24, -20], [-106, -22], [3, -65], [-49, -26], [3, -27], [-98, 9], [-9, -24], [-18, 33], [-15, -30], [-135, 209], [-69, 215], [-24, -5], [11, -127], [-63, -11], [20, -79], [-40, -122], [99, 14], [1, -2], [9, -15], [4, -8], [-5, -5], [-88, -117], [18, -85], [-110, -42], [31, -106], [-29, -6], [1, -55], [-65, -17], [-11, -14], [-45, -56]], [[13356, 4104], [-3, -4], [-6, 2], [-138, 44], [6, -34], [-35, -30], [-26, 86], [-9, 3], [-57, 8], [-71, -57], [-2, -2], [-19, -176], [-99, -15], [36, -184], [-11, -210], [-25, 1], [-84, -173], [-15, -8], [-20, -12], [-8, -4], [-6, 5], [-62, 65], [-30, 30], [-1, 15]], [[11674, 17150], [-38, -45], [-17, -20], [5, -39], [13, -116], [-140, -91], [-36, 122], [26, 91], [-151, -46], [-79, -59], [-82, -7], [16, 37], [-24, 53], [-31, -8], [-16, 60], [-68, 54], [6, 90], [-76, 39], [0, 0], [-78, -133], [-9, 43], [-86, 14], [-32, 37], [-62, -60], [-126, 55], [-140, -104], [-110, -14], [-97, 35], [-37, -67], [-14, 60], [-323, 28], [4, -27], [-80, -93], [-69, -11], [4, -91], [-106, 0], [-14, -52], [-65, 11], [0, -83], [-47, -13], [79, -26], [-21, -20], [34, -13], [-39, 0], [-14, -52], [-55, 0], [-17, -39], [-68, 19], [13, -57], [-53, -34], [14, -92], [-107, 6], [8, -45], [-80, 6], [-1, -48], [-99, -10], [13, -46], [-72, -20], [-389, 0], [0, 39], [92, 1], [0, 45], [-118, 13], [-139, 72], [-112, 0], [-15, 39], [29, 54], [-106, 5], [-13, 52], [-53, -20], [-1, 57], [-25, 0], [-1, 113], [-39, 13]], [[8140, 16812], [0, 51], [182, 108], [545, 231], [151, 361], [206, 239], [507, 969], [293, 1077], [513, 211], [536, 141], [413, 363], [13, 12], [6, -225], [-182, -201], [-45, -110], [21, -110], [1030, -53], [1, -3], [3, 0]], [[12333, 19873], [-2, -84], [-46, -14], [-67, -78], [12, -68], [48, 15], [13, -94], [-136, -3], [9, -95], [-57, -20], [-3, -87], [-26, -3], [3, -101], [-47, -21], [7, 64], [-126, 0], [-8, -95], [-32, -2], [-20, 40], [-80, 6], [0, 21], [-73, -32], [-26, 27], [-60, -54], [-100, 0], [0, -51], [-52, -1], [-1, -83], [-28, -21], [-25, 40], [-33, 0], [32, -35], [-125, 13], [-147, -45], [0, -38], [67, -49], [8, -51], [-33, -54], [84, 1], [74, 60], [60, -47], [9, 57], [27, -6], [-10, -69], [45, 0], [2, -56], [-123, 0], [-1, -26], [118, -4], [-45, -81], [38, -39], [-35, -16], [51, -19], [12, -58], [-26, -27], [-21, 27], [7, -76], [-26, 0], [1, -24], [-143, -1], [1, -81], [52, -20], [-23, -117], [109, -8], [113, 86], [49, -192], [41, -16], [-2, -119], [68, 0], [119, -84], [41, 107], [83, 1], [62, 48], [6, 40], [93, 12], [7, 33], [86, -40], [19, -73], [-77, -13], [-75, -55], [74, -103], [-9, -32], [-51, -18], [44, -25], [-28, -90], [20, -37], [-11, -66], [-94, -31], [-8, -80], [-72, -83], [-6, -42], [-1, -8], [-51, -2], [-78, -103], [-104, -55]], [[11979, 16478], [0, 9], [-55, 51], [-3, -1], [-205, -57], [90, 144], [88, 39], [1, 1], [-22, 18], [35, 84], [-25, 63], [-25, -5], [6, 123], [-122, -11], [22, 82], [-54, 101], [-36, -2], [0, 33]], [[12333, 19873], [0, 3], [384, -24], [2, 0], [201, -12], [-2, -5], [-42, -95], [86, -20], [44, -91], [-56, -84], [-80, -22], [-58, -53], [-26, -120], [3, -37], [10, -43], [138, -14], [73, 44], [35, -21], [29, 54], [74, -43], [-79, -281], [15, -43], [-25, -36], [-17, -181], [123, -96], [-3, -26], [-53, -61], [-42, -122], [-111, 14], [-7, -19], [-5, -93], [76, -12], [30, -92], [64, -68], [-10, -108], [142, 16], [9, 112], [157, -5], [51, -46], [59, 1], [65, 20], [84, 103], [77, 19], [225, -120], [4, -64], [54, -57], [-32, -54], [33, -60], [96, -29], [54, 45], [77, 0], [82, -168], [96, -14], [13, 49], [98, 12], [30, 20], [-15, 22], [50, 27], [56, -96], [73, -13], [3, 84], [14, -26], [100, -4], [11, 70], [47, 4], [41, 43], [59, -18], [-45, -138], [58, -30], [64, 87], [76, 15], [5, -14], [4, -13], [8, -24], [-39, -73], [30, -12], [15, -83], [-20, -42], [-65, -3], [-7, -77], [-18, -3], [0, -1], [11, -8], [65, -44], [12, -7], [-15, -15], [-23, -151], [130, -15], [26, -53], [-16, -89], [69, 17], [73, -17], [39, -9], [14, -4], [14, -94], [0, -1], [-3, -3], [-7, -8], [-32, -62], [2, -10], [-7, -1], [-8, -16], [-73, -4], [-8, -25]], [[6138, 13992], [-13, 121], [60, 16], [75, 462], [175, 249], [349, 302], [189, 234], [239, 794], [267, 328], [194, 83], [467, 281], [0, -50]], [[13356, 4104], [77, 16], [43, -58], [83, -24], [26, 34], [72, -10], [18, -45], [-21, -47], [98, 21], [6, 2], [1, -37], [1, -18], [0, -4], [-77, -70], [-9, -59], [-37, 4], [-13, -64], [-54, -58], [1, -140], [0, -2], [-81, -95], [12, -41], [290, 3], [75, -46], [1, -21], [27, -24], [-1, -1], [-72, -108], [43, -46], [18, -140], [38, -33], [7, -58], [50, -22], [-2, -46], [88, -41], [22, -40], [-44, -55], [-16, -100], [-71, -42], [-39, -77], [12, -33], [-47, -133], [29, -89], [-26, -139], [53, 7], [41, -92], [0, 0], [0, 0], [-41, -24], [31, -77], [-41, -29], [3, -71], [-56, -31], [4, -3], [53, -48], [37, -109], [-55, -30], [-31, 18], [0, 1], [-142, -210], [-114, -56], [-22, 53], [-55, -20], [-1, -3]]], "transform": {"scale": [0.0003903291125717814, 0.00034696152944500304], "translate": [69.484227, 23.059220531669062]}, "objects": {"districts": {"type": "GeometryCollection", "geometries": [{"arcs": [[0, 1, 2, 3, 4, 5]], "type": "Polygon", "properties": {"dt_code": "102", "district": "<PERSON><PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-2, 6, 7]], "type": "Polygon", "properties": {"dt_code": "103", "district": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[8, 9, 10, 11, 12, 13]], "type": "Polygon", "properties": {"dt_code": "114", "district": "Jaisalmer", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-3, -8, 14, 15, 16]], "type": "Polygon", "properties": {"dt_code": "111", "district": "<PERSON><PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[17, 18, 19, 20]], "type": "Polygon", "properties": {"dt_code": "104", "district": "Alwar", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-16, 21, -21, 22, 23, 24, 25]], "type": "Polygon", "properties": {"dt_code": "110", "district": "Jaipur", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[[-12, 26, 27, 28, 29]], [[-10, 30]]], "type": "MultiPolygon", "properties": {"dt_code": "113", "district": "Jodhpur", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-19, 31, 32, 33, 34]], "type": "Polygon", "properties": {"dt_code": "105", "district": "Bharatpur", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-4, -17, -26, 35, 36, -28, 37]], "type": "Polygon", "properties": {"dt_code": "112", "district": "Nagaur", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-20, -35, 38, 39, 40, -23]], "type": "Polygon", "properties": {"dt_code": "109", "district": "Dausa", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-34, 41, 42, 43, -39]], "type": "Polygon", "properties": {"dt_code": "107", "district": "<PERSON><PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-33, 44, -42]], "type": "Polygon", "properties": {"dt_code": "106", "district": "Dholpur", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-13, -30, 45, 46, 47]], "type": "Polygon", "properties": {"dt_code": "115", "district": "<PERSON><PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-40, -44, 48, 49, 50, 51]], "type": "Polygon", "properties": {"dt_code": "108", "district": "Sawai Madhopur", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-24, -41, -52, 52, 53, 54]], "type": "Polygon", "properties": {"dt_code": "120", "district": "Tonk", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-29, -37, 55, 56, 57, 58, 59, 60, 61, -46]], "type": "Polygon", "properties": {"dt_code": "118", "district": "<PERSON><PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-54, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71]], "type": "Polygon", "properties": {"dt_code": "122", "district": "Bhilwara", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-47, -62, 72, 73]], "type": "Polygon", "properties": {"dt_code": "116", "district": "Jalore", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-51, 74, 75, -63, -53]], "type": "Polygon", "properties": {"dt_code": "121", "district": "Bundi", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-50, 76, 77, 78, 79, 80, -75]], "type": "Polygon", "properties": {"dt_code": "127", "district": "Kota", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-61, 81, 82, -73]], "type": "Polygon", "properties": {"dt_code": "117", "district": "<PERSON><PERSON><PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-78, 83, 84]], "type": "Polygon", "properties": {"dt_code": "128", "district": "<PERSON><PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-60, 85, 86, 87, 88, 89, -82]], "type": "Polygon", "properties": {"dt_code": "130", "district": "Udaipur", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-79, -85, 90]], "type": "Polygon", "properties": {"dt_code": "129", "district": "<PERSON>hala<PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-89, 91, 92, 93]], "type": "Polygon", "properties": {"dt_code": "124", "district": "Dungarpur", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-93, 94, 95]], "type": "Polygon", "properties": {"dt_code": "125", "district": "Banswara", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[[-58, 96]], [[-25, -55, -72, 97, -70, 98, -68, 99, -56, -36]]], "type": "MultiPolygon", "properties": {"dt_code": "119", "district": "<PERSON><PERSON><PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[[-57, -100, -67, 100, -86, -59, -97]], [[-69, -99]], [[-71, -98]]], "type": "MultiPolygon", "properties": {"dt_code": "123", "district": "<PERSON><PERSON><PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[[-64, -76, -81, 101]], [[-66, 102, 103, -87, -101]]], "type": "MultiPolygon", "properties": {"dt_code": "126", "district": "Chittorgarh", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[104, 105, 106]], "type": "Polygon", "properties": {"dt_code": "099", "district": "Ganganagar", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-6, 107, -107, 108]], "type": "Polygon", "properties": {"dt_code": "100", "district": "Hanumangarh", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-5, -38, -27, -11, -31, -9, 109, -105, -108]], "type": "Polygon", "properties": {"dt_code": "101", "district": "<PERSON><PERSON><PERSON>", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}, {"arcs": [[-88, -104, 110, -95, -92]], "type": "Polygon", "properties": {"dt_code": "131", "district": "Pratapgarh", "st_code": "08", "year": "2011_c", "st_nm": "Rajasthan"}}]}}, "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:OGC:1.3:CRS84"}}}