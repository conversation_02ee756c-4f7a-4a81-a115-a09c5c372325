{"type": "Topology", "arcs": [[[6977, 9007], [-104, 0], [-66, -30], [-29, -123], [4, -59], [-102, 19]], [[6680, 8814], [-77, 3], [-5, -36]], [[6598, 8781], [-62, 7], [-35, 37], [-50, 11], [-99, -25], [9, -43], [-88, -113], [-65, 7], [-54, 25], [-5, 74], [-49, 35]], [[6100, 8796], [-3, 57], [-71, -9], [-14, 25], [-3, 181], [-72, -14], [-5, 32], [-75, 39], [-29, -95], [-106, -15], [-89, 11], [-61, -7], [-11, -57], [-42, -25], [-29, 76], [-56, -57], [4, -42], [-118, -36], [-54, 41], [-33, 70]], [[5233, 8971], [44, -19], [33, 33], [62, 0], [-16, 60], [62, 50], [68, -6], [38, 49], [-1, 39], [-60, 8], [-8, 101], [-26, 54], [166, 26], [72, -11], [5, -118], [58, -24], [55, 52], [112, -14], [22, -64], [59, -49], [24, 36], [47, -52], [60, 61], [-103, 60], [57, 68], [40, -36], [44, 44], [196, 59], [46, 49], [-32, 81], [58, 9], [-2, 70], [61, 16], [33, 63], [3, 45], [83, -22], [2, -67], [115, 4], [138, -11], [28, -22], [38, 160], [50, -119], [56, -90], [35, -231], [-2, -63], [-44, -166], [-32, -77]], [[6977, 9007], [-10, -89], [-61, -277], [-17, -142]], [[6889, 8499], [-70, 26], [-44, -8], [9, 95], [-30, 82], [-57, 28], [-17, 92]], [[3737, 8089], [-8, -44], [24, -56], [-10, -146], [59, -69], [-24, -99], [40, -16], [83, 26], [105, -21], [27, -48], [56, -23], [-7, -94], [-20, -49], [80, -16]], [[4142, 7434], [40, -66], [-66, -79]], [[4116, 7289], [-37, 10], [-54, -27], [-43, 24], [-58, -2], [-41, -71], [-52, 17], [9, 42], [-138, 57], [-10, 42], [-53, -4], [-35, 67], [-55, 18], [-60, -18], [-8, 45], [-47, 32], [18, 37], [-32, 37], [-38, -35], [-46, 9], [-20, 51], [-41, 14], [-26, 56], [23, 51], [-42, 44], [-30, -17], [-86, 53], [-37, -29], [-27, 77], [-49, 8], [-51, -38], [-55, 9], [86, -144], [-37, -62], [-2, -68], [-37, -21], [-103, 9], [14, -48], [-21, -82], [-32, 8], [-51, -48], [-35, -153], [-57, 37], [-24, -24]], [[2596, 7252], [-27, 43], [-113, 33], [-90, 13], [-161, -20], [-80, 27], [-2, 60], [30, 65], [66, -2], [66, 48], [22, 53], [59, 45], [25, 173], [-34, 30], [13, 51], [-48, 19], [-13, 104], [20, 75], [25, 23], [-12, 69], [105, 30], [32, -42], [106, 24], [35, 41], [0, 54], [57, 54], [9, 167], [63, 33], [93, -1], [67, 38], [16, -33], [-37, -61], [40, -26], [41, 17], [44, -20], [49, 17], [21, 38], [78, -23], [24, -40], [76, -63], [165, -38], [16, -103], [62, -2], [35, -54], [142, -82], [57, 4]], [[3738, 8090], [-1, -1]], [[4142, 7434], [47, 21], [59, 61], [33, 124], [39, 23], [28, 61], [-17, 38], [34, 40], [75, 132], [62, 167]], [[4502, 8101], [58, 78], [34, 18], [103, -28], [60, 55], [29, -18], [71, 96], [115, 28], [69, -31], [58, 2], [4, 69]], [[5103, 8370], [75, 42], [62, -58], [13, -51], [81, -27], [89, -75], [-66, -31], [45, -78], [117, 76], [24, 51], [7, 126], [-16, 23], [-5, 108], [27, 53], [67, -31], [82, -6]], [[5705, 8492], [45, -33], [92, -37], [12, -74], [89, 6], [80, -57], [-6, -88], [-121, -49], [-42, 7], [-27, -70], [60, -67], [43, 14], [58, -25], [-17, -67], [50, -31]], [[6021, 7921], [21, -30], [-11, -104], [-70, -51], [-19, -46], [-48, -15], [-10, -46], [-40, -41]], [[5844, 7588], [-74, 10], [-118, 35], [-37, -43], [-96, 50], [-5, 39], [-56, 19], [-32, 52], [-123, 26], [-28, -21], [-112, 12], [-29, -61], [38, -50], [-23, -25], [25, -83], [68, -8], [-49, -73], [-27, -5], [-20, -136], [-39, -56], [116, -73], [-31, -76], [-43, -37], [-59, 15], [-48, -70]], [[5042, 7029], [-44, 25], [-37, -45], [-80, 22], [33, 67], [-22, 21], [-57, -79], [-36, 50], [-123, 7], [-79, -96], [-69, 21], [-66, -34], [-53, 22], [-148, -63]], [[4261, 6947], [-34, 15], [20, 97], [42, 118], [-8, 25], [-108, -44], [-61, 57], [28, 52], [-24, 22]], [[4261, 6947], [-22, -80], [33, -35], [-27, -38], [-82, -5], [-58, -46]], [[4105, 6743], [-53, -83], [-84, -28], [-112, 19], [-70, 39], [-13, 40], [-51, 20], [-35, -76], [-55, -68], [-38, 10], [67, 147], [-77, 44], [-6, 48], [-107, 14], [-38, 69], [-146, -19], [-142, -53], [-48, -4], [-103, -68], [-106, 0], [-88, 31], [-51, -26], [-126, 16], [-41, 52], [-108, 23]], [[2474, 6890], [35, 113], [55, 84], [47, 30], [34, 69], [-49, 66]], [[4105, 6743], [115, -32], [-2, -84], [-47, -13], [-38, -51], [-9, -83], [26, -10], [-6, -86], [17, -35], [71, 78], [30, -15], [107, 36], [41, -27], [3, -48], [-32, -22], [-2, -63], [71, -65], [-19, -29], [45, -67]], [[4476, 6127], [-56, -27], [-36, -96], [52, -25], [-35, -97], [-23, 12], [-76, 103], [-72, 11], [-19, -36], [-64, -24], [21, -86]], [[4168, 5862], [-38, -56], [-63, -6], [-57, -42], [-31, 8], [0, 69], [-51, 21], [-23, 47], [-41, 19]], [[3864, 5922], [-66, -13], [2, 55], [-28, 80], [17, 40], [53, 38], [-30, 42], [-7, 95], [-148, -34], [-61, -82], [-46, 101], [-53, -18], [-49, -46], [-58, 21], [-84, -46], [-139, 17], [-8, -46], [-54, -13], [-29, 27], [-84, 1], [-10, -111], [-91, -29], [-35, -56], [-79, -10], [-54, 61], [-103, 3], [-50, -11], [-60, 35]], [[2510, 6023], [59, 81], [-13, 43], [39, 65], [-17, 58], [43, 83], [-22, 25], [-58, -26], [-44, 5], [14, 148], [43, 106], [-20, 27], [-77, 25], [-7, 96], [40, -2], [-16, 133]], [[2510, 6023], [-24, -73], [60, -80], [43, -25], [114, -174], [51, -9], [46, -38], [-7, -90], [39, -59], [12, -136], [71, -17]], [[2915, 5322], [-18, -36], [-71, -45], [-87, 41], [-21, 40], [-71, -21]], [[2647, 5301], [-66, 48], [-132, 33], [-77, 41], [-99, -32], [-100, -3], [-23, 128], [41, 14], [-18, 69], [-63, 58], [20, 77], [-97, 15], [-56, 21], [-88, -9], [20, -71], [-103, 27], [-117, 106], [-87, -62], [-35, 34]], [[1567, 5795], [-55, 76], [-119, 42], [-55, -60], [-86, 29]], [[1252, 5882], [30, 29], [4, 69], [50, 21], [-36, 52], [37, 76], [-90, 84], [-96, 16], [-87, -5], [-12, 30]], [[1052, 6254], [-21, 81], [27, 83], [65, 84], [35, 109], [105, -32], [23, 42], [51, 25], [44, -37], [76, -96], [50, 4], [-2, 56], [134, 33], [85, 4], [19, 33], [76, -7], [71, -67], [103, 26], [56, -34], [37, 52], [27, 140], [43, 60], [4, 58], [78, -16], [39, 17], [75, -7], [122, 25]], [[1252, 5882], [-45, -46], [-60, -15], [-19, 27], [-70, -46], [20, -59], [-34, -34], [-75, 8], [-17, -25], [-96, -57], [-46, -10]], [[810, 5625], [-132, -82], [-48, 6], [-115, -22], [-59, 46], [-86, -10], [7, 60], [117, 56], [36, 46], [9, 111], [-150, 53], [-157, 120], [-73, -4], [-72, 51], [-33, -3], [-42, 79], [-12, 75], [78, 59], [40, -53], [58, 44], [72, 11], [17, 49], [67, 7], [51, 55], [94, 72], [47, -28], [53, -127], [75, -18], [99, 14], [260, -13], [41, -25]], [[3864, 5922], [-37, -54], [-30, -80], [24, -14], [-67, -200], [-70, -44], [0, -80], [-61, -14], [-15, -52], [24, -63], [-46, -21], [-162, 66], [10, -54], [-90, 9], [20, -84], [-51, -16]], [[3313, 5221], [-108, 111], [-81, -3], [-47, 30], [-63, 10], [-76, -55], [-23, 8]], [[4476, 6127], [59, -27]], [[4535, 6100], [145, -182], [121, -21], [40, 16], [112, -11], [81, -32]], [[5034, 5870], [-19, -71], [-45, -11], [-38, -110], [23, -38], [-40, -32], [-9, -55], [-66, -125], [24, -35], [-25, -44], [-39, -3], [-44, -44]], [[4756, 5302], [-55, 63], [-81, -8], [-53, 18], [-152, -37], [6, 58], [-86, 15], [-44, -26], [13, -53], [-71, -13], [-35, 110], [-10, 114], [40, -6], [25, 41], [-5, 62], [49, 80], [-9, 52], [-132, 19], [12, 71]], [[1567, 5795], [6, -53], [-53, -29], [77, -39], [-6, -178], [82, -49], [59, -5], [34, -42], [-162, -83], [18, -56], [-43, -68], [43, -126], [55, -43], [63, 4], [20, -95], [44, -21], [16, -47], [-69, -65], [-41, 2], [-58, 45], [-61, -17], [-46, -77], [12, -124], [19, -53], [50, -12], [-40, -106], [-96, 4], [-36, -25], [-14, -57], [9, -66], [-33, -45], [25, -26], [-55, -64], [77, -87], [-17, -74], [24, -39]], [[1470, 3979], [-59, -49], [-8, -41], [-115, -74], [-77, 28], [-50, -5], [-23, 48], [-115, 73], [32, 114], [-43, 48], [-13, 51], [21, 47], [18, 260], [-36, 12], [-6, 58], [117, 7], [-28, 80], [65, 140], [-5, 80], [-53, 2], [-67, 109], [-88, 37], [-69, 6], [-138, 64], [13, 67], [85, 128], [52, 29], [3, 139], [-73, 13], [-18, 44], [60, 48], [9, 39], [-51, 44]], [[4756, 5302], [11, -87], [-45, -16], [-13, -54], [-63, 5], [103, -91], [4, -50]], [[4753, 5009], [-103, -9], [-36, -41], [-122, -41], [28, -31], [-6, -186], [14, -27]], [[4528, 4674], [-2, -31], [-89, 12], [-51, -80], [-55, 6], [-39, 109], [-80, 36], [7, -77], [-46, -34], [-13, 58], [-54, -2], [-59, -80], [-47, 3], [-25, 46], [-48, -22], [7, -80], [-76, 17], [-40, -48], [17, -125], [29, -4], [57, -100], [52, 56], [68, -71], [-11, -49], [-37, -27], [-66, 9], [-30, -108]], [[3897, 4088], [1, 22], [-97, 38], [-22, -83], [-36, 0], [-63, -94]], [[3680, 3971], [-78, -17]], [[3602, 3954], [8, 44], [-101, 84], [62, 119], [-139, 55], [-48, 6], [-25, 48], [14, 82]], [[3373, 4392], [74, 13], [34, 44], [4, 84], [47, 134], [41, 0], [49, 35], [56, 8], [50, -92], [117, 53], [72, 6], [85, 59], [21, 195], [-48, 49], [-83, 17], [-127, 107], [-44, 24], [-105, 0], [-157, 14], [-74, -18], [-44, 40], [-28, 57]], [[5034, 5870], [97, 24], [123, -18], [-7, 54], [128, 11], [31, -16], [7, -75], [145, -94], [42, -1], [21, -152], [-15, -44], [-74, -94]], [[5532, 5465], [-47, -52], [-55, -97], [-71, -31], [-79, -56], [-35, -6], [-118, -121], [-139, -11], [-112, -32], [-22, -43], [-53, -20], [-48, 13]], [[2647, 5301], [26, -45], [-18, -61], [-52, -31], [33, -34], [-17, -104], [21, -47], [104, -71], [65, 5], [12, -90], [-12, -31], [-124, 60], [-48, -68], [97, -32], [-5, -60], [-28, -62]], [[2701, 4630], [-33, 8], [-61, -31], [-43, -67], [-51, 57], [13, 96], [-18, 34], [-53, -34], [-15, -76], [-33, -8], [-87, 26], [-63, -79], [-11, -52], [-46, 5], [-20, 53], [-38, 13], [-111, -43], [-57, -63], [-27, -71], [16, -44], [13, -135], [-90, 11], [-22, -69], [-7, -110], [62, -142], [-39, -6], [-71, -70]], [[1809, 3833], [-40, 5], [-2, 51], [-61, 91], [12, 67], [-101, 10], [-96, -72], [-51, -6]], [[5532, 5465], [117, 59]], [[5649, 5524], [45, -100], [-40, -127], [-32, -25], [35, -55], [-27, -73]], [[5630, 5144], [-25, -50], [-26, -129], [-52, -9], [-113, 57], [-53, 7], [-115, 67], [-34, -69], [3, -46], [37, -16], [11, -63], [89, -62], [-17, -96], [-61, -41], [17, -100], [68, -28], [-19, -93], [25, -24], [66, -3], [68, -95], [51, -22], [24, -39], [-19, -99], [-30, 0], [36, -110], [44, -5]], [[5605, 4076], [-42, -32], [-8, -63]], [[5555, 3981], [-158, 0], [-23, -39], [-113, -51], [-58, -64], [20, -24], [-55, -50], [-33, -90]], [[5135, 3663], [-42, 38], [-88, 3], [16, 63], [-18, 37], [-59, 32], [29, 55], [-24, 42], [24, 36], [-20, 51], [54, 21], [-35, 133], [46, 88], [-32, 69], [31, 37], [45, 8], [-13, 85], [-61, 4], [-20, 83], [-50, -9], [-60, 33], [1, 39], [-64, 20], [-61, 72], [-11, -76], [-117, -15], [-78, 62]], [[3373, 4392], [-34, 23], [-70, 5], [-5, 61], [50, 30], [-34, 64], [10, 161], [50, 126], [-130, -11], [-12, -68], [-48, 16], [-122, -3], [-27, -26], [-63, 15], [-27, -43], [116, -2], [29, -41], [-16, -78], [-62, -6], [-17, -58], [-133, 17], [-17, -26], [-71, -3], [-39, 85]], [[5630, 5144], [50, -8], [79, 31], [52, -20], [44, 71], [92, 7], [82, -34]], [[6029, 5191], [19, -63], [-66, 15], [6, -71]], [[5988, 5072], [-96, -27], [-27, -50], [4, -86], [126, -17], [2, -53], [-45, -27], [-63, -236], [12, -46], [-10, -89], [80, -87], [-42, -134], [-102, -8], [6, -119], [-127, 13], [-42, -48], [-59, 18]], [[3602, 3954], [-8, -128], [-97, -80], [15, -96], [-39, -76], [-78, -20], [-97, 57], [9, 84], [-31, 49], [-40, 12], [-172, -8], [-81, -95], [-67, -31], [-33, -68], [-37, 5], [-151, -20], [-70, -20], [-88, -53]], [[2537, 3466], [-17, 136], [38, 91], [-48, 93], [-58, -30], [-64, 39], [-100, -50], [-54, -7], [-122, -86], [-180, 36], [-29, -26], [-69, 0], [-60, -25]], [[1774, 3637], [12, 111], [23, 85]], [[3680, 3971], [15, -71], [43, -101], [-31, -70], [12, -63], [61, -11], [35, -55], [-8, -75], [-43, -10], [48, -63], [1, -88], [-116, -29], [-38, -33], [-85, -13], [-13, -109], [-78, 8], [-39, -18], [-54, 27], [-100, 0], [-45, -36], [16, -77], [52, 30], [49, -38], [-82, -22], [-15, -58]], [[3265, 2996], [-51, 44], [-11, -55], [-68, -25], [44, -105], [-47, -36], [-32, 29], [-42, -55], [-70, -32], [-37, 35], [-191, -23], [-114, 8], [-29, 73], [-76, 33], [-80, -41], [-42, 49], [-66, -17], [-33, -50], [-84, -13], [-106, -172]], [[2130, 2643], [-19, 28], [108, 150], [69, 147], [57, 62], [40, 77], [-9, 61], [46, 55], [15, 52], [43, 25], [36, 145], [21, 21]], [[2130, 2643], [-35, -38], [-46, 9], [-44, -38]], [[2005, 2576], [-30, -11], [-33, 57], [-106, 85], [-59, -47], [-60, 16], [-62, 58], [-52, 1], [20, 74], [75, 131], [40, 121], [-34, 42], [16, 68], [-28, 9], [-3, 71], [94, 127], [-30, 62], [11, 39], [-99, 98], [-5, 33], [74, 44], [40, -17]], [[3265, 2996], [82, -7], [40, 42], [56, -45], [63, -28], [-7, -96], [23, -18], [43, 83], [80, -121], [61, -16], [10, -60], [51, -6]], [[3767, 2724], [-31, -37], [-66, -26], [4, -116], [-48, 6], [-32, -29], [-47, -94], [-142, 10], [7, -67], [35, -51], [104, 5], [2, -74], [56, 21], [43, -9], [-16, -72], [-53, -56], [-71, 12]], [[3512, 2147], [-119, 38], [-5, 39], [-64, 27], [-6, 46], [-190, -10], [-80, -97], [-2, -72], [-90, -34], [-66, -83], [-69, 11], [4, 66], [-57, -2]], [[2768, 2076], [-99, 13], [-133, 71], [-115, -5], [-93, 36], [1, 62], [-104, 57], [-17, -73], [-24, -5], [-112, 40], [16, 28], [-53, 52], [-54, -12], [-84, 49]], [[1897, 2389], [46, 71], [45, -1], [17, 117]], [[3512, 2147], [-20, -146], [46, 6], [-36, -101], [129, -55], [60, -42]], [[3691, 1809], [-194, -161], [-79, -56], [-47, -59], [-46, -98], [-19, -139], [15, -53], [44, -42], [-54, -50], [-43, -78], [-6, -113], [16, -33], [-24, -89], [22, -82], [-30, -48], [-107, -111], [3, -70], [-126, -40], [-36, -27]], [[2980, 460], [-139, -19], [-53, 29], [-18, 148], [-28, 15], [13, 93], [-41, 140], [-59, 35], [8, 64], [56, 21], [-11, 89], [26, 88], [-11, 85], [51, 46], [-85, 134], [-69, 20], [-2, 66], [-111, -1], [-8, 54], [67, 45], [-13, 35], [-58, 21], [40, 64], [-17, 22]], [[2518, 1754], [72, 26], [-4, 48], [-94, -4], [-21, 64], [107, 87], [13, -44], [68, 3], [26, 23], [68, -16], [36, 61], [-21, 74]], [[2326, 113], [-39, -38], [-18, -74], [-39, -1], [-152, 33], [-195, 58], [-141, 77], [-113, 87], [-53, 57], [-93, 72], [36, 51], [68, 12], [28, 40], [-26, 32], [64, 91], [27, 91], [85, 64], [37, 71], [-26, 57]], [[1776, 893], [117, -46], [65, -61], [23, 10], [86, -30], [10, -55], [48, -37], [-18, -48], [18, -49], [105, -58], [14, -102], [37, -63], [-37, -78], [12, -74], [-15, -51], [85, -38]], [[5844, 7588], [57, -50], [45, 30], [86, -15], [84, 22], [49, -44], [79, -124], [47, -24], [46, 33], [78, -26], [59, 38]], [[6474, 7428], [-211, -338], [-60, -23], [-16, -68], [29, -12], [-29, -90], [-58, 21], [-38, -14], [-60, -67], [-11, 99], [-39, 26], [-40, -90], [75, -65], [-96, -43]], [[5920, 6764], [25, -88], [-23, -34]], [[5922, 6642], [-19, -10]], [[5903, 6632], [-69, -9], [-75, 70], [-44, -2], [-62, 30], [-105, 26], [23, -75], [-6, -41], [-62, 3], [-17, -45]], [[5486, 6589], [-111, 63], [0, 52], [-37, 46], [-31, -35], [-40, 38], [-62, -65], [-84, 65], [-9, 63], [40, 74], [-48, 40], [-65, 25], [3, 74]], [[5934, 7038], [-62, -77], [63, -4], [-1, 81]], [[5841, 6951], [-35, -107], [12, -41], [53, 8], [23, 53], [-38, 28], [-15, 59]], [[4535, 6100], [47, -4], [33, 110], [138, 13], [24, 57], [91, -13], [22, 62], [55, -62], [115, -24], [45, 59], [45, 18], [25, 41], [79, -28], [61, 43], [91, 36], [101, -27], [-14, 103], [46, 35], [-1, 39], [-52, 31]], [[5903, 6632], [19, 10]], [[5920, 6764], [95, -30], [51, 18], [49, -83], [-106, 26], [-2, -59], [-27, -43], [130, -20], [-59, -267], [-7, -118], [16, -63], [63, -120], [17, -54]], [[6140, 5951], [-17, -76], [-66, 2], [-9, -53], [-60, -12], [-58, -52], [-59, -23], [-103, -153], [-59, -16], [-60, -44]], [[5135, 3663], [-2, -63], [44, -90], [-12, -42], [-158, -176], [-49, -79]], [[4958, 3213], [-4, -6]], [[4954, 3207], [-4, -49]], [[4950, 3158], [-4, -4]], [[4946, 3154], [-86, 1], [-44, 24], [-86, 141]], [[4730, 3320], [13, 80], [-117, 59], [9, 91], [42, 39], [-134, 106], [-59, -51], [-53, 78], [-11, 76], [-70, -24], [-4, -64], [-65, -5], [-35, 27], [-38, -53], [-32, 15], [9, 90], [-45, 37], [8, 49], [-127, 60], [-12, -54], [-63, -4], [-61, -117], [-44, 34], [26, 48], [-52, 10], [-8, 48], [93, -5], [24, 110], [-40, 46], [13, 42]], [[4730, 3320], [-41, -1], [-40, -47], [-82, 29], [9, -67], [-56, -75], [-4, -114], [35, -4], [-6, -70], [-90, 57], [-5, 115], [-33, 0], [-26, 81], [-31, -30], [-84, -3], [-26, -65], [-5, -63], [45, -59], [-16, -98], [95, 13], [31, -38], [-7, -127], [46, -46], [-84, -20], [-77, 2], [-58, -23], [-65, -100], [-70, 31], [-6, 34], [-61, 34], [-96, 5], [-74, 61], [-81, -8]], [[4958, 3213], [-4, -6]], [[4950, 3158], [-4, -4]], [[4946, 3154], [-59, -62], [-40, -76], [-31, -12], [-104, -136], [-35, -102], [-50, -65], [-28, -183], [-20, -52], [53, -92], [141, -144], [87, -58], [162, -25], [88, -1], [90, 16], [80, 63], [40, -34], [-36, -53], [56, -104], [-80, 45], [-75, 23], [-56, -1], [-57, 42], [-171, -38], [-190, 25], [-93, -13], [-30, -31], [-57, 14], [-253, -86], [-21, -36], [-51, 11], [-66, -19], [5, -41], [-121, -54], [-74, 12], [-208, -42], [-51, -36]], [[5988, 5072], [90, -58], [81, -22], [-5, -69], [51, -32], [4, -341], [31, -477], [19, -79], [-38, -72], [-157, -19], [-152, 18], [-75, 22], [-282, 38]], [[6140, 5951], [18, -80], [34, -49], [-15, -53], [37, -258], [-1, -311], [-184, -9]], [[2518, 1754], [-26, 10], [-106, -21], [-25, -188], [-29, -71], [-49, -28], [51, -48], [-44, -141], [-83, -4], [-89, 44], [-41, -123], [-81, -3], [-64, 20], [-96, 71], [33, 65], [-17, 78], [-31, 18], [-67, -6]], [[1754, 1427], [-60, 44], [-82, 156], [-41, 32], [30, 63], [44, -3], [23, 72], [106, 124], [-19, 77], [32, 67], [14, 103], [94, 135], [2, 92]], [[2980, 460], [-37, -42], [-100, -73], [-85, -33], [-63, -87], [-19, -72], [-61, -30], [-43, 33], [-168, -20], [-78, -23]], [[1776, 893], [-30, 74], [-64, 51], [-12, 57], [-48, 88], [36, 57], [51, 39], [4, 40], [55, 62], [-14, 66]], [[4502, 8101], [-85, 5], [-69, 79], [30, 108], [31, 19], [6, 95], [75, 19], [83, 72], [23, 68], [-44, -2], [-131, -71], [-48, -12], [-58, -52], [-57, 71], [-96, 13], [-98, -36]], [[4064, 8477], [53, 154], [-22, 106], [60, 43], [85, 0], [16, 95], [63, -24], [62, 16], [44, 44], [50, -18], [84, 33], [1, -115], [54, 84], [55, -7], [46, 18], [90, -29], [51, -33], [28, -45], [48, 23], [93, -33], [50, 50], [-6, 70], [79, 31], [-10, 92]], [[5138, 9032], [62, -84], [49, -103], [-45, -119], [3, -50], [-90, -3], [-52, -66], [9, -61], [-23, -113], [52, -63]], [[6100, 8796], [-224, -131], [5, -37], [-162, -55], [-14, -81]], [[5138, 9032], [95, -61]], [[3738, 8090], [101, 9], [-6, 78], [61, 10], [1, 77], [87, -46], [4, 103], [61, 45], [26, 70], [-9, 41]], [[6598, 8781], [-29, -13], [15, -70], [57, -26], [-86, -127], [-3, -78], [-166, -87], [-80, 16], [-47, -58], [63, -27], [56, -74], [-60, -49], [20, -80], [-180, -30], [-5, -74], [-132, -83]], [[6889, 8499], [9, -132], [-148, -437], [-21, -137], [-32, -88], [-139, -150], [-84, -127]]], "transform": {"scale": [0.0005829937085111589, 0.0005625271732568685], "translate": [76.23340535317459, 8.076333431067162]}, "objects": {"districts": {"type": "GeometryCollection", "geometries": [{"arcs": [[0, 1, 2, 3, 4]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "st_code": "33", "dt_code": "602", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-1, 5, 6]], "type": "Polygon", "properties": {"district": "Chennai", "st_code": "33", "dt_code": "603", "year": "2018", "st_nm": "Tamil Nadu"}}, {"arcs": [[7, 8, 9, 10, 11]], "type": "Polygon", "properties": {"district": "Krishnagiri", "st_code": "33", "dt_code": "631", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-9, 12, 13, 14, 15, 16, 17, 18, 19]], "type": "Polygon", "properties": {"district": "Tiruvannamalai", "st_code": "33", "dt_code": "606", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-10, -20, 20, 21, 22]], "type": "Polygon", "properties": {"district": "Dharmapuri", "st_code": "33", "dt_code": "630", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-22, 23, 24, 25, 26, 27]], "type": "Polygon", "properties": {"district": "Salem", "st_code": "33", "dt_code": "608", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-28, 28, 29, 30, 31, 32, 33]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON>", "st_code": "33", "dt_code": "610", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-33, 34, 35]], "type": "Polygon", "properties": {"district": "Nilgiris", "st_code": "33", "dt_code": "611", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-27, 36, 37, -29]], "type": "Polygon", "properties": {"district": "Namakkal", "st_code": "33", "dt_code": "609", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-25, 38, 39, 40, 41]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "33", "dt_code": "615", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-32, 42, 43, -35]], "type": "Polygon", "properties": {"district": "Coimbatore", "st_code": "33", "dt_code": "632", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-26, -42, 44, 45, 46, 47, 48, 49, 50, -37]], "type": "Polygon", "properties": {"district": "Tiruchirappalli", "st_code": "33", "dt_code": "614", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-41, 51, 52, -45]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON>", "st_code": "33", "dt_code": "616", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-31, 53, 54, 55, -43]], "type": "Polygon", "properties": {"district": "Tiruppur", "st_code": "33", "dt_code": "633", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-46, -53, 56, 57, 58, 59, 60, 61]], "type": "Polygon", "properties": {"district": "Thanjavur", "st_code": "33", "dt_code": "620", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-30, -38, -51, 62, -54]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON>", "st_code": "33", "dt_code": "613", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-59, 63, 64, 65]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "st_code": "33", "dt_code": "619", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-50, 66, 67, 68, -55, -63]], "type": "Polygon", "properties": {"district": "Dindigul", "st_code": "33", "dt_code": "612", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-49, 69, 70, 71, -67]], "type": "Polygon", "properties": {"district": "Madurai", "st_code": "33", "dt_code": "623", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-68, -72, 72, 73]], "type": "Polygon", "properties": {"district": "Theni", "st_code": "33", "dt_code": "624", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-71, 74, 75, 76, 77, 78, -73]], "type": "Polygon", "properties": {"district": "Virudhunagar", "st_code": "33", "dt_code": "625", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-77, 79, 80, 81, 82]], "type": "Polygon", "properties": {"district": "Thoothu<PERSON>kudi", "st_code": "33", "dt_code": "627", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[83, 84]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "33", "dt_code": "629", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-18, 85, 86, 87, 88, 89, 90], [91], [92]], "type": "Polygon", "properties": {"district": "Viluppuram", "st_code": "33", "dt_code": "607", "year": "2019", "st_nm": "Tamil Nadu"}}, {"arcs": [[-40, 93, -90, 94, -88, 95, 96, -57, -52]], "type": "Polygon", "properties": {"district": "Cuddalore", "st_code": "33", "dt_code": "617", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-47, -62, 97, 98, 99, 100, 101, 102]], "type": "Polygon", "properties": {"district": "Pudukkottai", "st_code": "33", "dt_code": "621", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-48, -103, 103, -75, -70]], "type": "Polygon", "properties": {"district": "Sivaganga", "st_code": "33", "dt_code": "622", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-76, -104, -102, 106, -80]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "st_code": "33", "dt_code": "626", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[[-60, -66, 107]], [[-58, -97, 108, -64]]], "type": "MultiPolygon", "properties": {"district": "Nagapattinam", "st_code": "33", "dt_code": "618", "year": "2011_c", "st_nm": "Tamil Nadu"}}, {"arcs": [[-19, -91, -94, -39, -24, -21]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "st_code": "33", "dt_code": "729", "year": "2019", "st_nm": "Tamil Nadu"}}, {"arcs": [[-78, -83, 109, 110]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON>", "st_code": "33", "dt_code": "0", "year": "2019", "st_nm": "Tamil Nadu"}}, {"arcs": [[-82, 111, -85, 112, -110]], "type": "Polygon", "properties": {"district": "Tirunelveli", "st_code": "33", "dt_code": "628", "year": "2019", "st_nm": "Tamil Nadu"}}, {"arcs": [[-14, 113, 114, 115]], "type": "Polygon", "properties": {"district": "<PERSON><PERSON><PERSON>", "st_code": "33", "dt_code": "605", "year": "2019", "st_nm": "Tamil Nadu"}}, {"arcs": [[-4, 116, -15, -116, 117]], "type": "Polygon", "properties": {"district": "Ranipet", "st_code": "33", "dt_code": "0", "year": "2019", "st_nm": "Tamil Nadu"}}, {"arcs": [[-8, -12, 118, -114, -13]], "type": "Polygon", "properties": {"district": "Tirupathur", "st_code": "33", "dt_code": "0", "year": "2019", "st_nm": "Tamil Nadu"}}, {"arcs": [[-3, 119, -16, -117]], "type": "Polygon", "properties": {"district": "Kancheepuram", "st_code": "33", "dt_code": "604", "year": "2019", "st_nm": "Tamil Nadu"}}, {"arcs": [[-2, -7, 120, -86, -17, -120]], "type": "Polygon", "properties": {"district": "Chengalpattu", "st_code": "33", "dt_code": "0", "year": "2019", "st_nm": "Tamil Nadu"}}]}}, "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:OGC:1.3:CRS84"}}}