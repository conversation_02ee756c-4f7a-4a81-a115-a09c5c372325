{"type": "Topology", "arcs": [[[3171, 19443], [197, -65], [1, -77], [86, 40], [173, -95], [99, 26], [96, -46], [33, 45], [16, -48], [94, 26], [86, -48], [83, 35], [45, -22], [203, 139], [132, -59], [86, 97], [49, -14]], [[4650, 19377], [-7, -14], [-3, 0], [2, -3], [63, -67], [-3, -8], [-21, -48], [67, -23], [-105, -187], [-9, -16], [42, -7], [-32, -34], [68, -47], [198, 6], [20, -100], [70, -55], [-52, -44], [35, -19], [-9, -36], [118, -39], [-36, -66], [-68, -33], [-15, 13], [-21, -30], [-1, -3], [-26, -39], [19, -21], [-6, -45]], [[4938, 18412], [-29, 14], [-171, -35], [-41, -61], [-130, 14], [-20, -43], [-56, 45], [-74, -41], [-46, 54], [-31, -46], [-45, 42], [-48, -32], [1, -2], [46, -103], [-38, -25], [25, -141], [-36, -75], [56, -68], [-78, -83], [39, -131], [67, -23], [-26, -40], [46, -31], [-54, -131], [40, -45], [-68, -83], [45, -43], [-52, -169], [-66, 49], [-171, -145], [-107, 6], [1, -2]], [[3917, 17038], [-3, 0], [-22, 12], [-25, 36], [-100, 35], [-11, 73], [46, 36], [-20, 44], [-133, 53], [-39, 53], [-35, 302], [39, 62], [-1, 0]], [[3613, 17744], [0, 0], [1, 1], [-20, 112], [-20, 11], [9, 54], [-4, 21], [-2, 5], [-114, 145], [-1, 0], [0, 1], [12, 35], [12, 39], [-124, 153], [-17, 96], [50, 52], [1, 66], [-86, 99], [15, 48]], [[3325, 18682], [-106, 47], [66, 100], [-27, 54], [-58, -23], [-25, 35], [72, 59], [-62, 98], [-27, 264], [-62, 78], [75, 49]], [[3613, 17744], [-122, 8], [-67, 61], [-69, -38], [-132, 73], [-31, 72], [-36, -29], [-161, 63], [-30, 61], [-72, -5], [-76, 63], [-103, -53], [-51, -112], [-47, -21], [35, -53], [-31, -65], [-150, 80], [-54, -83], [-136, -59], [-59, -114], [-72, -13], [-24, -90], [-90, -35], [-112, 24], [-4, 7]], [[1919, 17486], [-20, 59], [-31, 27], [-67, 56], [-104, 89], [30, 28], [-36, 40], [-81, 10], [-83, -81], [-48, 34], [62, 93]], [[1541, 17841], [-66, 51], [1, 68], [61, 32], [49, 26], [112, 58], [-30, 43], [2, 1], [3, 3], [19, 15], [5, -2], [80, -29], [2, -1], [2, 0], [48, 38], [1, 1], [46, -21], [38, -17], [16, 71], [8, 3]], [[1938, 18181], [1, -3], [98, -25], [87, 51], [74, 42], [16, 9], [3, 9], [9, 23], [26, 66], [34, -17], [124, 71], [85, -33], [-1, 119], [-28, 17], [45, 120], [148, -14], [62, -100], [48, 48], [45, -22], [40, 58], [90, -19], [70, 94], [34, 13], [97, -3], [90, -26], [90, 23]], [[8351, 18593], [-24, -31], [-4, -5], [14, -197], [-36, -97], [71, -48], [2, -47], [-53, -79], [-58, 2], [-91, -125], [22, -61], [-40, -103], [49, -33], [-32, -70], [40, -70], [3, -3], [94, 43], [39, -38], [-15, -38], [99, -8], [-67, -100], [1, -2], [19, -33], [96, 25], [-1, -1], [-47, -108], [88, -52], [-21, 42], [73, 27], [0, -4], [11, -62], [85, -30], [-1, -7], [-19, -154], [52, -66], [-11, -6], [-58, -31], [-12, 70], [-38, -2], [-11, -183], [-74, 42], [-102, -107], [119, -67], [-27, -104], [-90, -112], [134, -121], [9, -81], [-5, -1], [-64, -14], [-11, 49], [-24, -44], [-23, 14]], [[8412, 16362], [33, -63], [-73, -57], [27, -33], [-2, -4], [-48, -21], [-78, -34], [32, -72], [-56, -33], [-7, -89], [-41, -14], [35, -110], [38, -3], [-40, -27], [32, -25], [-11, -86], [-94, -17], [-71, 52], [-84, -101], [-52, 24], [-25, -66], [-63, 37], [-69, -62], [31, -32], [-50, -64]], [[7776, 15462], [-55, 103], [26, 42], [37, 61], [10, 17], [-13, 53], [-29, 114], [-1, 4], [-1, 1], [-34, 15], [-69, 32], [-19, 9], [-62, 79], [-48, 61], [-66, -22], [-96, 45], [-48, -46], [-70, 11], [-20, -38], [-40, 19], [20, 48], [-45, 32], [-169, -49], [-96, 96], [-135, 15], [-31, -64], [-74, 36], [-34, -22], [-44, 55], [-54, -11], [-37, 70], [42, 67], [-114, 102], [31, 125], [-36, 53], [17, 43], [2, 4], [-72, 23], [-11, 74], [34, 72], [-1, 0], [0, 0], [-27, 43], [-25, 4], [-143, 23], [-36, 78], [-78, 40]], [[6062, 16979], [36, 53], [54, -28], [33, 52], [66, 14], [3, 57], [69, 52], [-20, 39], [-5, 10], [80, -13], [17, 86], [177, 26], [106, 124], [-9, 45], [80, 55], [64, -13], [52, 159], [68, 48], [-41, 39], [15, 81], [66, 19], [71, 87], [60, 162], [38, 45], [86, -12], [83, 46], [18, 39], [-64, 48], [21, 33], [53, -47], [61, 22], [-48, 40], [24, 83], [-50, 84], [128, -2], [19, 75], [-96, 25], [110, 40], [-67, 29], [-28, 63], [108, -28], [0, 51], [11, -15]], [[7511, 18752], [15, -20], [14, -18], [-17, -74], [0, -2], [0, 0], [72, 7], [12, 12], [48, -6], [177, -20], [112, 129], [89, -75], [-34, -91], [35, -32], [230, 88], [89, -54], [-2, -3]], [[8351, 18593], [76, -50], [64, 74], [62, -4], [11, 44], [110, -1], [47, 53], [6, 7], [43, -1], [17, 0], [4, 0], [43, -61], [37, 16], [16, -59], [-29, -43], [-32, 28], [20, -57], [-42, -4], [43, -25], [-41, -93], [-26, 36], [-24, -63], [130, -2], [-10, 88], [55, 64], [49, -37], [31, 23], [19, -60], [66, -7], [3, -53], [-119, 39], [-29, -24], [12, -41], [91, -58], [123, 22], [-50, -54], [61, -50], [-15, -35], [50, -103], [245, 96], [102, 129], [20, 73], [-52, 66], [42, 61], [34, -49], [141, 14], [311, -272], [112, 10], [48, -54], [-29, -51], [67, -68], [53, 7], [84, -60], [18, -104], [-35, -148]], [[10384, 17752], [92, 9], [91, -110], [71, 18], [1, -7], [-11, -61], [26, -37], [139, -201], [19, -26], [-48, -102], [-101, -18], [-36, -60], [-178, 85], [-87, -108], [153, -166], [74, -282], [-64, -54]], [[10525, 16632], [-132, -7], [19, 67], [-28, 12], [-77, -68], [-15, 97], [-114, 16], [51, -87], [-26, -45], [39, -33], [-8, -34], [-148, -22], [18, 77], [-60, 23], [-82, -37], [-12, 39], [-43, 1], [-37, -53], [-27, 33], [8, 83], [-77, -29], [-66, 23], [-26, -41], [-20, 108], [-53, 2], [-33, -47], [-52, 76], [-101, 30], [-1, -1], [-40, -33], [-23, -18], [-74, -204], [40, -105], [-2, -249], [31, -70], [-3, -4], [-31, -44], [4, -9], [35, -75], [-3, -13], [-33, -189], [-58, -21], [-98, 76], [-19, 15], [-55, -60], [-6, -4], [-48, 26], [1, 98], [59, -25], [-38, 28], [39, 22], [-46, 27], [42, 45], [-95, 40], [15, 58], [-102, 31], [27, 58], [-36, 60], [-91, 1], [28, -85], [-21, -20], [39, -36], [-74, -84], [18, -26], [-86, -31], [-119, 97], [-66, -5], [0, 0], [17, 64], [0, 2], [-53, -13], [-17, 34], [91, 80], [-105, 33], [-54, 75]], [[3917, 17038], [9, -37], [11, -16], [-3, -18], [-6, -28], [-3, -17], [-91, -66], [-29, -84], [45, -33], [93, -311], [134, -17], [0, -1], [46, -129], [87, -89], [17, -103], [187, -126], [1, -3], [3, -3], [53, -126], [5, -5]], [[4476, 15826], [-109, -116], [0, -1], [-65, 44], [0, 0], [0, 0], [-39, -38], [0, 0], [-80, 81], [0, 0], [0, 0], [-54, -33], [-6, 129], [-95, 60], [0, 0], [0, 0], [-142, -123], [-199, -5], [-65, -70], [-30, 27], [-77, -89], [-68, 12], [-108, -77], [-40, 34], [23, 71], [-67, 83], [-68, -32], [8, 68], [-62, -24], [-94, 59], [-123, -191], [-107, 48], [-91, -18], [10, 37], [-38, 32], [-27, -20], [-164, 155], [-69, -11], [23, 72], [-29, 82], [-1, -1], [-113, -59], [10, -55], [-84, 0], [-31, -43], [-63, 83], [-2, 3], [-45, -86], [-17, -31]], [[2078, 15883], [-23, 32], [-19, 0], [7, 16], [31, 61], [-82, 23], [1, 65], [70, 110], [-42, 24], [-5, 69], [-50, 8], [-33, 158], [116, 57], [-2, 43], [46, 42], [-70, 55], [1, 77], [-158, 101], [0, 77], [50, 68], [-71, 36], [49, 64], [-48, 19], [20, -22], [-34, -75], [-77, 8], [59, 129], [-83, 57], [-8, 6], [204, 123], [20, 88], [-28, 84]], [[2078, 15883], [-29, -56], [8, -29], [-12, -40], [-25, -20], [-31, -4], [-49, 10], [-115, 116], [-55, -71], [-114, 60], [-112, -116], [-292, -11]], [[1252, 15722], [-3, 4], [-3, 0], [1, 2], [15, 50], [8, 26], [1, -1], [55, -45], [41, 112], [-67, 44], [41, 34], [-7, 58], [116, 21], [10, 14], [19, 53], [-5, 10], [-39, 9], [-79, -14], [-19, -3], [-14, -3], [-4, 80], [11, -3], [5, -1], [84, -21], [22, 41], [-45, 53], [94, 67], [-186, 36], [-6, 72], [41, 33], [11, 95], [-115, 109], [81, 53], [27, 78], [-101, 101], [32, 43], [-18, 39], [-46, -14], [-3, 34], [-87, 25], [-6, 89], [-108, 3], [26, 43], [-89, 11], [-28, 105], [-63, 6], [-2, 0], [-1, 8], [-12, 69], [-157, 164], [-5, 5], [2, 5], [50, 110], [87, 45], [6, 3]], [[820, 17679], [5, 3], [81, -6], [-34, 34], [48, 16], [40, 111], [3, 9], [14, -75], [137, -45], [74, 59], [67, -40], [74, 81], [64, -1], [70, -57], [9, 69], [1, 4], [8, -4], [60, 4]], [[10384, 17752], [34, 139], [166, -140], [126, 40], [138, -57], [33, -155], [33, 18], [124, -132], [40, 74], [-29, 16], [-58, 329], [160, 101], [119, -44], [-33, -71], [56, -17], [-25, -34], [53, 18], [16, -49], [97, 18], [33, -35], [51, 31], [31, -143], [60, -1], [-17, -60], [77, -72], [32, 25], [46, -35], [0, 34], [97, -34], [2, -59], [64, -25], [-2, -71], [87, -14], [162, -117], [-15, 57], [113, 9], [38, -34], [-19, -73], [41, -77], [-4, 40], [53, -40], [38, 42], [-33, -38], [62, -12], [1, -30], [92, 28], [-50, -65], [45, -13], [-4, 31], [69, 37], [60, -6], [8, -74], [33, 29], [15, -41], [-58, -44], [58, -62], [4, 52], [56, -32]], [[12760, 16914], [3, -3], [-47, -108], [42, -32], [26, -17], [1, -3], [5, -12], [18, -49], [-3, -9], [-86, -210], [106, -49], [-37, -157], [149, -63], [97, -218], [142, -77], [47, -122], [-89, -158], [84, -191], [169, -201], [103, -48], [81, -126], [-87, -88], [24, -83], [28, -87], [-2, -2], [2, -6], [-59, -80], [12, -27], [41, -90], [3, -7], [-71, -118], [60, -137], [-2, -21], [-4, -45], [-4, -40], [-18, -15]], [[13494, 14215], [-11, -9], [-14, -12], [-2, 0], [-5, 1], [-68, 11], [4, 7], [8, 16], [29, 53], [29, -2], [0, 2], [-47, 56], [-45, 56], [-86, -53], [-98, 52], [0, 0], [28, 92], [72, -16], [10, 11], [21, 29], [-153, 91], [-93, -15], [-31, 95], [-96, -64], [-3, 4], [-88, 118], [-45, 11], [-28, -49], [-226, 50], [-2, 0], [1, 4], [20, 160], [0, 1], [1, 0], [45, 33], [-97, 40], [-25, 27], [33, 17], [-37, 22], [-43, -24], [-26, -101], [-40, 3], [9, -58], [-36, -47], [-28, 15], [-43, -56], [-39, 65], [-97, -102], [-57, 5], [-8, -79], [-59, -51], [-119, 102], [-44, -32], [-31, 24], [-15, -37], [-41, 23], [-13, -62], [-242, 130], [-47, -50], [-40, 83], [-118, -331], [-111, 5], [-22, 1], [-30, 68], [-38, 6], [-4, 0], [-5, 90], [-73, 53], [-88, -131], [-69, -6], [-14, -61], [-61, 53], [16, -83], [-116, -19], [-116, -139], [-105, -21], [-74, -43], [-13, -12], [-23, -21]], [[10372, 14214], [-3, -3], [-23, 7], [-14, 5], [-4, 1], [-18, 94], [-43, 5], [19, 77], [-45, -1], [-56, 101], [-41, -28], [-3, 58], [-49, -13], [26, 35], [-99, 15], [-28, -57], [-112, 40], [-77, -78], [7, 103], [-70, -16], [-10, 38], [-64, 0], [-102, -50], [0, -38]], [[9563, 14509], [-12, 13], [-33, 186], [27, 23], [-65, 54], [72, 58], [1, 21], [-3, 36], [-81, 136], [7, 87], [38, -7], [29, 60], [61, 8], [20, -47], [43, 24], [6, 92], [68, 48], [31, 114], [58, 19], [68, 94], [51, -21], [140, 146], [47, -26], [52, 114], [-42, 50], [-9, 152], [49, 88], [50, -30], [83, 9], [11, 3], [45, 110], [-52, 41], [47, 110], [1, 167], [154, 191]], [[7776, 15462], [12, -57], [-84, -1], [-31, -36], [3, -16], [-51, -94], [45, 19], [50, -81], [-16, 11], [8, -63], [-61, -20], [-38, -141], [38, -12], [-15, -34], [70, -103], [-6, -4], [-78, 18], [-12, -47], [-64, 19], [-51, -49], [-40, 54], [-35, -98], [-48, 18], [-29, -83], [-34, 9], [-41, -190], [46, -66], [-75, 11], [-47, -45], [54, -54], [-1, -102], [57, -58], [-18, -38]], [[7284, 14129], [-151, 82], [-33, 17], [-8, 2], [7, -56], [-189, 143], [-25, -60], [-138, 80]], [[6747, 14337], [-10, 5], [47, 130], [-82, 78], [-21, 97], [-97, 64], [44, 56], [-81, 66], [-1, 1], [-80, -64], [-87, 52], [-37, -35], [-186, 12], [49, 70], [-102, 66], [-66, -101], [-149, 11], [10, 94], [-106, 152], [-81, -14], [-191, 162], [-52, -15], [-68, 43], [-27, -54], [-113, 56], [-68, 113], [-5, 6], [-148, 46], [40, 30], [-50, -13], [-71, 57], [-48, -26], [-18, -10]], [[4892, 15472], [-1, 0], [-60, 59], [49, 17], [-100, 42], [19, 51]], [[4799, 15641], [4, -1], [29, 65], [3, 20], [-58, 61], [70, 115], [-62, 36], [117, 87], [-59, 72], [-1, 1], [142, 136], [-13, 82], [61, 96], [-24, 72], [-1, 5], [60, 69], [-31, 34], [59, 61], [116, 120], [9, 1], [158, 19], [34, 55], [-32, 32], [7, 10], [15, 24], [1, 3], [1, 0], [88, -27], [57, 174], [148, 9], [25, 69], [11, 4], [9, 4]], [[5742, 17149], [31, 14], [4, 4], [40, -33], [35, 28], [21, -33], [84, -7]], [[5957, 17122], [6, 0], [0, -1], [9, -36], [47, -112], [25, 8], [10, 3], [8, -5]], [[12760, 16914], [15, 35], [100, -105], [400, -102], [66, -121], [-4, -138], [283, -343], [1, -106], [-63, -8], [85, -69], [-32, -55], [213, 52], [-45, 22], [43, 52], [-27, 43], [163, -28], [187, -196], [-19, -134], [533, -334], [177, -20], [142, -131]], [[14978, 15228], [-80, -62], [11, -36], [-52, -58], [42, -48], [-37, -43], [-55, 17], [-3, -81], [-21, 20], [-19, -34], [-126, 64], [-94, -96], [1, -66], [107, -34], [51, -58], [-36, -33], [-7, -43], [33, -15], [-30, -55], [89, -79], [-19, -61], [94, -43], [20, -96], [-72, -45], [8, -143], [-81, -37], [153, -36], [-45, -124], [19, -48], [-47, -48], [92, -71], [66, 64], [113, -23], [28, -84], [-27, -39], [32, -22], [-75, -57], [33, -42], [73, 31], [30, 62], [131, -45], [45, 98], [55, 16], [14, -99], [101, -9], [32, -71], [147, 10], [101, -79], [2, -2], [-33, -151]], [[15742, 13294], [-1, -4], [-13, 1], [-45, 2], [-60, 3], [-8, -12], [-16, -24], [-23, -46], [34, -100], [-102, -81], [36, -61], [-30, -59], [43, -24], [-2, -49], [-92, 10], [-23, 52], [-106, -9], [-50, -170], [-54, -40], [-22, -18], [-54, 29], [-9, 70], [-44, -8], [-18, 66], [-3, 2], [-103, 63], [-61, -11], [-129, -24], [-2, -54], [86, -35], [-5, -56], [-76, -79], [-10, -71], [-52, -13], [5, -123], [-37, 9], [-44, -56], [0, -112], [-1, 0], [-35, 3], [-3, -63], [-76, 15], [-64, -105], [-59, 45], [-90, -130], [10, -66], [-88, 27], [0, -2], [0, 0]], [[14246, 11986], [-33, -92], [-85, 61], [-7, 122], [-67, 41], [-13, 26], [-18, 41], [-6, 13], [-11, 69], [-3, 16], [-9, 59], [4, 2], [64, 40], [-73, 115], [-10, 369], [-48, 40], [-15, 12]], [[13916, 12920], [12, 63], [-19, 69], [-6, 4], [-25, 18], [-50, 38], [-19, 26], [-90, 150], [-65, 108], [1, 3], [2, 104], [-43, 37], [12, 109], [-65, 33], [15, 79], [-73, 20], [-60, 99], [5, 45], [12, 108], [98, 1], [1, 0], [-25, 126], [-40, 55]], [[9563, 14509], [0, -16], [42, -28], [-70, -26], [-73, 16], [-70, -73], [-131, 23], [-5, -7], [-54, -62], [-131, 19], [41, -57], [-41, -36], [-90, 25], [14, -109], [-54, -13], [-14, -72], [-178, 37], [-131, 200], [-3, 6], [-17, 3], [-71, 14], [-7, 2], [-15, -164], [-1, -1], [-80, -29], [-20, -31], [-31, -50], [-15, -24], [4, -20], [9, -47], [1, -6], [2, 1], [28, 11], [2, 1], [1, 0], [15, -136], [41, -6], [1, -92], [-40, -35], [87, -54], [-9, -87], [28, -26], [-53, -83], [-27, -42], [-2, -3], [107, -68], [-15, -16], [-18, -13]], [[8520, 13335], [-62, 21], [-21, 108], [-48, -40], [26, 101], [-81, -9], [-76, 41], [12, 141], [40, 15], [-59, 73], [23, 42], [-66, -4], [72, 138], [-40, 0], [6, 69], [-4, 6], [-74, 14], [-43, -72], [-69, 7], [66, -103], [-68, 23], [-106, -33], [-37, 40], [-40, -30], [-47, 45], [-82, -58], [-62, 39], [26, 48], [-50, 65], [-51, 34], [-28, 10], [-56, -53], [-109, 75], [-54, -21], [-60, 22], [-11, 3], [-18, 7], [15, 30], [0, 0]], [[4476, 15826], [3, 2], [100, -104], [66, -156], [84, 77], [71, -3], [-1, -1]], [[4892, 15472], [-45, -23], [19, -114], [-93, -159], [-50, -85], [-10, -17], [-3, 0], [-65, -8], [11, -38], [-49, -17], [-40, -103], [-48, -35], [71, -59], [1, -1], [-24, -35], [38, -68], [-27, -44], [-12, 3], [-81, 17], [-22, 5]], [[4463, 14691], [-36, 8], [-3, 6], [-55, 98], [-112, 32], [-9, -14], [-7, -45], [-135, -169], [23, -142], [-141, -96], [-179, -60], [-99, -154], [-80, 110], [-38, -6], [-98, 84], [29, 23], [-34, 52], [33, 41], [-40, 57], [-59, -14], [-50, 53], [-223, -8], [17, 34], [-33, 52], [-65, -57], [-37, -32], [-20, -18], [-7, -37], [4, -11], [69, -22], [-43, -48], [-59, 27], [-25, -44], [35, -45], [-77, -91], [7, -7], [87, -78], [7, -5], [-26, -34], [17, -39], [3, -7], [-51, 11], [-65, -88], [54, -76], [-97, -35], [-47, 69], [-2, -1], [-28, -37], [-91, -11], [-2, -41], [-62, -41]], [[2613, 13835], [-1, 0], [-4, -3], [-37, 31], [-57, -55], [-63, 128], [42, 56], [-45, 124], [-192, 111], [36, 61], [-18, 129], [24, 53], [106, 28], [-64, 77], [-3, 137], [-106, 98], [91, 69], [-141, 54], [-2, 110], [-109, 114], [-86, 51], [-100, -9], [-52, 76], [-38, 8], [-6, 1], [-1, 0], [-67, -125], [-2, -4], [-93, 18], [-112, -99], [-96, 36], [8, 18], [-1, 2]], [[1424, 15130], [16, 33], [32, 77], [-1, 9], [-83, 15], [-6, 33], [8, 6], [74, 61], [-109, 126], [14, 34], [-94, 32], [57, 67], [-83, 99], [3, 0]], [[6747, 14337], [-37, -19], [-29, -15], [-89, -1], [-51, -74], [48, -63], [7, -95], [-29, -28], [120, -76], [50, 10], [15, -46], [-51, -76], [20, -3], [127, -17]], [[6848, 13834], [-42, -58], [-121, 43], [-10, -28], [-10, 40], [-52, -61], [-215, -15], [-143, 97], [-117, 237], [-66, -44], [-24, -77], [50, -54], [-41, -43], [-6, -79], [-31, -20], [-22, 46], [-195, -90], [-21, 29], [40, 38], [-34, 71], [-42, 17], [-4, -40], [-57, -11], [15, 62], [-106, 47], [-49, -18], [-116, 124], [-108, -8], [-66, 141], [-21, -15], [-60, 66], [-26, 210], [-52, -28], [-60, 59], [-131, -23], [-39, 102], [-107, -12], [-42, 63], [-1, -1], [-70, -38], [22, -53], [-84, -74]], [[4584, 14436], [-127, 80], [27, 32], [-21, 143]], [[2613, 13835], [3, -5], [18, -20], [-1, -6], [-3, -63], [-100, -90], [101, -66], [16, -91], [58, -55], [-107, -91], [45, -45], [38, 32], [33, -22], [4, -66], [-76, 15], [-63, -80], [60, -73], [-30, -43], [17, -56], [-50, -75], [69, -23], [-1, -5], [-8, -45], [10, -7], [82, -61], [17, -12], [73, 121], [59, -15]], [[2877, 12888], [5, -30], [20, -119], [-91, -67], [2, 94], [-13, 3], [-6, 1], [-7, 2], [-5, -16], [-75, -87], [1, -2], [-3, -2], [34, -77], [-36, -44], [-73, 69], [28, 120], [-113, 45], [-49, -27], [6, -58], [58, -17], [-36, -67], [-66, 34], [1, 136], [-109, -80], [4, -53], [-91, -9], [-26, -89], [-63, 46], [-72, -21], [-131, 49], [-50, -50], [-125, 6]], [[1796, 12578], [-37, 50], [-3, 8], [-3, 0], [-51, -1], [2, 107], [-59, 40], [75, 59], [2, 87], [-141, -51], [-99, 53], [-8, 48], [-51, 7], [-72, 95], [-111, 0], [-105, 50], [-7, 69], [-54, 49], [54, 128], [-117, 75], [-2, 78], [-44, 58], [-152, 51], [29, 62], [-35, 25], [-3, 74], [68, 72], [-46, 30], [-31, -21], [-56, 57], [114, 166], [-36, 195], [-120, 28], [-3, 56], [53, 28], [-5, 9], [-32, 63], [7, 133], [-88, 41], [0, 0], [44, 96], [116, 30], [6, 9], [32, 52], [14, 23], [52, -1], [168, -3], [12, -1], [-37, 74], [32, 56], [3, 5], [18, -14], [102, -45], [2, 1], [26, 24], [37, 45], [-8, 134], [55, 34], [30, -49], [48, 30], [43, -26]], [[14978, 15228], [199, -185], [92, -20], [165, -152], [131, 11], [2, 61], [115, 176], [95, 19], [254, -20], [166, -215], [88, -28], [14, 32], [173, -86], [0, -1]], [[16472, 14820], [-1, 1], [-30, -148], [-51, -56], [-61, 6], [-123, -207], [33, -18], [-4, -85], [-133, -144], [18, -32], [-40, -96], [-131, -81], [-68, -113], [-6, -10], [48, -66], [-14, -42], [75, 13], [-49, -47], [44, -84], [-15, -76], [31, -22], [-22, -171], [0, -3], [60, -25], [-9, -60], [48, -17], [-30, -64]], [[16042, 13173], [-17, 18], [-35, 35], [-31, -5], [-150, -23], [-67, 96]], [[13916, 12920], [0, 0], [-56, 25], [0, 0], [15, -56], [-65, -36], [37, -21], [-10, 2], [-19, 4], [1, -65], [-29, -7], [25, -57], [-17, -17], [-38, -39], [-19, -20], [-55, -13], [-19, 67], [-66, 26], [23, 69], [-53, 15], [33, 72], [-60, 58], [-17, 87], [-36, -101], [-72, 9], [-32, -77], [-64, 37], [-10, -46], [47, -17], [-28, -31], [35, -105], [-30, -30], [-24, 37], [-58, -75], [0, -57], [-78, 31], [-31, 80], [-80, -33], [-115, 62], [-68, -39], [-39, -105], [18, -27], [-59, -36], [-43, -6], [0, 17], [-84, 21], [-80, -3], [-107, 48], [-11, -85], [-30, 9], [-62, -93], [-32, 24], [10, -158]], [[12364, 12265], [0, 0], [-74, -26], [-40, -91], [-97, -4], [-115, -96], [-3, -4], [-2, 5], [-3, 1]], [[12030, 12050], [-15, 55], [-50, -24], [-7, 81], [56, 29], [1, 75], [-55, 42], [-24, 111], [-28, 31], [-71, -23], [-61, 97], [-21, -20], [-76, 46], [-79, -73], [-116, 40], [-49, 73], [27, 49], [-72, -22], [-35, 70], [-51, -33], [-45, 60], [-57, -1], [27, 27], [-18, 26], [-46, -88], [-195, -50], [65, 47], [-70, 162], [20, 48], [-46, 20], [-15, 56], [-87, -22], [26, 24], [-31, 54], [38, -16], [7, 42], [-45, 13], [-9, 115], [-90, 46], [23, 35], [-66, 44], [-40, 72], [17, 34], [-86, 67], [-63, -5], [-40, 114], [-60, 46], [-66, 222], [62, 69], [-59, 19], [-22, 97], [38, 32], [1, 145], [0, 6]], [[4584, 14436], [49, -59], [-67, -69], [37, -47], [-41, -56], [36, -31], [-60, -101], [7, -95], [-95, 7], [-177, -150], [-53, 40], [-24, -75], [-92, -36], [-7, 55], [-90, 30], [-47, -34], [-12, -115], [-30, 32], [-50, -27], [-27, 42], [-32, -25], [40, -49], [-38, -40], [-63, -26], [-27, 39], [-98, -17], [22, -36], [-53, -46], [-16, -161], [-80, -109], [82, 17], [-7, -101]], [[3571, 13193], [-48, 3], [-10, 1], [-13, -70], [-127, -93], [-105, 18], [-71, 88], [-1, -2], [-2, 3], [-57, -53], [-20, 8], [-14, -40], [-1, -5], [-1, -2], [120, -45], [-50, -75], [-90, 42], [-4, 1], [0, 0], [-10, -47], [-76, -52], [-24, 19], [7, -28], [-98, 28], [1, -4]], [[6848, 13834], [46, -87], [-31, -72], [170, -82], [-8, -78], [-78, -80], [36, -35], [-22, -31], [37, -36], [-9, -61], [-115, -64], [102, -67], [-4, -84], [65, -82], [4, -5], [-18, -5], [-56, -42], [-1, -11], [6, -13]], [[6972, 12899], [-9, -5], [-10, -6], [-116, 27], [-53, 107], [-72, 57], [-105, 5], [0, 75], [-83, 30], [-26, 106], [-1, 0], [-121, -63], [-227, 55], [-16, -43], [-29, 32], [-29, -43], [-47, 1], [-9, 43], [-63, 7], [3, 72], [-43, -18], [-124, 101], [0, -1], [-52, -132], [-108, -101], [5, -45], [48, -23], [-64, -114], [-169, -54], [-22, -48], [-66, 41], [-64, -50], [-53, 46]], [[5247, 12958], [-20, 133], [-86, 13], [-11, 27], [-37, 96], [-5, 1], [-82, 14], [-13, 44], [80, 152], [-7, 63], [-103, -6], [-75, 83], [-32, -7], [-32, -120], [-73, -15], [-65, -113], [-106, 82], [-14, -90], [-49, -62], [-89, 6], [89, -140], [-26, -111], [22, -16], [-40, -39], [-68, 118], [-72, -51], [-112, 42], [-39, -37], [-106, 122], [-25, -7], [-70, -52], [-12, -9], [-7, -5], [-40, 46], [-22, 19], [-5, -3], [-26, -21]], [[3869, 13115], [-39, 30], [-57, -6], [-110, 48], [-92, 6]], [[12030, 12050], [1, -4], [-111, 41], [-39, -48], [-30, 19], [-17, -49], [-84, 46], [-125, -149], [-174, 57], [-12, -30], [45, -25], [-103, -23], [-30, -65], [-106, -7], [-1, -31], [-1, -14], [-1, -10], [-19, -6], [-82, -28]], [[11141, 11724], [0, 0], [-15, -49], [-35, 26], [-95, -81], [-44, 16], [-114, -55], [-39, -46], [10, -50], [-62, -24], [13, -102], [-58, -97], [-1, 2], [-75, 156], [-87, -40], [-10, 13], [-66, 95], [12, 63], [-38, 90], [-107, 20], [-2, 0], [4, 5], [28, 104], [-96, 13], [-66, -2], [-9, -18], [5, -16], [-23, -21], [-36, -70], [-21, -82], [-24, -9], [-26, -9], [-118, -42], [23, 152], [-106, -73], [-55, 29], [-50, 25], [-14, 8], [-36, -46], [-8, -15], [16, -65], [-65, 2], [-135, -134], [-3, -4]], [[9513, 11393], [-87, 103], [-1, 7], [-4, 4], [-8, 3]], [[9413, 11510], [11, 10], [-4, 50], [-88, 144], [-11, 97], [45, 73], [-90, 20], [-91, 88], [-2, 2], [34, 37], [-36, 27], [36, 40], [-77, 106], [-57, -28], [-48, 53], [-136, 6], [20, 41], [-173, 36], [-2, -1], [-1, 1], [-1, 0], [-267, 185]], [[8475, 12497], [-82, 35], [3, 72], [2, 42], [1, 11], [3, -1], [39, -8], [59, 70], [-23, 36], [21, 71], [-51, 143], [-69, 49], [-4, 26], [-6, 34], [-2, 8], [4, 4], [119, 136], [-27, 69], [58, 41]], [[8475, 12497], [-11, -34], [-13, 3], [-53, 12], [-94, -184], [-204, 80], [-16, -58], [-140, -58], [-122, 194], [-256, 7], [-61, 67], [-43, -21], [-13, -85], [-1, -7], [-3, -1], [-23, -3]], [[7422, 12409], [1, 12], [-33, -11], [-60, 91], [-1, 0], [0, 1], [-44, -38], [-50, 11], [3, 66], [-71, 41], [34, 118], [-56, 39], [-65, -26], [8, 95], [-2, 0], [-76, 5], [-10, 40], [-14, 54], [-14, -8]], [[5247, 12958], [-16, -8], [-4, 2], [0, -5], [21, -214], [-97, -53], [100, -23], [128, -138], [-26, -71], [-79, -10], [-4, -39], [287, -173], [18, -25], [-1, -4], [-34, 12], [-12, 9], [-4, -3], [-42, -24], [6, -55], [44, -15], [-25, -74], [-84, 52], [-26, -31], [38, -42], [-33, -107], [32, -19], [-39, -33], [78, -36], [-47, -39], [0, -8], [2, -18], [1, -15], [3, -1], [6, -3], [47, -19], [-34, -44], [1, -1], [-1, 0], [3, -3], [69, -79], [42, 28], [1, -1], [-47, -75], [22, -37]], [[5541, 11516], [-1, -1], [-69, -47], [-35, 14], [-87, -119], [-35, 12]], [[5314, 11375], [-2, 9], [-4, -7], [-39, -64], [-6, -11], [-41, 10], [-9, 57], [-46, -30], [-55, 37], [0, -3], [-3, 2], [-11, -150], [-59, 4], [-47, 9], [-18, 90], [-50, 42], [13, 98], [-6, 1], [-77, 15], [43, -90], [-37, -24], [-86, 96], [15, 65], [-5, 0], [-95, -4], [-76, 58], [-4, -5], [-25, -27], [44, -35], [0, -11], [-7, -62], [-30, 5], [-9, 80], [-91, 13], [-9, 99], [-94, -27], [7, 88], [-96, 169], [-99, 15], [-104, 95], [-34, -38], [-64, 7], [-60, -78], [0, 166], [74, 6], [32, 56], [64, -8], [7, -1], [2, 17], [1, 25], [1, 3], [-52, 10], [-35, 7], [-2, 0], [-54, -71], [-31, 61], [-38, -3], [-32, -46], [10, -92], [-37, -28], [-38, 10], [-41, 94], [-58, 16], [-6, -35], [1, -1], [4, -5], [55, -60], [-56, -40], [-35, 3], [-16, 5], [14, 66], [-93, 98], [64, 136], [3, 6], [-85, 121], [55, 111], [-32, 42], [53, 51], [-42, 33], [49, 121], [-80, 76], [35, 13], [12, -39], [83, 48], [48, -46], [55, 6], [90, 298], [-1, 0], [-17, 12]], [[19497, 12100], [-57, -25], [-32, 24], [-1, 0], [1, -4], [2, -49]], [[19410, 12046], [0, -9], [-237, -12], [-1, -1], [33, -33], [-19, -2], [-66, -10], [-4, -7], [-18, -39], [-5, -18], [41, -94], [-43, 26], [-40, -11], [-3, -42], [-71, 6], [-49, 53], [-25, -74], [-168, -30], [-18, -60]], [[18717, 11689], [-1, -4], [-3, -12], [-26, 51], [-3, 4], [-15, -2], [-34, -4], [-1, 0], [-47, 93], [-113, -70], [-35, 72], [-80, 36], [-10, 120], [-87, 118], [-133, -44], [-13, 35], [-188, 19], [-3, 32], [-73, -69], [-162, 1], [-86, -86]], [[17604, 11979], [-18, 38], [4, 37], [-33, 23], [-97, 68], [-4, 2], [0, 0], [-26, -21], [0, 2], [16, 42], [-53, 47], [-12, 72], [0, 0], [-61, -12], [32, -39], [-80, 0], [1, -2]], [[17273, 12236], [-3, 0], [-51, 52], [18, 64], [108, 76], [123, -102], [161, 37], [-74, 66], [37, 8], [-7, 40], [-4, 0], [0, 3], [-127, -16], [28, 72], [-75, 22], [87, 59], [-6, 1], [-38, 6], [-22, 4], [27, 74], [-2, 0], [0, 1], [-14, 2], [-54, 12], [52, 59], [22, 26], [3, 3], [-93, -18], [-2, 4], [30, 59], [58, -18], [-4, 12], [-1, 6], [-4, 15], [-11, 0], [-32, 1], [-47, 2], [-24, 7], [50, 45], [7, 87], [5, 6], [64, 15], [93, 22], [114, 199], [66, -54], [52, 38], [45, -62], [7, 36], [65, -1], [27, -37], [18, 55], [-4, -45], [50, 38], [62, -24], [-16, 37], [42, -36], [-13, 52], [43, -32], [-1, -44], [54, 59], [75, -40], [1, 1], [28, 21], [-45, 92], [-16, 33], [-87, 75], [24, 46], [13, 15], [3, 16]], [[18158, 13488], [31, 62], [606, -7], [81, -123], [259, -72], [494, 19], [94, -136], [176, -127], [-21, -66], [87, -113], [-3, -3]], [[19962, 12922], [-23, 0], [-53, -34], [1, -29], [-79, -66], [0, -2], [-1, -1], [24, -54], [-121, -76], [25, -70], [-61, -71], [-118, -7], [34, -241], [-59, -27], [-9, 48], [-12, -2], [-100, -17], [30, -99], [42, -44], [15, -30]], [[19532, 12238], [31, -9], [-11, -9], [-16, -9], [-4, 27]], [[7422, 12409], [-4, -39], [-11, -102], [0, -10], [76, -68], [-3, -3], [-97, -86], [39, 8], [8, -82], [128, 1], [-33, -67], [22, -32], [-103, -49], [-63, -252], [-85, -110], [-97, 8]], [[7199, 11526], [-11, 29], [-11, 28], [-15, 11], [-44, 13], [-92, 29], [-200, -74], [-46, 30], [-6, 45], [-38, 56], [-6, 3], [-174, -45], [-103, 20], [14, -64], [-34, -20], [-33, 53], [-41, -10], [-168, -161], [-66, 130], [-48, -12], [-58, 42], [-44, -70], [-67, 57], [-6, -83], [-81, 20], [-95, -35], [-45, 52], [-69, -87], [-71, 32], [0, 1]], [[19962, 12922], [164, 3], [144, 149], [-7, 55], [78, 86], [-80, 86], [57, 77], [-44, 54], [21, 30], [706, -38], [695, -370], [107, -85], [-6, -44], [-82, -49], [88, -45], [24, -11], [124, 61], [65, -37], [6, -7], [2, -12], [-5, -46], [-30, -36], [-35, -31], [-12, -12]], [[21942, 12700], [0, 2], [-84, 30], [-10, 22], [-36, 14], [-85, -46], [-10, -8], [39, -95], [-105, -46], [-30, 32], [-41, -20], [41, -59], [44, 14], [37, -75], [-37, -46], [-72, 47], [1, -7], [43, -91], [-97, -75], [138, 31], [-45, -47], [-33, -193], [-79, -14], [-12, -64], [-87, -45], [71, -12], [1, -76], [-123, 7], [23, -69], [60, -6], [-33, -72], [1, -10], [10, -11], [53, -30], [-17, -7], [-87, -41], [-27, -141], [-3, 1], [-134, 49], [-10, -67], [-175, 32], [7, -95], [-47, -5], [-18, -64], [-50, 15], [-6, 63], [-59, 5], [-41, -52], [-14, -113], [-1, -4], [-18, 3], [-5, 0], [-1, -3]], [[20779, 11258], [-89, 20], [-65, 14], [-4, 42], [-83, 64], [8, 61], [-67, 34], [-70, -22], [23, 46], [-262, 16], [-5, -47], [-5, 1], [-34, 8], [-21, 21], [-40, 40], [-3, 77], [91, 42], [9, 96], [43, -10], [-17, 89], [82, 40], [-13, 51], [77, 61], [-58, -20], [-93, 65], [-12, -42], [-48, 18], [-18, -45], [-74, 21], [-62, -98], [13, -73], [-91, -43], [2, 180], [-64, -47], [-41, 18], [-3, -29], [-43, 103], [-2, 0], [-156, 16], [-29, 51], [-38, -40], [-12, 67], [-2, -1], [-6, -3]], [[5314, 11375], [17, -66], [10, -43], [7, -9], [8, 17], [22, 50], [4, 9], [7, -9], [78, -102], [-6, -57], [84, 32], [22, -92], [115, -135], [0, -2], [-4, -9], [-15, -34], [-46, -38], [-58, 46], [-71, -1], [-40, -122]], [[5448, 10810], [-12, 2], [-81, 66], [-71, -42], [-4, 14], [-18, 61], [-1, 3], [-28, -7], [-87, -23], [-14, -3], [-1, -3], [-20, -52], [-12, -31], [0, -1], [-139, 29], [-6, 1], [-57, -78], [-103, -11], [-104, 57], [-77, 63], [-195, 26], [-87, 142], [-123, -32], [-74, 175], [-44, 19], [-3, -1], [-239, -54], [-32, -103], [-14, -42], [-13, -5], [-128, 61], [0, 0], [-10, 5], [-10, 6], [-72, 177], [7, -8], [32, -37], [77, 20], [123, 172], [-37, 40], [-14, 14], [-2, -34], [-51, 4], [-45, 72], [-28, -32], [-19, -23], [-1, 0], [-1, 0], [-197, 65], [-23, 1], [-88, -28], [-3, -3], [10, -10], [22, -13], [0, -7], [-2, -26], [-1, -12], [-1, 1], [-52, 15], [-11, -33], [30, -82], [-2, 0], [-2, 1], [-39, 10], [-53, 14], [-105, 28], [23, -156], [-36, 6], [-57, -74], [-2, -2], [-17, 30], [46, 49], [-36, 7], [19, 50], [-50, 63], [-88, -51], [-51, 62], [-74, -46], [-28, 49], [-77, 3], [-30, 39], [8, -102], [-40, -25], [-149, 108], [-128, 42], [-56, -26], [-105, 56], [-23, -57], [-67, 12], [-24, -36], [-7, -135], [-52, 10], [-29, -36], [-35, 30], [-38, -66], [-175, -55], [-32, -53], [-7, 39], [-109, -68], [-157, -8], [-28, 26], [12, 59], [-63, 11], [-44, -210], [49, -72], [0, -1], [-20, -27], [-34, -47], [-2, -3], [-99, -23], [2, 4], [11, 34], [38, 47], [-17, 17], [-14, 13], [-20, 20], [-70, -38], [-11, 126], [-101, 107], [102, 63], [-7, 63], [102, 33], [4, 48], [96, 55], [299, 88], [29, 60], [32, -26], [189, 142], [37, -42], [13, 49], [148, 55], [68, -18], [7, 45], [42, 7], [-27, 18], [31, 15], [10, 4], [-39, 83], [-5, 0], [-15, 1], [-23, 1], [-2, -13], [-22, -121], [-59, 13], [-71, 110], [11, 53], [-42, 22], [-1, 1], [-46, -111], [-75, 31], [-37, -55], [-23, 41], [-70, 2], [-73, 59], [-51, -33], [39, 101], [-4, 19], [-7, 33], [-1, 5], [-10, -1], [-67, -8], [-76, -8], [7, 65], [-65, 28], [38, 38], [64, -15], [40, 49], [153, 25], [4, 95], [47, 26], [-11, 46], [32, 40], [57, -36], [7, 2], [8, 3], [51, 18], [26, 32], [47, 59], [-49, 54], [-61, 5], [19, 92], [-31, 0], [8, 28]], [[16042, 13173], [8, -8], [48, -12], [40, 14], [45, 15], [16, -45], [-34, -57], [91, -58], [21, -105], [141, -51], [218, -172], [-2, -46], [126, -54], [38, -70], [-23, -58], [-94, 85], [-85, -49], [-57, -197], [38, -72], [91, -49], [21, -86], [66, -7], [7, -45], [64, 18], [26, -144], [57, 16], [82, -58], [43, 71], [35, -26], [37, 27], [-15, 70], [70, -29], [54, 90], [-22, 11], [13, 105], [51, -46], [12, -11], [35, 14], [1, 3], [9, 16], [-41, 63]], [[17604, 11979], [7, -16], [21, -22], [-7, -14], [-36, -69], [36, -12], [18, -125], [31, 25], [106, -72], [-95, 1], [-68, -51], [-3, -82], [-164, -67], [-74, -161], [0, -1], [-141, 105], [-29, -17], [-151, 84], [-33, -51], [-79, -14], [-122, 171], [-133, -156], [-53, 22], [-4, -3], [-73, -80], [52, -84], [-2, -4], [-61, -105], [78, -157], [7, -2], [-2, -7], [-15, -44], [0, -3], [-44, 29], [-29, -25]], [[16542, 10972], [-19, -15], [-102, 108], [-73, -31], [-89, 25], [-170, -88], [-41, -3], [-133, -8], [-103, -86], [-81, -17], [-192, 37], [-80, 135], [-200, 65], [-33, 76], [-96, -44], [-136, 51], [2, 2]], [[14996, 11179], [41, 46], [-123, 172], [47, 90], [-28, 62], [-30, 21], [-6, -32], [-97, -25], [-11, 38], [-106, 48], [9, 63], [-90, 52], [-39, 79], [-78, -1], [-93, 81], [-63, -5], [-83, 118]], [[14996, 11179], [-4, 2], [-103, -99], [-45, 28], [-108, -44], [-49, 30], [-37, -84], [-88, 40], [-37, 57], [-39, -59], [-41, 4], [-3, 0], [-17, -104], [-9, -56], [-40, -28], [55, -61], [-8, -68], [112, -94], [-34, -88], [25, -74], [-5, 1], [-79, 21], [-8, -7], [-92, -73], [-2, -74], [46, -16]], [[14386, 10333], [18, -7], [-25, -80], [0, -3], [58, -14], [-27, -80], [-70, -56], [30, -17], [-12, -22], [-47, -83], [25, -64], [1, -5], [-107, 11], [12, 81], [-89, -25], [-94, 118], [-82, -54], [-129, 57], [-31, 53], [-98, -164], [-13, 4], [-8, -12]], [[13698, 9971], [-7, 8], [-63, 27], [-25, -46], [-37, 14], [-13, 5], [-5, 16], [-70, 81], [-46, 25], [-259, 104], [-67, -72]], [[13106, 10133], [9, 71], [57, 54], [-48, 87], [159, 237], [-86, 202], [0, 1], [34, 35], [44, 45], [-60, 34], [-24, -48], [-43, 36], [-46, -52], [-10, 86], [39, 36], [-89, 17], [-36, 79], [-129, 9], [35, 126], [-42, 75], [37, 45], [-12, 45], [-74, -13], [-27, 66], [-73, 44], [4, 57], [68, 48], [-109, -3], [-26, 34], [21, 32], [-102, 56], [17, 74], [-28, 18], [28, 49], [-76, -1], [-20, 65], [70, 77], [59, -37], [80, 28], [38, 72], [-106, 48], [28, 50], [-42, 83], [-43, 24], [-25, -36], [-119, 11], [-73, 65], [-1, 1], [0, 0], [0, 0]], [[21942, 12700], [-7, -6], [0, -60], [21, -41], [1, -2], [0, -1], [182, -79], [5, -4], [-9, -52], [64, -46], [12, -43], [10, -39], [-113, -143], [-40, -172], [58, -25], [167, 80], [45, -29], [-44, -97], [-54, -12], [159, -94], [-39, -128], [63, -72], [-36, -28], [30, -82], [-74, -71], [47, -7], [-11, -28], [54, -43], [-24, -52], [25, -68], [1, -1], [121, -44], [39, 84], [25, -41], [91, -8], [-18, -122], [82, -71], [50, -1], [27, 93], [94, 49], [88, -44], [30, -40], [10, -45], [-29, -66], [31, -55], [0, -2], [-88, -188], [42, -69], [11, -20], [7, 4], [112, 72], [65, -2], [78, -245], [258, -51], [8, -120], [13, -2], [4, -22], [4, -21], [-129, -43], [-232, 7], [-23, -47], [-60, -16], [-259, 94], [-131, -1], [-5, 44], [-151, -21], [-82, 32], [9, -135], [-119, -192]], [[22408, 10000], [-1, -1], [-135, 103], [-151, 186], [-18, 172], [-166, 60], [-58, 162], [-152, 92], [-28, -54], [-73, 22], [-3, -95], [-118, -90], [-4, -2], [0, 0], [-43, 24], [-22, -51], [-8, 2], [-78, 18], [-5, -41], [-77, -15], [-8, -2], [0, 2], [10, 60], [-59, 10], [-82, -156], [-86, 4]], [[21043, 10410], [-5, 19], [-6, 23], [-29, 115], [25, 88], [-43, 46], [39, 57], [-114, 21], [29, 111], [0, 1], [-3, 4], [-31, 61], [-77, 60], [4, 47], [-122, 61], [0, 0], [34, 33], [14, 14], [7, 6], [15, 81], [-1, 0]], [[9413, 11510], [-64, 27], [0, 0], [-46, -53], [47, -39], [-31, -33], [-77, 84], [-2, 2], [-9, -7], [-40, -36], [-8, -6], [-1, -50], [0, -10], [0, -4], [33, -8], [-33, -80], [18, -45], [-27, -40], [76, -66], [-62, -50], [-48, 38], [-110, -24], [-7, -77], [18, -43]], [[9040, 10990], [-10, -9], [-96, 47], [-5, -9], [-40, -77], [45, -28], [-75, -85], [-101, 40], [-105, -58], [-69, 95]], [[8584, 10906], [-51, 69], [-45, -33], [-39, 20], [-40, 53], [15, 51], [-142, 115], [13, 71], [-35, 18], [-100, -27], [-76, 29], [-64, -70], [8, -34], [-74, -17], [-71, 59], [-132, 24], [-96, 92], [-36, -13], [11, 46], [-36, 55], [-44, -5], [10, 46], [-118, -18], [-25, -37], [-103, 73], [-49, -49], [1, -11]], [[7266, 11413], [-17, 9], [-8, 4], [-63, 64], [2, 12], [19, 24]], [[13106, 10133], [-64, 4], [-62, -107], [-72, 12], [-23, -77], [-140, -136], [-11, -2], [-26, 29], [-14, 29], [-17, 7], [-13, 15], [-77, 24]], [[12587, 9931], [-65, 74], [-1, 0], [-19, -5], [-17, -5], [16, 27], [-125, 42], [23, 12], [-34, 11], [-20, 72], [-46, -21], [-6, 45], [-20, -26], [-43, 21], [11, 84], [-56, -20], [-14, 28], [31, 26], [-123, 129], [-156, -75], [-27, 77], [-58, 0], [9, 61], [-44, 23], [-88, -122], [-39, 35], [-9, 60], [-57, 19], [73, 57], [-68, 66], [29, 4], [-5, 74], [-38, 72], [3, 4], [127, -16], [22, 127], [-62, 39], [-138, 233], [-165, 57], [44, 93], [-63, 23], [-42, 129], [-49, 3], [-39, -75], [-62, 46], [58, 88], [52, -20], [12, 107], [1, 9], [-66, 43], [-93, 58]], [[18717, 11689], [10, -11], [-3, -9], [-25, -67], [47, -54], [-20, -92], [28, -27], [46, -25], [-13, -9], [-38, -28], [-104, 50], [8, 39], [-49, -6], [-68, -87], [75, -5], [-6, -52], [-49, -11], [57, -74], [-84, -10], [-21, -72], [-56, -25], [14, -42], [40, 26], [56, -57], [83, -3], [14, -190], [144, 45], [-41, -112], [60, 13], [10, -48], [73, -42], [8, -4], [38, -117], [-40, -65], [16, -65], [-3, 1], [-87, 17], [-82, -82], [52, -139], [1, -4], [-82, 69], [-42, -96], [-84, 21], [-28, -55], [-97, 23], [-15, -20], [-18, -124]], [[18442, 10064], [-41, -6], [-304, -46], [-159, 185], [-276, 0], [-334, 179]], [[17328, 10376], [-1, 1], [0, 0], [-89, -49], [-171, 48], [-10, 12], [-232, 251], [-112, 56], [-9, 20], [-110, 227], [-6, 3], [-21, 13], [-25, 14]], [[21043, 10410], [13, -54], [5, -87], [-55, -24], [50, -106], [-2, -11], [-5, -28], [-6, -31], [-3, -1], [-44, -11], [0, -1], [-41, -191], [-151, -11], [-24, -86], [-20, 44], [-16, -2], [-123, -29], [-10, -15], [0, -97], [-54, -15], [93, -154], [-62, -47], [1, -12], [6, -29], [114, 29], [-43, -64], [71, 3], [39, -111], [34, 83], [43, -10], [-70, 63], [116, 23], [1, -134], [90, 29], [-38, -79], [55, -22], [-19, -31], [37, -48], [51, 27], [-48, -31], [-15, -74], [120, 64], [26, -15], [5, -3], [3, -2], [15, -95], [-106, -53], [-28, -203]], [[21048, 8758], [-68, 52], [25, 55], [-123, 9], [-137, 90], [-30, 5], [-190, 0]], [[20525, 8969], [4, 16], [-108, -16], [-54, 34], [0, -38], [-255, 280], [-5, 4], [-114, -1], [-235, 167], [-147, 11], [-17, -5], [-146, -41]], [[19448, 9380], [-145, 22], [-4, 4], [-6, 6], [-52, 53], [9, 9]], [[19250, 9474], [83, 84], [2, 70], [-43, 53], [37, 75], [231, 77], [68, -70], [16, 109], [37, 27], [-48, 28], [-39, -27], [-73, 55], [-48, 86], [-8, 119], [-90, 26], [4, 66], [77, 59], [-7, 86], [-100, 2], [48, 97], [-84, 11], [-24, 55], [0, 0], [33, 74], [49, -40], [-3, 50], [85, 4], [-8, 37], [42, 30], [-30, 124], [-32, -43], [-60, 10], [26, 51], [-55, 93], [73, 49], [-5, 84], [52, 76], [-15, 54], [28, 49], [45, -67], [206, 36], [1, 59], [-44, 46], [40, -16], [17, 28], [-46, 82], [64, 5], [-48, 48], [-27, -37], [-23, 13], [-1, 1], [-10, 5], [51, 27], [-46, 51], [87, 40], [10, 15], [-42, 75], [4, 24], [-11, 24], [-127, -50], [-3, -1], [-1, -1], [11, 37], [-41, 27], [-6, -51], [-24, 39], [-26, -27], [-4, -4], [16, 58], [-26, 15], [42, 62], [-64, 32], [61, 14], [-45, 40], [-36, 130], [-13, 3]], [[19250, 9474], [-164, 239], [-386, 149], [-178, 165], [-17, 15], [-25, 4], [-38, 18]], [[12587, 9931], [17, -20], [-32, -49], [-2, -3], [0, -1], [41, -36], [1, -1], [-44, -4], [2, -14], [24, -21], [-11, -62], [-23, -17], [-18, 50], [-12, -46], [100, -144], [-10, -39], [66, -35], [-30, -15], [35, -52], [-9, -59], [67, -18], [-166, -93], [0, -142], [-163, -11], [1, 140], [-39, 44], [-33, -65], [-32, 46], [-78, -37], [-4, 53], [-78, -6], [-6, 48], [-69, 4], [-50, -110], [56, -30], [73, -38], [0, -1], [5, -55], [56, -53], [-47, -77], [-81, -135], [-73, -34], [-15, -99], [-160, 44], [-20, -40], [44, -30], [-25, -45], [-49, 18], [-53, -53], [-141, -44], [-28, -63], [40, -108], [-76, 30]], [[11538, 8403], [-11, -74], [-74, 103], [24, 55], [-123, 16], [-5, 1], [-5, 11], [-70, 88], [40, 139], [-27, 47], [-12, 10], [-5, 5]], [[11270, 8804], [-46, 43], [-6, 18], [-91, 260], [-93, 154], [-165, 175], [-140, 62], [-105, 106], [-64, 87], [34, 59], [-17, 68], [-213, 127], [-5, 12], [-13, 33], [-64, 149], [-42, 216], [-77, 136], [-264, 100], [-91, 88], [-2, 4], [-59, 139], [22, 181], [62, 42], [-2, 85], [-213, 133], [-103, 112]], [[7266, 11413], [13, -7], [-10, -61], [-11, -62], [-3, -22], [-27, -164], [39, -45], [-47, -56], [25, -70], [-47, -79], [59, -175], [-1, -2], [-121, -164], [36, -24], [-31, -36], [42, -46], [-123, -145], [-79, -4], [-123, -202], [6, -59], [-49, -19], [20, -30], [81, 23], [23, -110], [139, 55], [63, -37], [-162, -63], [-19, -30], [61, -45], [-19, -61], [-155, -103]], [[6846, 9570], [-8, 4], [-7, 3], [-48, -49], [-142, 58], [-29, -1], [-23, -1]], [[6589, 9584], [-115, 47], [-14, -38], [-82, -2], [-55, 44], [61, 94], [-11, 66], [-38, -6], [-25, 86], [-29, -11], [-36, 118], [-66, -11], [21, 26], [-23, 55], [-56, 50], [-2, 62], [88, 19], [-74, 151], [43, 111], [1, 2], [-25, 5], [-58, 11], [-5, 1], [-111, -79], [33, 138], [-148, 57], [-122, -33], [-180, 240], [-113, 23]], [[11270, 8804], [-149, -29], [-25, -96], [-62, -37], [-74, 31], [-59, -82], [-19, 33], [-71, -3], [-60, -107], [-81, 34], [-53, -73], [-200, -103], [-40, -161], [-188, -100], [-28, -211], [-102, -79], [49, -43], [23, -112]], [[10131, 7666], [-41, -13], [-37, 0], [-61, 88], [-347, 191], [52, 110], [32, 68], [2, 4], [-21, 21], [-31, 33], [-6, 6], [-148, 36], [-18, 5], [-1, 0], [-165, -29], [-111, 46], [30, 255], [-11, 9]], [[9249, 8496], [2, 21], [14, 7], [30, -8], [137, 85], [-8, 9], [-49, 54], [30, 155], [-38, 28], [30, 69], [71, 8], [59, 76], [64, -26], [82, 51], [95, -88], [59, 54], [-45, 60], [-8, 89], [39, 7], [1, -65], [82, -6], [0, 1], [-2, 70], [72, 45], [-1, 0], [1, 1], [-29, 35], [44, -5], [-28, 31], [-20, -3], [-78, -10], [1, 68], [-46, -2], [56, 58], [-61, 48], [52, 41], [-47, -12], [10, 75], [-64, 161], [35, 9], [0, 43], [79, -28], [-52, 94], [23, -27], [93, 53], [-42, 60], [50, 31], [-111, 14], [54, 79], [-91, -15], [5, 79], [-123, 49], [-11, 63], [-69, -38], [-57, 69], [-99, 3], [-22, 127], [-108, 74], [76, 52], [-4, 77], [-82, 22], [12, 37], [-57, 45], [-3, 64], [-89, 70], [51, 59], [-31, 63], [-139, 75], [-4, 9]], [[8584, 10906], [-23, -71], [-37, -38], [-40, -40], [-13, -13], [-17, -7], [-130, -19], [147, -198], [-28, -56], [18, -66], [-112, -98], [-32, -77], [-105, -16], [-17, -114], [36, -19], [-58, -101], [34, -50], [-5, -78], [-48, 17], [-38, -39], [37, -48], [-81, -44], [7, -49], [-98, -73], [29, -50], [-62, -92], [-166, -165]], [[7782, 9302], [-62, 202], [-43, 49], [-8, -1], [-216, -26], [-254, -97], [-103, 153], [-7, 11], [-152, -61], [-91, 38]], [[17328, 10376], [-17, -84], [26, -72], [-74, -20], [-9, -40], [48, -44], [0, 0], [-1, -1], [-70, -79], [-48, -54], [-2, 2], [-42, 42], [-1, -1], [-40, -58], [-26, 4], [-34, 6], [-39, 6], [-9, 20], [-3, 6], [-5, 12], [37, 8], [-6, 50], [-126, 22], [-85, -194], [-11, -51], [30, -11], [-1, -2], [-56, -85], [-42, -6], [-36, -133], [-25, -10], [-40, -94], [-102, -43]], [[16519, 9472], [-3, -2], [-16, -7], [-75, 8], [-21, 3], [-9, 1], [11, 41], [-130, 53], [24, 42], [-90, 10], [19, 56], [-19, 22], [-16, -46], [-78, 73], [-110, 2], [-53, -40], [-24, 70], [-23, -15], [-42, 50], [-33, -62], [0, 50], [-112, 81], [-106, -27]], [[15613, 9835], [0, 0], [0, 1], [-74, 45], [-15, -43], [-52, 0], [-24, 55], [32, 23], [-65, 31], [-9, 4], [-91, -54], [-3, -44], [-118, 105], [-39, -44], [-48, 58], [-67, 11], [-139, -33], [2, -72], [-124, 29], [-12, 14], [8, 38], [6, 11], [23, 1], [30, 1], [1, 0], [2, 26], [8, 135], [1, 9], [-6, 0], [-163, -22], [0, 4], [-1, 89], [88, -22], [-42, 86], [-69, 52], [-86, -41], [-47, 84], [-108, 55], [-3, 1], [0, -2], [-11, -43], [-6, -25], [-6, -25]], [[9249, 8496], [-57, 47], [-8, 6], [-1, -4], [-3, 1], [-78, -267]], [[9102, 8279], [-163, 98], [-94, -57], [-177, 35], [-118, 95], [60, 125], [-4, 36], [-1, 12], [0, 5], [-155, 8], [-210, 92], [12, 97], [-34, 48], [-127, 7], [-160, 70], [-76, 95], [-10, 71], [125, 95], [12, 64], [-42, 19], [-5, -3], [-94, -69], [-1, 0], [-2, 1], [-55, 75], [-1, 4]], [[22408, 10000], [12, -8], [2, -2], [-5, -4], [-149, -97], [-103, 44], [-6, -95], [-11, 0], [-66, 3], [-34, 61], [-104, -23], [23, -61], [-23, -208], [269, -55], [20, 53], [63, 4], [53, -133], [96, -41], [37, -50], [80, -13], [68, 4], [116, -35], [63, -19], [-13, -138], [43, -77], [-37, -77], [7, -102], [-49, -30], [15, -59], [-49, 62], [-94, 3], [-14, -69], [-85, -24], [-15, -56], [-205, 71], [-32, -19], [-1, -2], [26, -24], [34, -31], [0, -1], [-2, -23], [-6, -44], [0, -4], [-58, -31], [-5, -30], [1, -11], [79, -69], [-20, -67], [71, -46], [2, -1], [0, -10], [18, -100]], [[22420, 8316], [-10, -33], [-1, -5], [-120, 90], [-122, 4], [-45, 62], [-184, -35], [-19, 19], [-87, 105], [-27, 95], [-19, 11]], [[21786, 8629], [8, -1], [-69, 150], [-17, 32], [-184, 0], [-112, 125], [-3, -1], [-361, -176]], [[16519, 9472], [2, -6], [61, -47], [25, -11], [54, 41], [46, -51], [68, 58], [55, -48], [-10, -51], [36, 26], [165, -24], [3, -11], [11, -44], [59, -32], [2, -114], [52, -24], [-61, -70], [21, -40], [47, -3], [53, 69], [38, -61], [15, -24], [53, -24], [14, -4], [10, 5], [6, 26], [17, -14], [88, 43], [92, -50], [33, 31], [91, -76], [3, -41], [85, 28], [91, -83], [20, 39], [30, -39], [44, 73], [1, 0], [6, -36], [43, 18], [1, -4]], [[17989, 8897], [7, -26], [-30, -75], [56, -75], [-19, -170], [30, -25], [-44, -19]], [[17989, 8507], [-61, 53], [-13, -3], [-58, -14], [36, 49], [-83, -12], [-74, 106], [-3, 5], [-1, -3], [-5, -70], [-57, 19], [-20, -139], [-43, -12], [34, -16], [-11, -39], [-117, 57], [-94, -48], [-48, 43], [-30, -58], [-108, 11], [-27, -128], [132, -106], [-43, -24], [-17, -69], [149, -10], [43, -45], [-49, -50], [58, -47], [-47, -28], [2, -26], [-43, -51], [-7, -4], [-46, 93], [-137, 25]], [[17201, 7966], [1, 1], [-117, 18], [-18, 58], [-3, 9], [-16, 1], [-28, 2], [-10, 1], [-50, -95], [-43, -6], [-21, 16], [24, 37], [-98, 48], [10, 32], [-139, 32], [-20, -28], [-86, 61], [-127, -136], [-15, -8], [-47, 83], [-97, 65], [-93, -3], [-68, 63], [-16, 14], [4, -20], [-136, -67], [-15, 7], [-15, 6]], [[15962, 8157], [1, 0], [7, 13], [62, 113], [-81, 59], [14, 158], [-86, 33], [-8, 60], [-100, 38], [-76, 133], [-51, -29], [-27, 43], [-52, -1], [-57, 60], [-7, 75], [-71, 27], [-31, 63], [-69, 0], [-44, 137], [-96, 42], [39, 30], [64, -21], [66, 52], [67, 149], [151, -29], [62, 146], [-53, 67], [45, 9], [11, 52], [-81, 12], [4, -51], [-62, 32], [13, 57], [15, 2], [21, -19], [68, 35], [3, 30], [-66, 46], [-6, 7], [62, 48]], [[19448, 9380], [-60, -102], [-1, -10], [-14, -268], [-80, -14], [-29, 42], [-35, -42], [-1, 0], [-73, 60], [-14, 46], [6, 30], [-27, 41], [-19, 29], [-7, 11], [-9, -3], [-103, 56], [-48, -21], [-23, 45], [27, 92], [-66, 25], [-53, -29], [-4, 104], [-26, 5], [-43, -2], [8, -66], [-111, -39], [-38, -96], [84, -46], [-45, -72], [-70, -23], [37, -29], [27, -95], [-30, -45], [48, -105], [-76, -103], [20, -42], [-40, -9], [53, -67], [22, 0], [18, -16], [-7, -35], [-12, -14], [-21, 10], [-21, -56], [-144, 113], [-136, 23], [-64, 70], [-33, 45], [9, 144], [-10, -11], [-56, -65], [-135, 94], [-15, -38], [-19, -5]], [[13698, 9971], [-2, -3], [53, -65], [0, -94], [54, -79], [-24, -50], [28, -45], [-49, -33], [98, -52], [17, -47], [-53, -102], [38, -178], [-77, -29], [-10, -3], [55, -26], [72, -46], [-50, -88], [-102, -48], [-8, -4], [2, -12], [11, -71], [-41, -42], [16, -63], [72, -20], [-48, 7], [39, -132], [-53, -53], [52, -61], [-1, -49], [-26, -91], [-63, -31], [34, -71], [-41, -39], [-11, -130], [-101, 5], [-2, -114], [183, -120], [73, 16], [13, -50], [160, -73], [18, -88]], [[14024, 7697], [-9, 20], [18, -70], [-74, -10], [-49, -83], [-68, -37], [-59, 40], [-31, -33], [39, -34], [-16, -45], [-117, 12], [-24, -47], [12, -23], [6, -11], [15, -29], [55, -17], [-45, -94]], [[13677, 7236], [-152, 139], [-231, 67], [-10, 276], [-162, 87], [-151, 23], [-179, 115], [-158, 278], [-41, 15], [-237, -106], [-167, 18], [-151, -136], [-62, 42], [-49, 162], [-79, 23], [-58, 86], [-64, 13], [-33, -116], [-167, 99], [12, 82]], [[9102, 8279], [14, -8], [21, -13], [55, -89], [-8, -102], [-82, -5], [-30, -71], [40, -10], [72, 72], [40, -24], [-35, -87], [-38, -4], [-1, -93], [0, -5], [-26, -36], [-48, 28], [-33, -51], [-35, 27], [-35, -58], [-50, 16], [38, 70], [-65, 1], [-19, -43], [-4, -13], [68, -76], [14, -202], [-10, 1], [-89, 7], [-80, 112], [-4, -143], [-91, -47], [-86, 64], [-33, 105], [-127, -19], [-73, 97], [-88, -95], [-86, 98], [-57, 6], [-36, -33], [24, -98], [-130, -49], [-110, 39], [-46, -22], [6, -84], [116, -6], [38, -41], [-66, -119], [-41, -1], [-41, 79], [-90, 57], [-124, -128], [2, -127], [-59, -5], [-60, 72], [-65, 2], [-16, -70]], [[7433, 7155], [-31, 2], [-112, 4], [-94, -90], [-105, 69], [-3, 42], [39, 17], [-32, 73], [-95, -116], [-67, 64], [-101, -66], [-151, 87], [-94, -10], [-63, 56], [37, 147], [-67, -32], [-31, 27], [36, 107], [-7, 0], [-89, -4], [-7, -105], [-10, -7], [-109, 30], [16, 42], [-33, 24], [-5, 68], [68, -44], [9, 56], [77, -10], [10, 54], [-339, 103], [-26, -41], [-102, 63]], [[5952, 7765], [-1, 9], [-21, 15], [9, 70], [2, 8], [0, 2], [2, 0], [72, 60], [-40, 37], [29, 33], [-17, 47], [205, 206], [-1, 2], [-45, 37], [-69, 58], [-88, 73], [58, 80], [122, -7], [-1, 79], [-69, 56], [34, 47], [106, -21], [-2, 107], [-94, 30], [-16, 41], [89, 32], [50, -83], [92, 2], [20, 74], [-78, 54], [33, -5], [68, 81], [44, -39], [37, 137], [49, -29], [24, 47], [-30, 33], [63, 16], [8, 51], [-171, 73], [2, 3], [13, 21], [22, 36], [-22, 83], [0, 1], [0, 0], [20, 60], [99, -5], [30, 107]], [[20525, 8969], [-51, -194], [-135, -206], [42, -127], [-34, -40], [24, -47], [-192, -156], [40, -44], [-168, -29], [-3, -50], [40, -17], [-114, -318], [146, 26], [109, -66], [-1, -26], [-1, -7], [-1, -11], [-6, 2], [-43, 10], [-12, -43], [-51, 35], [41, -38], [-6, -18], [-2, -9], [-6, -21], [-89, -40], [16, -107], [-30, -85], [39, 17], [75, -76], [12, -12], [-21, -5]], [[20143, 7267], [-81, -17], [-9, -69], [-50, -39], [-5, -112], [-64, 3], [-27, 75], [-181, -65], [-1, 37], [-79, 36], [-15, -46], [90, -14], [-10, -116], [89, -85], [-9, -1], [-144, -23], [-22, -95], [-124, -73], [0, -79], [-90, 69], [-175, -8]], [[19236, 6645], [-14, 27], [54, 84], [-72, 5], [-50, 30], [10, 38], [-36, -15], [-76, 65], [-34, -115], [-53, -15], [-187, 195], [17, 93], [-96, 65], [-238, -19], [-10, 47], [-52, 23], [8, 65], [-74, 78], [-100, 38], [59, 221], [-151, 356], [17, 126], [-90, -3], [60, 41], [-75, 17], [45, 39], [-50, 62], [31, 103], [-74, 15], [-2, 13], [-9, 53], [-41, 25], [-4, 47], [-1, 11], [-1, 15], [28, 17], [14, 15], [0, 0]], [[21786, 8629], [-48, 28], [-114, -4], [-5, -41], [-91, -63], [3, -3], [82, -105], [-33, -98], [-108, -23], [9, -34], [-36, 16], [13, -31], [-47, 8], [-9, -29], [-101, 43], [-7, -6], [-8, -7], [-71, -63], [20, -53], [-33, -60], [-1, -121], [79, -8], [83, -8], [113, -46], [43, 22], [10, -52], [-43, -45], [72, 10], [-22, -163], [63, -14], [20, -51], [-30, -50], [-102, -8], [-90, -62], [-52, -101]], [[21345, 7407], [-2, 0], [6, 35], [5, 11], [-4, -1], [-72, -18], [-178, 135], [-3, -6], [-45, -108], [-112, 56], [-149, -70], [-169, -6], [-50, -91], [-65, 42], [-51, -40], [-68, 30], [-78, -30], [1, -63], [-50, -6], [-43, -91], [-29, 51], [-42, 4], [-12, 10], [8, 16]], [[13677, 7236], [-96, -152], [-149, -63], [-49, 31], [-66, -173], [-90, 20], [-15, -65], [33, -70], [108, -25], [-72, -25], [-26, -273], [100, -44], [71, -141], [-40, -122], [70, -39], [-32, -55], [-80, -20], [-59, -133], [15, -40], [-70, 40], [-20, -3]], [[13210, 5884], [-18, -2], [12, 29], [9, 21], [-8, 63], [-11, 45], [0, 2], [-495, 157], [-10, 3], [-30, 38], [-8, 9]], [[12651, 6249], [3, 2], [-74, 87], [104, 80], [21, 111], [-10, 10], [-37, 41], [-213, -97], [-38, 82], [-6, 14], [11, 175], [0, 14], [-17, 10], [-16, 10], [-12, 7], [-232, -53], [-66, 174], [-2, 5], [-2, -2], [-193, -138], [-174, -28], [-231, 94], [-252, 42], [-59, 60], [71, 86], [-4, 86], [-170, -24], [-180, 177], [-2, 5], [21, 33], [14, 18], [87, 7], [110, 7], [1, 1], [14, 21], [37, 55], [0, 2], [-3, 14], [-5, 26], [-9, 3], [-427, 143], [-32, 11], [-2, -1], [-36, -9], [-158, -41], [2, -7], [0, 0]], [[10485, 7562], [-118, -35], [-5, 6], [-127, 159], [-4, -1], [-97, -34], [-1, 0], [-2, 9]], [[22420, 8316], [154, -28], [221, -254], [-8, -138], [19, 33], [66, -32], [34, 45], [289, -204], [184, 50], [186, -104], [51, -148], [144, -35], [55, 25], [69, -64], [74, 18], [32, -64], [-67, 4], [24, -106], [58, -12], [7, 46], [238, -182], [-4, -78], [-32, 18], [-34, -52], [85, -70], [17, -57], [-112, 16], [0, 0], [-127, 18], [-10, -214], [-70, 40], [14, 136], [-35, -165], [-96, -29], [-19, 59], [-73, -14], [-55, 101], [-77, -11], [-9, -67], [-65, 18], [-21, 115], [-51, 13], [22, 46], [-3, 1], [-38, -3], [-14, -6], [4, -29], [-124, 32], [-41, -272], [-13, -11], [-50, 24], [-55, 11], [35, -39], [6, -6], [-13, -4], [-39, -10], [-24, -6], [-173, 30], [-48, 8], [-6, 1], [-22, 121], [-142, 101], [-14, -37], [-66, -6], [38, 16], [-6, 36], [-163, -68], [-46, -68], [21, -219], [-206, -81], [-280, -214]], [[22016, 6291], [0, 2], [1, 113], [36, -23], [2, -1], [-1, 7], [-9, 56], [-10, 63], [-50, 71], [51, 29], [34, 122], [48, 36], [-8, 0], [-26, 2], [3, 64], [-52, 22], [16, 57], [58, 7], [-7, 10], [-29, 44], [-1, 2], [0, 3], [17, 30], [-13, 11], [-65, 51], [-8, 7], [-1, -2], [-44, -67], [12, 44], [-107, 12], [-91, 126], [-8, -45], [-60, 29], [-59, -60], [17, 33], [-66, 96], [-42, -14], [-115, 95], [-12, 77], [-1, 8], [-28, -46], [-9, -12], [-14, 59], [-30, -2]], [[19236, 6645], [-5, -95], [39, -42], [-33, -76], [63, -17], [13, -64], [-45, -39], [-19, -44], [5, -12]], [[19254, 6256], [-13, 24], [-24, -14], [-38, -33], [-7, 10], [-70, 103], [-12, 17], [-62, -77], [27, -27], [-104, 29], [-71, -115], [-1, -1], [-43, 25], [-29, 131], [-188, -2], [-24, 49], [-38, -63], [-61, 19], [4, -50], [-33, -20], [-110, 14], [-45, -89], [-125, 64], [-22, -8], [-55, -46], [-2, -3], [31, -106], [-49, -72], [91, -63], [-103, -33], [-9, -38], [54, -59], [-117, -39], [30, -102], [-1, 0]], [[18035, 5681], [-65, 21], [-11, 3], [-1, 0], [-158, -24], [-1, -1], [6, 36], [58, 4], [-123, 33], [5, 9], [31, 67], [5, 10], [-5, 18], [-17, 32], [-17, 3], [-54, -5], [-7, -103], [-19, -13], [-1, -12], [-4, 9], [-36, 103], [-17, 47], [-47, 5], [42, -51], [-27, -32], [-104, 19], [-42, 121], [-108, -15], [0, 1], [-14, 30], [46, 33], [-98, 31], [-20, 82], [-49, -5], [-11, -2], [-39, -76], [-50, 90], [-130, -39], [-67, 103], [-20, -12]], [[16866, 6201], [-10, 29], [-2, 7], [-12, -1], [-102, -4], [-1, 2], [-84, 86], [-43, -16], [-15, 71], [0, 2], [-71, 33], [-61, -60], [-10, 41], [-52, -26], [-46, 78], [-54, 10], [-103, 164], [74, 22], [-23, 15], [42, 67]], [[16293, 6721], [38, 28], [13, 55], [7, 33], [1, 0], [58, 15], [29, 128], [-29, 68], [33, 24], [-91, 88], [46, 42], [-18, 93], [95, -22], [32, 73], [97, -28], [62, 52], [39, -14], [52, 96], [0, 88], [118, 25], [42, 60], [30, -37], [49, 43], [75, -27], [-27, 39], [19, 50], [-87, 18], [-9, 10], [-24, 27], [-30, 32], [8, 4], [40, 15], [31, 77], [113, -33], [68, 43], [5, -10], [9, 4], [0, 18], [0, 44], [0, 25], [0, 1], [14, -2]], [[7433, 7155], [13, 0], [-22, -36], [-10, -17], [-5, -7], [-91, -12], [43, -27], [4, -67], [74, -24], [-2, -2], [-91, -84], [-6, -53], [60, -66], [-27, -74], [18, -13], [61, -35], [-11, 1], [-108, 8], [-36, -34], [63, -83], [-19, -70], [54, -99], [108, 0], [-6, -4], [-37, -25], [-26, -18], [42, -92], [-2, -3], [2, -3], [-55, -68], [0, 0]], [[7421, 6148], [-25, -31], [-77, -96], [0, 0], [15, -93], [-110, -99], [24, -117], [0, -1], [-55, -3], [-78, -90], [-5, -14], [-55, -185]], [[7055, 5419], [-4, 1], [-52, -189]], [[6999, 5231], [54, -113]], [[7053, 5118], [14, -187], [3, -3], [24, -30]], [[7094, 4898], [40, -14]], [[7134, 4884], [-1, -8], [53, -97], [6, -11], [-51, -75], [-66, 7], [-11, -51], [-74, 29], [29, 49], [-27, 55], [-50, -147], [-57, 30], [-40, -39], [-34, 107], [-39, 24], [-52, -24], [32, -93], [-177, -21], [-24, 23], [28, 44], [-52, 47], [32, 80], [-97, 51], [-17, 60], [-128, -212], [-64, 15], [29, 123], [-29, 51], [69, 85], [-12, -52], [35, -12], [13, 117], [-97, 45], [21, 33], [-57, -6], [0, 2], [-1, 0], [2, 85], [-15, 18], [-75, 14], [-59, -123], [80, -24], [16, -144], [-143, 75], [-64, -9], [-36, -21], [-69, -103], [-20, -34], [-7, -5], [-65, -43], [-32, 110], [65, 102], [-64, 59], [-4, 3], [5, 12], [31, 73], [0, 1], [1, 0], [60, -41], [56, 114], [90, -53], [31, 7], [2, 80], [79, -14], [-72, 87], [10, 159], [-105, -78], [14, 44], [-32, 26], [-34, -38], [-96, 41], [-2, -70], [-44, 3], [-16, -42], [38, -21], [17, -86], [-43, -20], [-54, -192], [-129, 155], [29, 106], [32, 14], [-37, 15], [-59, -65], [-35, 25], [33, 67], [68, -26], [75, 111], [12, -36], [-2, 107], [111, 113], [108, -6], [13, 44], [114, 15], [14, -24], [-62, -71], [-72, 25], [-63, -27], [16, -47], [120, -54], [66, 91], [52, -20], [45, 30], [-62, 59], [20, 112], [-29, 54], [-52, 23], [-41, -69], [-95, 76], [88, 71], [23, 72], [-53, 34], [50, 8], [-72, 86], [1, 1], [105, 83], [-41, 68], [-6, 0], [-48, -74], [-39, 71], [-92, -4], [-27, -49], [39, -32], [-6, -62], [-159, -37], [100, -150], [-71, -141], [-75, 41], [110, 33], [-64, 27], [19, 25], [-66, 23], [-36, -53], [29, 68], [-86, 44], [-76, -53], [-65, 56], [-9, 56], [-22, -17], [-19, -139], [65, -40], [32, -74], [131, 38], [59, -55], [-235, -47], [0, -68], [63, 31], [-35, -54], [54, -35], [-26, -26], [29, -60], [-155, -36], [-55, 30], [17, 81], [-176, 40], [18, 118], [28, -58], [119, 105], [-72, 66], [-22, -46], [-77, 28], [-55, -28], [12, -70], [-31, -52], [-79, 57], [-62, -112], [-80, 18], [-15, -46], [51, 12], [31, -34], [-8, -96], [-73, -22], [-31, 56], [-74, -10], [76, -125], [-65, -70], [-73, -21], [-39, 92], [-81, -129], [-61, 58], [-3, -2], [-57, -59], [-1, -1], [50, -83], [170, 102], [29, -48], [62, 27], [9, -59], [140, -55], [-72, -165], [11, 13]], [[4735, 5004], [-70, -106], [-278, -223], [-13, -10], [-1, 2]], [[4373, 4667], [-62, 118], [66, -4], [34, 44], [-112, 18], [-22, 53], [-69, 16], [-13, 50], [4, 2], [-2, 15], [-3, 2], [64, 23], [-164, 111], [41, 66], [-72, 65], [14, 55], [-138, 42], [103, 93], [-52, 91], [-86, 28], [6, 21], [14, 48], [10, 7], [22, -7], [76, 78], [2, 62], [-4, -1], [-1, 0], [-1, 33], [-1, 23], [4, 7], [8, -5], [40, 75], [27, -21], [33, -25], [68, 18], [69, 121], [48, -11], [-47, 105], [39, 37], [-28, 73], [45, 76], [49, 81], [7, -5], [-16, -39], [58, -60], [7, 52], [86, 72], [303, -82], [98, 101], [3, -2], [38, -16], [-21, -43], [109, -28], [66, 95], [124, 74], [80, -31], [220, 88], [20, 208], [-68, 77], [14, 31], [-118, 55], [-33, 116], [96, 27], [82, 159], [66, -20], [-3, 71], [-44, -28], [-28, 24], [41, 59], [82, -22], [22, -45], [6, 169], [39, 56], [18, 2], [24, 4], [36, 4], [20, 78], [-31, 65], [-3, 5], [136, 123], [37, -15], [-4, 37], [1, -1]], [[12651, 6249], [-47, -39], [63, -113], [-95, -75], [-1, -37], [-52, -5], [0, -44], [0, -1], [-121, 23], [-48, -108], [-75, 0], [2, -37], [-67, -53], [5, -55], [-42, -7], [-22, -90], [-89, -90], [-213, -75], [-46, -41], [-11, -81], [-78, -70], [-44, 44], [-73, -79], [-12, -182], [20, -46], [33, 18], [21, -33], [12, -157], [-69, -24], [-27, -87], [1, -4], [31, -108], [66, -41], [-64, -36], [1, -1], [62, -61]], [[11672, 4454], [-6, -16], [-109, -46], [-17, 45], [34, 61], [-38, 21], [-36, -56], [-9, 59], [-56, 32], [-4, 2], [-13, -11], [-35, -18], [-58, 50], [85, 60], [6, 4], [8, 5], [-9, 21], [-16, 33], [-5, 9], [-7, -3], [-49, -18], [-13, 71], [-3, 13], [-3, -9], [-34, -77], [39, -35], [-31, -15], [-16, -89], [18, -40], [28, 28], [21, -79], [-144, 79], [-11, -57], [-9, -7], [-49, -18], [-69, 35], [-12, 7], [-2, 7], [-21, 64], [-2, 6], [-45, -1], [-50, 0], [-16, -1], [87, -46], [-33, -41], [41, -53], [-28, -64], [147, -34], [11, -112], [-162, 14], [-71, -112], [-67, 20], [21, 76], [-2, 0], [-38, -12], [-37, -11], [51, 37], [-70, 67], [69, 69], [64, -43], [89, 25], [-86, 2], [-28, 101], [-5, -1], [-44, -9], [-7, 59], [-1, 2], [-51, -48], [-55, 28], [-23, -67], [-26, 54], [-41, -70], [-46, 19], [-10, -50], [62, -26], [-38, -32], [59, -56], [-29, -29], [-78, 72], [-18, -45], [-51, 7], [-34, -79], [-101, 87], [-48, 14], [-69, 21], [78, 48], [1, 0], [0, 1], [0, 0], [8, 77], [2, 2], [1, 0], [65, -56], [65, 14], [16, 41], [60, -12], [-31, 34], [9, 81], [71, 49], [8, 63], [-58, 36], [-10, -9], [9, 32], [7, 25], [3, 13], [226, 112], [10, 5], [-2, 6], [-72, 213], [-14, 41], [-1, -1], [-64, -23], [-103, 149], [-131, 35], [38, 170], [-41, 85], [34, 42], [-1, 3], [-1, 5], [-14, 77], [-47, 57], [-11, 12], [-29, 36], [-78, 26], [0, 0], [-34, -86], [-56, 26], [-50, -42], [-59, 65], [-24, -135], [-83, -4], [-6, -48], [-116, -111], [-88, 59], [1, 3]], [[9706, 5520], [70, 114], [49, 0], [-35, 21], [11, 49], [1, 4], [55, 2], [1, 4], [-20, 18], [-58, 7], [-30, -7], [5, 10], [8, 20], [66, 160], [163, -57], [32, 157], [39, -59], [36, 13], [-48, 84], [38, 3], [-7, 60], [-21, 8]], [[10061, 6131], [15, -4], [17, 49], [-53, 10], [16, 85], [8, 5], [35, 21], [33, 19], [59, 103], [79, -29], [46, 92], [-51, 62], [5, 2], [-2, 4], [107, 64], [18, 24], [-6, 38], [-33, 75], [6, 74], [52, 44], [87, -32], [-99, 176], [-284, 179], [143, 81], [30, -21], [39, 33], [-34, 59], [32, 24], [103, -9], [73, 102], [-17, 101]], [[22016, 6291], [-87, -120], [-1, -1], [-86, -26], [5, -70], [-68, -115], [-77, -41], [-53, 14], [-11, -49], [94, -4], [13, -44], [1, -3], [-77, -9], [-64, -11], [38, -29], [-2, -4], [-2, -4], [-17, -38], [-23, -8], [-31, -35], [-37, 4], [-32, -39], [-2, -6], [-3, 1], [-17, 8], [-25, 13], [2, 2], [34, 30], [-68, -2], [-14, 37], [-99, -91], [-28, 33], [-15, -46], [-44, -13], [3, -7], [32, -28], [-13, -4], [-7, -3], [-35, -13], [-19, 15], [-18, 0], [6, -21], [-35, 21], [-23, -107], [-1, 0], [0, -1], [-48, 15], [-17, -55], [-104, -3], [-73, -77], [-3, -4], [-2, 32], [-103, 4], [-9, -22]], [[20751, 5367], [-23, 49], [0, 1], [0, 0], [20, 85], [25, 11], [-69, 115], [27, 47], [-296, 232], [-111, 145], [-69, 0], [-152, -111], [-294, 225], [-138, 40], [-7, 2], [-84, -33]], [[19580, 6175], [-56, -22], [-5, -3], [34, -50], [-106, 3], [-95, 71], [-12, -45], [58, -51], [-25, -23], [-46, 43], [-34, -35], [0, 0], [-64, 121], [40, 34], [-15, 38]], [[13677, 7236], [61, -114], [5, -2], [99, -24], [6, -2], [33, -45], [-18, -99], [-89, -83], [17, -63], [5, -3], [152, -77], [149, -200], [180, 3], [49, -110], [80, -69], [1, -1], [146, 95]], [[14553, 6442], [87, -48], [13, -33], [-2, -184], [3, 0], [185, -53], [-31, -35], [-63, 10], [-107, -124], [35, 36], [37, -46], [-45, -110], [29, -12], [-28, -71], [58, -10], [-17, -128], [0, 0], [52, -32], [63, 79], [61, -16], [-49, -120], [-223, -25], [-253, -219]], [[14358, 5301], [-30, -18], [-68, -42], [-179, -58], [-30, -7], [-224, 65], [51, 145], [4, 10], [-24, 23], [-51, 49], [-8, 8], [-147, 37], [-209, -85], [-74, 121], [-187, 57], [-82, 58], [-24, 24], [5, 122], [6, 5], [123, 69]], [[16866, 6201], [30, -90], [-14, -20], [-7, -10], [-10, -14], [-7, 14], [-32, 72], [-59, 5], [42, -70], [6, -12], [4, -6], [-97, -22], [-2, -10], [3, -18], [71, -6], [0, -81], [91, -46], [55, -100], [-12, -28], [-8, -17], [-19, -43], [-88, -40], [1, -62], [1, -65], [1, -84], [0, -5], [-2, -1], [-43, -21], [-3, -82], [-122, -37], [-32, -75], [-94, 20], [-77, -203], [23, -101], [13, -34], [19, -6], [144, -8], [26, -2]], [[16668, 4893], [10, -87], [22, -15], [78, -13], [-1, -167], [73, -77], [0, 0], [1, -1], [2, -55], [-22, -19], [-74, -65], [0, 0], [31, -39], [-12, -103], [-162, -221], [-39, 33], [-43, -114], [-55, 26], [-22, -72], [-48, -12], [-64, -99], [-1, -3], [-50, 32], [-91, -115], [-46, 38], [10, -49], [-64, -48], [77, 17], [10, -33], [-125, -49], [45, -53]], [[16108, 3530], [-248, 106], [-146, -26], [-20, -44], [-73, 70], [-29, 93], [-99, 78], [54, 136], [-45, 168], [-8, 74], [12, 49], [-17, 7], [-3, 1], [-16, 6], [11, -20], [-19, -51], [-5, -6], [-182, 104], [-136, -47], [-197, 164], [-59, -46], [-76, 10], [13, 37], [-85, 47], [-2, 51], [-82, -68], [-95, -2], [18, 18], [-55, 49], [131, 92], [-53, 79], [-52, -90], [-34, 132], [83, 36], [9, 74], [-34, 31], [-87, -13], [22, 61], [0, 0], [-4, 2], [-56, 29], [-14, 7], [-66, -27], [-38, 14]], [[14326, 4915], [-74, 28], [17, 145], [1, 2], [0, 0], [127, 12], [-39, 199]], [[14553, 6442], [-21, 53], [46, 54], [2, 54], [-1, 8], [-57, 15], [-13, 46], [-1, 4], [6, 1], [128, 21], [5, -91], [-39, -11], [98, -115], [82, 99], [133, -48], [18, 61], [-39, 74], [-84, 20], [88, 43], [105, -20], [2, 81], [-41, 29], [47, 53], [0, 4], [-6, 74], [0, 1], [9, 4], [124, 49], [11, 4], [88, -31], [-16, -58], [74, 20], [137, -73], [93, 14], [46, 142], [0, 1], [58, -52], [111, 7], [10, 25], [23, -23], [94, 5], [0, -1], [2, 0], [76, -133], [-104, -48], [160, -47], [3, -102], [103, 20], [62, -32], [36, 23], [3, 89], [92, -13], [-13, -21]], [[19580, 6175], [-22, -127], [-2, -5], [-2, -4], [-118, -137], [-10, -11], [3, -5], [13, -16], [28, -38], [158, -68], [-90, -281], [-56, -66], [-6, -5], [-266, 31], [-121, -57], [-14, -27], [-23, -44], [79, -22], [22, -78], [55, -49], [18, -30], [-34, -5], [-72, -11]], [[19120, 5120], [-1, -3], [-66, -124], [-13, -16], [-206, 9], [-111, -45], [-21, 73], [-90, -54], [-4, -3], [-4, -2], [-29, -114], [-67, -2], [-102, 77], [-16, -71], [-31, 30], [29, 41], [-62, 53], [104, 5], [-37, 55], [41, 34], [-16, 47], [17, 38], [11, 11], [-7, -2], [-105, -16], [-5, -1], [1, 0], [-1, 0], [24, -74], [-7, -1], [-29, 12], [3, 65], [-17, 17], [0, 30], [16, 10], [-15, 18], [-2, 2], [-1, 1], [-48, -14], [-41, -105], [-40, 55], [-69, -10], [-5, 41], [-100, -14], [-33, -41], [-15, 25], [2, 8]], [[17952, 5165], [17, 91], [55, 4], [46, 157], [-53, 31], [-15, 90], [60, 39], [16, -25], [10, 92], [-49, 35], [-4, 2], [0, 0]], [[14326, 4915], [-1, -30], [-75, 10], [-6, 1], [0, -12], [0, -17], [0, -8], [70, -49], [-43, -46], [1, -2], [1, -5], [4, -9], [-82, 18], [-9, -73], [-54, 11], [34, -84], [89, -32], [-57, -17], [3, -69], [-9, -6], [-28, -12], [-80, 63], [-169, 51], [113, 13], [-29, 86], [-1, 1], [-103, 3], [26, -39], [-47, -6], [-22, 44], [-70, 14], [14, 26], [-48, 70], [-83, 32], [-49, -46], [38, -157], [-32, -7], [-25, 190], [-116, 8], [13, -52], [-49, -46], [-4, -83], [-42, -57], [48, -66], [-24, -32], [30, -31], [-16, -54], [-120, -192], [-57, -34], [38, -57], [-43, -48], [36, -121], [-11, -15], [-65, -9], [-3, 1], [43, 17], [-126, 93], [-25, -156], [-48, -12], [-19, -85], [-64, 58], [-4, 17], [-79, 75], [-17, 47], [4, 22], [-12, 2], [-167, 22], [-2, 0], [0, 0], [-6, -11], [-20, -35], [-31, 41], [42, -67], [-19, -21], [-59, 35], [-58, -51], [-40, 40], [-54, -61], [-36, 180], [-1, 1], [-150, -66], [24, -50], [-45, 38], [-103, -62], [-74, -1], [-6, -33], [-76, 11], [26, 39], [-90, 119], [102, 11], [91, 78], [9, 57], [-75, 108], [149, 121], [-34, 37], [39, 89], [-36, 62], [36, 84], [49, -16], [58, 94], [-91, 104], [-49, -47], [-81, 46], [-2, -2], [-1, -2], [-16, -19], [15, -31], [0, -88], [-21, -56], [21, -80], [-42, -46], [-10, 80], [-25, 5], [-23, 5], [-9, 36], [-3, 12], [-70, -28], [-27, 48], [-116, -70], [-37, 86], [-82, -66], [-2, -2], [3, -12], [6, -22], [2, -6], [53, 3], [4, -101], [88, -2], [14, -33], [84, -23], [14, -53], [-164, -31], [-1, 1], [-62, 58], [-6, -15]], [[20751, 5367], [51, -5], [-52, -35], [-8, -6], [-35, 19], [11, -36], [-69, -44], [-10, 24], [-35, -29], [-4, 36], [-75, -137], [-54, 31], [-36, -12], [-9, -51], [-26, 35], [-2, -1], [-1, 0], [-37, -12], [-3, -12], [-41, -141], [-14, 27], [-14, -45], [-23, 30], [-13, -39], [-80, -8], [-23, -53], [-1, -2], [1, -4], [28, -108], [28, -111], [-13, -52], [-43, 8], [-45, -194], [24, -39], [-62, -51], [-13, -75], [98, -31], [-24, -170], [59, -36], [8, -149], [-30, -31], [48, -94], [-39, -36], [120, -135], [6, -20], [-7, -124], [-3, -50], [5, -3]], [[20294, 3396], [-23, -4], [-165, -28], [-51, 60], [2, -59], [36, -6], [-96, -129], [1, -65], [-44, 4], [-23, 60], [-45, -36], [-34, 34], [-6, -33], [-95, 28], [-128, -59], [-11, 3], [-13, 4], [-32, 11], [-35, 17], [-65, 109], [-167, 89], [38, 135], [83, 15], [16, 52], [-47, -30], [-71, 49], [-53, 154], [11, 85], [0, 0], [-1, 4], [-60, 192]], [[19216, 4052], [4, 1], [-12, 46], [54, 23], [-2, 205], [-38, 117], [5, 5], [79, 15], [143, -28], [0, 133], [-23, 3], [43, 43], [-18, 83], [125, 26], [-6, 110], [36, 7], [-1, 36], [-71, 41], [-109, -9], [-9, 59], [-159, 36], [-9, 65], [-128, 51]], [[17952, 5165], [-20, 0], [-12, 11], [-48, -10], [-19, -131], [-67, 19], [-4, 70], [-100, 18], [-210, -63], [-138, 40], [-28, -86], [-46, -5], [-53, 74], [-155, 76], [-2, 1], [-47, -40], [-34, -137], [-40, -20], [-58, 15], [-89, 212], [-1, 5], [-19, 0], [-82, -1], [-16, 0], [-7, -11], [-108, -160], [-2, -3], [11, -6], [132, -64], [23, -11], [4, -14], [-33, -35], [-16, -16]], [[19216, 4052], [-17, -2], [-5, 11], [-32, -17], [14, -53], [-119, -69], [-8, -79], [-68, -17], [-12, -108], [-89, -47], [-4, -69], [-79, 11], [6, 53], [-30, 6], [-40, -36], [30, -19], [-28, -32], [-334, 73], [-178, -123], [-89, -10], [-70, 38], [-4, -77], [-138, -92], [-107, 19], [29, 113], [-59, -24], [-57, 37], [-53, 35], [-1, 2], [-32, -64], [-81, -32], [22, -245], [-54, -20], [0, -11], [0, -4], [0, -38], [20, -40], [14, -55], [-73, -57], [1, -5]], [[17491, 3005], [-3, -2], [-235, -6], [11, 52], [-69, 52], [-3, -3], [-75, -73], [-4, -3], [39, -110], [-30, -99], [33, -111], [-3, -4], [-152, 8], [-20, 3], [-85, 85], [-61, -53], [-63, 4], [2, -5], [-13, 0], [0, 1], [-5, 9], [18, 158], [7, 54], [0, 3], [0, 3], [-49, -2], [-58, 61], [-3, 3], [-19, -17], [14, -7], [-20, -58], [-38, 37], [-33, -39], [-29, 77], [65, 58], [2, 271], [-17, -96], [-36, 62], [-30, -10], [14, -48], [-43, 35], [-29, -27], [46, -94], [-24, 45], [-115, -5], [81, 94], [-21, 75], [68, -5], [-27, 6], [22, 59], [-103, 5], [63, 88], [-119, -68], [39, -22], [-38, -36], [-91, 53], [-16, -43], [-59, -13], [-37, 85], [-10, 12], [-10, 21], [-12, 5]], [[4735, 5004], [30, 45], [3, -7], [3, -9], [106, -274], [-3, -194], [82, -32], [1, -1], [7, -11], [30, -44], [3, -12], [39, -65], [-8, -39], [-59, -308], [102, -72], [42, -148], [301, -148], [63, -179], [-62, -85], [34, -307], [-119, -194], [26, -201], [102, -43], [62, 56], [101, 3], [28, 70], [53, -21], [27, 84], [49, 5], [119, -249], [106, -104], [-31, -46], [12, -120], [78, -30], [-109, -58], [-34, 12], [32, 34], [-53, 29], [-20, -146], [82, -23], [24, -63], [124, 19], [0, -1], [0, -1], [0, 0], [22, -78], [-4, -11], [-1, 1], [-69, -181], [6, -80], [-199, -185], [23, -109], [-104, -69], [0, -114], [-69, -38], [-51, 16], [-67, -69], [-36, 43], [-27, -102], [-10, -2], [-46, 13], [-25, 49], [-63, 238], [-43, -99], [-46, 73], [-28, -60], [-73, -36], [-110, 49], [-28, 103], [-69, 66], [-42, -17], [-18, 45], [27, 41], [-80, 40], [-56, 146], [-181, 54], [-36, 74], [-54, 9], [-27, -33], [-61, -94], [11, -64], [-110, -74], [-41, -132], [-40, 4], [-11, -33], [-85, -47], [-3, 3], [-83, 104], [-53, 65], [-53, 65], [131, 179], [-13, 41], [-54, 5], [-26, 55], [-236, 179], [-1, 1], [1, 1], [-11, 19], [35, 68], [-94, 46], [1, 87], [-67, 48], [2, 8], [8, 33], [4, 15], [1, 1], [111, 54], [44, 233], [-50, 117], [44, 65], [-97, 189], [-64, 124], [-2, 3], [31, 91], [-122, 246], [-24, 10], [-53, 21], [-6, 85], [3, 24], [123, 70], [42, 77], [228, 178], [45, 50], [101, 114], [17, 19], [-2, 15], [-29, 197], [6, 61], [4, 2], [93, 39], [20, 9], [106, 113], [127, -26], [1, 1]], [[20294, 3396], [6, 2], [100, -52], [97, -137], [100, 19], [-14, -130], [44, 6], [60, -82], [10, -14], [-64, -98], [94, -31], [-16, -57], [74, -18], [1, 0], [-38, -35], [-41, -40], [21, -132], [-16, -49], [-6, -15], [-3, -10], [-81, -32], [20, -51], [-18, -5], [-314, -98], [-43, -182], [24, -53], [34, 5], [1, -117], [94, -15], [74, -135], [-78, -96], [-1, -2], [-69, 6], [-99, -98], [18, -55], [68, -38], [6, -84], [-98, -134], [14, -121], [-93, -272], [-64, -78], [-6, 1], [-7, -8], [-10, 1], [-18, 13], [-127, -210], [-9, -117], [-118, -25], [0, -54], [-61, -39], [-50, -244], [-156, -52], [-58, -84], [-161, -50], [-10, 2], [-69, 12], [-81, 13], [-302, -27], [-66, 78], [-137, 62], [-170, 181], [-62, 9], [-2, 8], [-26, 155], [-170, 31], [0, 3], [-12, 53], [61, 73], [25, 110], [-194, -5], [-22, 83], [-137, 115], [2, 1], [58, 100], [56, -58], [89, -13], [52, 112], [-31, 193], [78, 72], [-10, 94], [55, 171], [-24, 24], [13, 211], [-30, 25], [-112, -35], [-38, 46], [27, 124], [58, 62], [-24, 4], [-8, 154], [44, 89], [-74, 65], [0, 56], [33, 22], [-38, 66], [77, -20], [17, 53], [158, 57], [17, 76], [-120, -4], [45, 68], [-18, 103], [-216, -20], [-27, 40], [11, 105], [-99, 81], [-419, -183], [-2, 1], [-5, 2], [-29, 16], [-17, 82], [-1, 7]], [[15962, 8157], [-58, 26], [-29, -3], [-110, -11], [-34, 55], [-4, 7], [-11, -3], [-25, -6], [-19, -4], [11, -29], [-81, 34], [-21, -88], [-146, 111], [-55, -33], [-27, -96], [-100, -35], [-82, 37], [-10, -56], [-50, -16], [-46, 49], [-3, 97], [-135, 99], [-42, -40], [8, -64], [-94, 38], [-15, 142], [0, 3], [-1, 0], [-72, 9], [-43, 61], [21, 38], [-111, 137], [-61, -139], [-97, 12], [-1, -1], [-101, -105], [-2, -2], [-6, -53], [56, -20], [47, -93], [-1, -2], [-79, -64], [-2, -1], [-29, 24], [-6, -63], [51, -46], [-3, -6], [-9, -21], [-5, -9], [82, 15], [-22, -114], [103, -100], [-74, 22], [-143, -73], [-40, 16], [-28, -49], [-42, 3], [-21, -61], [-33, 25], [-76, -39], [-12, 25]], [[820, 17679], [1, 46], [-84, 72], [23, 133], [30, 19], [-31, 62], [25, 54], [-105, -16], [-23, -3], [-6, -1], [1, 5], [3, 50], [-46, 49], [-50, -5], [-14, 82], [-67, 18], [-24, 33], [-1, 2], [-19, 26], [39, 24], [-85, 29], [-16, 6], [-4, 72], [56, -16], [12, 87]], [[435, 18507], [1, 7], [25, -40], [71, 19], [88, 24], [42, -25], [37, -137], [63, 43], [55, -27], [22, 41], [49, -74], [102, -36], [35, 22], [-37, 79], [36, 38], [-50, 55], [77, 44], [-41, 36], [5, 103], [52, 9], [91, 176], [0, 0]], [[1158, 18864], [37, -7], [-7, -69], [151, -44], [90, -26], [43, 22], [40, -1], [45, -24], [36, 21], [33, -67], [160, 26], [41, -51], [90, 19], [54, -75], [-26, -110], [57, -40], [-5, -87], [-76, -83], [-16, -17], [33, -70]], [[4938, 18412], [41, -20], [13, 10], [16, 12], [120, -159], [-1, -5], [-10, -57], [-42, -5], [-7, -189], [-59, -98], [26, 5], [-10, -28], [68, -70], [71, -7], [9, -76], [106, 57], [16, 61], [70, -36], [54, 18], [-20, -121], [27, -21], [-39, -147], [103, -15], [10, -12], [13, -2], [21, -36], [11, -21], [7, -11], [-36, -38], [-1, -2], [1, -1], [87, -122], [-1, -1], [1, 0], [-22, -38], [108, -3], [53, -85]], [[7358, 4743], [-34, -63], [55, -12], [20, -137], [61, -50], [4, -28], [60, -23], [-35, -75], [-65, -24], [-85, 81], [-2, 2], [-38, -52], [-16, 106], [46, -37], [47, 22], [-24, 101], [23, 35], [-93, 62], [2, 8], [41, 86], [33, -2]], [[7294, 4867], [16, 47], [42, -31], [-21, -31], [-37, 15]], [[7094, 4898], [30, 64], [15, -54], [-5, -24]], [[6999, 5231], [22, -38], [19, -2], [23, -2], [36, 80], [73, -103], [60, 51], [50, -21], [-22, -137], [-35, 2], [61, -58], [-43, -53], [-32, 87], [-82, 45], [-77, 42], [1, -6]], [[7421, 6148], [89, -47], [145, -7], [53, -53], [91, -11], [37, 34], [17, -53], [101, 34], [8, -50], [0, -3], [3, 3], [104, 77], [65, -64], [79, 12], [7, 90], [76, 4], [-30, 150], [138, 0], [-25, 32], [61, 27], [-5, 100], [123, -70], [47, 43], [29, -15], [50, 28], [40, 133], [148, -59], [-12, -58], [95, -61], [67, 8], [57, 67], [116, -30], [35, 79], [4, 63], [-41, 4], [29, 47], [160, -90], [-14, -44], [33, -42], [210, -23], [43, -154], [51, -14], [28, 32], [111, -60], [14, -98], [34, -5], [-21, 9], [26, 30], [73, -42], [26, 49], [65, -19]], [[9706, 5520], [-49, 18], [-52, -70], [-126, 19], [-6, -8], [-42, -69], [12, -89], [-76, -45], [19, -68], [-181, 5], [-99, -156], [-33, 90], [-17, -38], [-78, 39], [-85, -58], [55, -71], [-40, -84], [45, -81], [-12, -84], [49, -29], [-96, -50], [-12, -68], [42, -52], [-3, -1], [-64, 7], [-295, 155], [-101, -41], [-109, 24], [-33, -55], [-162, 75], [-72, -46], [20, 91], [-34, -34], [-86, 106], [-165, -168], [23, -110], [-63, -7], [-45, -59], [-85, 31], [39, 148], [-119, -98], [-45, 66], [-67, -48], [-69, 137], [20, 102], [70, 46], [1, 66], [91, -5], [27, 60], [-52, 21], [96, 164], [103, 9], [-88, 61], [-98, -107], [-90, 5], [-4, 67], [-125, 69], [17, -74], [-30, -43], [-30, 18], [9, 68], [-71, 15], [52, 156], [-169, 29], [-15, -55], [-48, 3]], [[2359, 21650], [-104, -145], [-29, 12], [-126, -110], [-53, 48], [-28, -36], [-66, 23], [-121, -199], [0, 0], [0, 0], [-28, 105], [-119, 99], [-232, 68], [-37, -54], [-82, 15], [-48, 24]], [[1286, 21500], [-11, 5], [4, 80], [-59, 2], [-51, 62], [-36, -14], [-24, -86], [-56, 47], [-81, -47], [-77, 29], [-92, -41], [-127, 106], [-40, 11], [-37, -38], [-158, 114], [-73, -55], [-163, 28]], [[205, 21703], [-4, 1], [13, 29], [-63, 83], [2, 8], [-6, 7], [19, 59], [-61, 64], [2, 14], [1, 6], [4, 24], [79, 18], [69, 100], [16, 23], [17, 153], [76, 86], [-52, 144], [125, 109], [5, 10], [1, 2], [65, 137], [94, 74], [6, 169], [81, 50], [101, -5], [143, 140], [115, 21], [26, 24], [-63, 30], [-2, 1], [68, 71], [12, 100], [2, 19], [89, 0], [69, 74], [172, 261], [25, 97], [145, 108], [-16, 84], [82, 142], [-3, 4], [-72, 38], [-18, 80], [24, 1], [30, 20], [108, -11], [93, -9], [10, -1], [1, -7], [61, -41], [5, -45], [39, 1], [0, 0], [101, -171], [402, -218], [152, -24], [153, -95], [60, 9], [3, 0], [1, 0], [1, 0], [-2, -1], [-4, -4], [-132, -111], [-22, -107], [-166, -259], [-76, -36], [-28, -65], [-86, 35], [-97, -177], [35, -109], [-65, -35], [-92, -199], [49, -16], [-58, -75], [56, -76], [-103, -98], [-2, -23], [3, -45], [10, -18], [73, -38], [-5, -64], [60, -60], [-24, -29], [74, -74], [19, -97], [-35, -73], [42, -34], [-33, -73], [123, -53], [-37, -61], [69, -46]], [[3171, 19443], [6, 8], [43, 63], [7, 165], [108, 96], [-52, 126], [19, 191]], [[3302, 20092], [1, 0], [-82, 236], [-72, 58], [-1, 1], [-3, 21], [-6, 46], [-55, 9], [-51, 66], [-11, 49], [57, 76], [-74, 85], [-3, 135], [-31, 22], [-67, 46], [1, 3], [23, 90], [-37, 70], [8, 14], [5, 7], [60, 98], [46, 9], [-8, 73]], [[3002, 21306], [-7, 62], [98, 61], [28, 60], [94, -21], [-17, 38], [158, 65], [132, 145], [79, 4], [25, 167], [180, 4], [244, 217], [-1, 3], [118, -56], [448, -215], [6, -7], [27, -243], [36, -71], [6, -7], [229, -252], [35, -39], [34, -15], [236, -183], [13, -2], [21, -18], [109, 1], [176, -135], [390, -113], [17, 15], [55, -13], [-56, -33], [-76, -153], [-79, -37], [-19, -48], [-64, -7], [-5, -48], [-1, -9], [-15, 7], [-54, 26], [-4, 2], [0, 1], [0, 2], [-4, 32], [-84, -109], [15, -57], [-28, -28], [-8, 61], [-51, -9], [-35, 44], [-53, -75], [-53, 17], [2, -71], [-30, 1], [129, -89], [20, -54], [-36, -3]], [[5382, 20121], [-58, -3], [-171, -88], [24, -26], [-72, -79], [26, -27], [-38, -51], [81, 48], [9, -42], [-125, -57], [-6, -121], [-47, 55], [-38, -39], [61, -96], [-140, -97], [18, -52], [0, -2], [-76, -52], [-79, 31], [-56, -59], [-45, 13]], [[2359, 21650], [1, -1], [44, -9], [91, 68], [38, -8], [26, 57], [96, -24], [46, 66], [70, -20], [14, -4], [-10, -10], [-32, -32], [97, -110], [-12, -61], [56, -20], [-1, -4], [-18, -113], [-95, 13], [-48, -61], [108, -76], [91, 58], [81, -53]], [[3302, 20092], [-85, -36], [-311, 35], [-24, -68], [-144, -1], [-62, -87], [-53, 18], [-28, -34], [-111, 69], [-46, -96], [-126, 1], [-33, -67], [-60, 1], [-4, 59], [-110, 75], [-182, 32], [-55, 91], [-8, 2], [-25, -9], [-43, -98], [-49, 2], [-29, 87], [-108, 60], [-70, -117], [-169, -110], [-25, -51]], [[1342, 19850], [-76, 26], [-81, 119], [-42, -12], [-123, 80], [-172, -27], [-105, 46], [-61, 27], [-69, 29]], [[613, 20138], [-2, 1], [6, 91], [6, 6], [11, 10], [64, 57], [14, 117], [108, -41], [89, 42], [32, 75], [59, 9], [84, 87], [33, 127], [94, 101], [-9, 8], [-59, 57], [-3, 4], [12, 20], [77, 125], [-57, 50], [36, 63], [44, -1], [5, 89], [43, 19], [-28, 39], [-4, 2], [1, 1], [66, 84], [-63, 74], [14, 46]], [[435, 18507], [-16, 68], [-5, 11], [-4, 10], [-82, 47], [26, 67], [99, -17], [12, 126], [2, 17], [-22, 14], [-57, 38], [-36, 23], [18, 177], [-43, 151], [-71, 23], [-10, 170], [-55, 4], [-41, 55], [48, 81], [0, 1], [-42, 93], [24, 93], [0, 0], [0, 1], [-2, 88], [-36, 99], [60, 57], [-36, 125], [1, 3], [17, 40], [30, 73], [3, -2]], [[217, 20243], [156, -83], [149, 24], [53, -56], [8, -2], [30, 11], [0, 1]], [[1342, 19850], [-98, -14], [-18, 2], [0, -5], [-1, -4], [-5, -28], [43, -28], [-38, -31], [9, -70], [-42, -7], [-2, -50], [-54, 6], [7, -47], [-42, -41], [16, -46], [-38, -8], [122, -56], [-44, -31], [25, -57], [-36, -73], [56, -34], [-30, -20], [76, -199], [-46, -60], [30, -12], [-18, -60], [-56, -13]], [[5382, 20121], [139, -87], [14, -16], [35, -15], [2, 0], [3, -1], [58, 39], [37, -37], [28, 34], [1, -33], [51, -10], [28, -70], [-26, -24], [108, -99], [-23, -33], [55, -127], [-40, -40], [1, 0], [66, -26], [24, 44], [71, -37], [58, 42], [1, -1]], [[6073, 19624], [-1, -4], [29, -23], [-52, -12], [20, -45], [-88, -113], [33, -37], [-44, 22], [6, -63], [-55, 4], [28, -41], [-30, -14], [24, -96], [-103, -80], [75, -66], [-34, -55], [66, -80], [-6, -38], [-36, 13], [-16, -146], [66, -24], [-15, -98], [128, -54], [-34, -45], [24, -47], [87, -4], [-29, -58], [20, -53], [-66, -50], [87, -61], [-96, -86], [-36, -148], [-189, -59], [-68, 57], [20, 62], [-43, 23], [7, -87], [-54, -86], [29, -22], [-25, -46], [116, -105], [22, -74], [73, -49], [-10, -51], [52, -10], [-5, -49], [71, -70], [-31, -53], [-10, -18], [-8, -13], [51, -71], [-77, -150], [-1, -2], [12, -27]], [[6073, 19624], [14, 35], [4, 4], [23, -17], [66, -93], [60, 2], [13, 17], [30, 38], [1, 2], [0, 0], [0, 1], [-76, 21], [2, 54], [4, 21], [60, -33], [0, 3], [6, 48], [-33, 21], [24, 19], [243, -154], [27, 10], [106, -57], [-35, -180], [105, -78], [-41, -63], [28, -65], [131, 59], [126, -65], [26, -66], [155, -97], [15, 39], [170, -33], [-8, 50], [33, 22], [41, -133], [73, 1], [63, -147], [0, 0], [-18, -58]], [[217, 20243], [1, 1], [10, 22], [10, 110], [-4, 2], [-30, 18], [-73, 44], [-8, 42], [3, 42], [99, 57], [-77, 51], [34, 107], [15, 17], [-7, 6], [-64, 63], [-9, 8], [-2, 2], [29, 108], [0, 1], [-1, 0], [0, 0], [1, 1], [-114, 138], [38, 97], [59, 43], [-127, 109], [71, 33], [-10, 130], [81, 36], [6, 25], [29, 119], [22, 22], [6, 6]], [[16472, 14820], [311, -260], [206, -113], [88, -126], [174, -112], [217, 44], [77, -21], [398, 137], [53, -33], [89, 35], [-7, -71], [40, -68], [-20, -83], [81, -78], [-8, -123], [60, -86], [-54, -147], [39, -91], [-42, -16], [-16, -120]]], "transform": {"scale": [0.0003108213788206926, 0.0002680098839355288], "translate": [77.08732627947595, 23.871438]}, "objects": {"districts": {"type": "GeometryCollection", "geometries": [{"arcs": [[0, 1, 2, 3, 4, 5]], "type": "Polygon", "properties": {"dt_code": "137", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-5, 6, 7, 8, 9]], "type": "Polygon", "properties": {"dt_code": "705", "district": "Hapur", "st_code": "09", "year": "update2014", "st_nm": "Uttar Pradesh"}}, {"arcs": [[10, 11, 12, 13, 14]], "type": "Polygon", "properties": {"dt_code": "150", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-11, 15, 16, 17]], "type": "Polygon", "properties": {"dt_code": "151", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-4, 18, 19, 20, -7]], "type": "Polygon", "properties": {"dt_code": "142", "district": "<PERSON><PERSON>ndsha<PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-8, -21, 21, 22, 23]], "type": "Polygon", "properties": {"dt_code": "141", "district": "<PERSON><PERSON>am Buddha Nagar", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-17, 24, 25, 26, 27, 28]], "type": "Polygon", "properties": {"dt_code": "153", "district": "Lakhimpur Kheri", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-13, 29, 30, 31, 32, 33, 34, 35]], "type": "Polygon", "properties": {"dt_code": "149", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-26, 36, 37, 38, 39, 40]], "type": "Polygon", "properties": {"dt_code": "180", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-12, -18, -29, 41, 42, -30]], "type": "Polygon", "properties": {"dt_code": "152", "district": "Shahjahanpur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-20, 43, -33, 44, 45, 46, 47, -22]], "type": "Polygon", "properties": {"dt_code": "143", "district": "Aligarh", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-32, 48, 49, 50, -45]], "type": "Polygon", "properties": {"dt_code": "202", "district": "Kasganj", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-47, 51, 52, 53]], "type": "Polygon", "properties": {"dt_code": "145", "district": "<PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-38, 54, 55, 56]], "type": "Polygon", "properties": {"dt_code": "181", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-27, -41, 57, 58, 59]], "type": "Polygon", "properties": {"dt_code": "154", "district": "Sitapur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-46, -51, 60, 61, -52]], "type": "Polygon", "properties": {"dt_code": "144", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-50, 62, 63, 64, 65, -61]], "type": "Polygon", "properties": {"dt_code": "201", "district": "Etah", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-28, -60, 66, 67, 68, 69, 70, -42]], "type": "Polygon", "properties": {"dt_code": "155", "district": "<PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-31, -43, -71, 71, 72, -63, -49]], "type": "Polygon", "properties": {"dt_code": "159", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-65, 73, 74, 75]], "type": "Polygon", "properties": {"dt_code": "147", "district": "Firozabad", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[[76, 77, 78, 79, 80, 81, 82]], [[83]]], "type": "MultiPolygon", "properties": {"dt_code": "184", "district": "Siddharthnagar", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-64, -73, 84, 85, -74]], "type": "Polygon", "properties": {"dt_code": "148", "district": "Mainpuri", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-83, 86, 87, 88], [-84]], "type": "Polygon", "properties": {"dt_code": "187", "district": "Maharajganj", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-53, -62, -66, -76, 89, 90]], "type": "Polygon", "properties": {"dt_code": "146", "district": "Agra", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-39, -57, 91, -80, 92, 93, 94]], "type": "Polygon", "properties": {"dt_code": "183", "district": "Gonda", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-40, -95, 95, 96, 97, 98, -58]], "type": "Polygon", "properties": {"dt_code": "176", "district": "Barabanki", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-88, 99, 100, 101]], "type": "Polygon", "properties": {"dt_code": "189", "district": "Kushinagar", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-70, 102, 103, 104, 105, -85, -72]], "type": "Polygon", "properties": {"dt_code": "160", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-59, -99, 106, 107, -67]], "type": "Polygon", "properties": {"dt_code": "157", "district": "Lucknow", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-79, 108, 109, 110, -93]], "type": "Polygon", "properties": {"dt_code": "185", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-77, -89, -102, 111, 112, 113, 114, 115]], "type": "Polygon", "properties": {"dt_code": "188", "district": "Gorakhpur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-78, -116, 116, -109]], "type": "Polygon", "properties": {"dt_code": "186", "district": "Sant Kabir Nagar", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-68, -108, 117, 118, 119]], "type": "Polygon", "properties": {"dt_code": "156", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-75, -86, -106, 120, 121, 122, -90]], "type": "Polygon", "properties": {"dt_code": "161", "district": "Etawah", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-69, -120, 123, 124, 125, -103]], "type": "Polygon", "properties": {"dt_code": "164", "district": "Kanpur Nagar", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-105, 126, 127, -121]], "type": "Polygon", "properties": {"dt_code": "162", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-94, -111, 128, 129, 130, -96]], "type": "Polygon", "properties": {"dt_code": "177", "district": "Ayodhya", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-104, -126, 131, 132, -127]], "type": "Polygon", "properties": {"dt_code": "163", "district": "Kanpur Dehat", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-101, 133, 134, 135, -112]], "type": "Polygon", "properties": {"dt_code": "190", "district": "Deoria", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-130, 136, 137, 138, 139, 140]], "type": "Polygon", "properties": {"dt_code": "179", "district": "Sultanpur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-110, -117, -115, 141, -137, -129]], "type": "Polygon", "properties": {"dt_code": "178", "district": "Ambedkar Nagar", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-98, 142, 143, 144, -118, -107]], "type": "Polygon", "properties": {"dt_code": "158", "district": "<PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-122, -128, -133, 145, 146, 147]], "type": "Polygon", "properties": {"dt_code": "165", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-114, 148, 149, 150, -138, -142]], "type": "Polygon", "properties": {"dt_code": "191", "district": "Azamgarh", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-113, -136, 151, 152, -149]], "type": "Polygon", "properties": {"dt_code": "192", "district": "<PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-119, -145, 153, 154, 155, 156, -124]], "type": "Polygon", "properties": {"dt_code": "172", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-135, 157, 158, -152]], "type": "Polygon", "properties": {"dt_code": "193", "district": "Ballia", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-139, -151, 159, 160, 161, 162, 163]], "type": "Polygon", "properties": {"dt_code": "194", "district": "Jaunpur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-147, 164, 165, 166, 167, 168, 169, 170, 171, 172]], "type": "Polygon", "properties": {"dt_code": "166", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-156, 173, 174, 175, 176]], "type": "Polygon", "properties": {"dt_code": "170", "district": "Banda", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-150, -153, -159, 177, 178, 179, -160]], "type": "Polygon", "properties": {"dt_code": "195", "district": "Ghazipur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-154, 180, 181, 182]], "type": "Polygon", "properties": {"dt_code": "174", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-163, 183, 184, 185, 186, -182, 187]], "type": "Polygon", "properties": {"dt_code": "175", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-161, -180, 188, 189, 190]], "type": "Polygon", "properties": {"dt_code": "197", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-155, -183, -187, 191, -174]], "type": "Polygon", "properties": {"dt_code": "171", "district": "Chitrakoot", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-179, 192, 193, 194, -189]], "type": "Polygon", "properties": {"dt_code": "196", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-162, -191, 195, -184]], "type": "Polygon", "properties": {"dt_code": "198", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-185, -196, -190, -195, 196, 197]], "type": "Polygon", "properties": {"dt_code": "199", "district": "Mirzapur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-172, 198]], "type": "Polygon", "properties": {"dt_code": "167", "district": "Lalitpur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-194, 199, -197]], "type": "Polygon", "properties": {"dt_code": "200", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-97, -131, -141, 200, -143]], "type": "Polygon", "properties": {"dt_code": "706", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "update2014", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-9, -24, 201, 202, 203]], "type": "Polygon", "properties": {"dt_code": "140", "district": "Ghaziabad", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-3, 204, -34, -44, -19]], "type": "Polygon", "properties": {"dt_code": "754", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[[205]], [[206]], [[-170, 207]], [[-168, 208]], [[-166, 209, -176, 210]]], "type": "MultiPolygon", "properties": {"dt_code": "169", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[211, 212, 213]], "type": "Polygon", "properties": {"dt_code": "132", "district": "Saharanpur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-1, 214, 215, 216, 217]], "type": "Polygon", "properties": {"dt_code": "134", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-212, 218, -216, 219, 220, 221]], "type": "Polygon", "properties": {"dt_code": "133", "district": "Muzaffarnagar", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-203, 222, 223, -221, 224]], "type": "Polygon", "properties": {"dt_code": "139", "district": "Baghpat", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-6, -10, -204, -225, -220, -215]], "type": "Polygon", "properties": {"dt_code": "138", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-2, -218, 225, 226, -35, -205]], "type": "Polygon", "properties": {"dt_code": "135", "district": "Moradabad", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-14, -36, -227, 227]], "type": "Polygon", "properties": {"dt_code": "136", "district": "Rampur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-213, -222, -224, 228]], "type": "Polygon", "properties": {"dt_code": "704", "district": "<PERSON><PERSON><PERSON>", "st_code": "09", "year": "update2014", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-56, 229, -81, -92]], "type": "Polygon", "properties": {"dt_code": "182", "district": "Balrampur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-125, -157, -177, -210, -165, -146, -132]], "type": "Polygon", "properties": {"dt_code": "168", "district": "Hamirpur", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}, {"arcs": [[-140, -164, -188, -181, -144, -201]], "type": "Polygon", "properties": {"dt_code": "173", "district": "Pratapgarh", "st_code": "09", "year": "2011_c", "st_nm": "Uttar Pradesh"}}]}}, "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:OGC:1.3:CRS84"}}}