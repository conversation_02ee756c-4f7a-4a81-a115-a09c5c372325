{"type": "Topology", "arcs": [[[4851, 7445], [-3, -10], [-51, -12], [-65, -96], [-148, -72], [66, -58], [-24, -108], [-1, 1], [-79, 20], [-76, -42], [56, -62], [-3, -68], [-36, -22], [29, -96], [-58, -23], [-20, -82], [-39, -14], [11, -45], [-41, -42], [170, -195], [-39, -81]], [[4500, 6338], [-50, -5], [-30, 37], [-119, -5], [-80, 73], [-70, -27], [-42, 69], [-93, 21], [-63, 71], [-92, 36]], [[3861, 6608], [-14, 6], [-66, 149], [-152, -15], [-60, 75], [-72, -21], [-28, 17], [-13, -1], [-81, -90], [35, -100], [-23, -29], [-16, -219], [-105, -59], [-100, 6], [-49, -33], [-86, 74], [-51, -58], [-103, -26], [-13, -56], [-180, -7], [31, -48], [-7, -132], [-126, -181], [-6, -127], [-22, -20], [-112, 12], [8, 158], [37, 47], [-31, 41], [-60, 53], [-29, -36], [-56, 1], [-28, -128], [38, -32], [8, -67], [-28, -59], [-47, -23], [-31, -152], [-24, 9], [-29, 110], [-118, 87], [-27, 92], [-29, -14], [-26, 51], [-27, -6], [16, -36], [-73, -27], [0, 35], [41, 2], [-35, 25], [36, 47], [-64, 78], [-76, -3], [-37, -77], [-79, -26], [-108, 33], [16, 51], [-34, 76], [-48, 21], [-96, -2], [-101, -35], [27, -51], [0, -1], [-6, -1], [-54, -12], [-16, -3], [-3, 1], [-8, -1], [-78, 45]], [[1163, 5996], [-1, 6], [-11, 7], [7, 97], [58, -19], [78, 68], [55, 172], [-11, 63], [-148, 154], [11, 47], [-75, 71], [-11, 34], [-55, 165], [12, 33], [52, 15], [53, 39], [2, 14], [-12, 23], [-92, 118], [-43, 35], [-96, -16], [-113, -83], [-19, 5], [-26, 9], [-35, 10], [-1, 2], [-38, 85], [-19, 152], [-1, 0], [-6, 2]], [[678, 7304], [-48, 94], [34, -11], [37, 54], [34, 2], [34, 112], [71, 41], [21, 91], [69, 0], [112, 73], [46, -52], [43, 16], [78, -35], [33, 87], [161, 22], [150, 127], [92, -12], [26, -4], [41, 43], [69, -11], [96, 113], [62, -20], [12, 71], [1, 2], [0, 1], [47, -16], [28, 4], [81, 22], [11, -12], [31, -36], [27, 13], [0, -1], [28, -104], [72, 39], [4, -3], [33, -25], [20, -15], [2, -4], [72, -129], [180, 3], [70, 80], [82, 13], [100, -46], [29, -94], [37, 37], [246, -35], [119, 40], [19, -28], [-17, -56], [37, -26], [17, -73], [143, -132], [62, 0], [31, 38], [80, -41], [154, 38], [-57, 106], [-4, 96], [-75, 41], [-36, 63], [10, 32], [-49, 1], [-3, 9], [-32, 91], [-79, 63], [0, 2], [-10, 38], [-3, 10], [0, 0], [43, 19], [-3, 42], [52, 38], [72, 159], [63, -31], [27, 22], [57, -73], [43, 23], [-20, 99], [22, 142], [114, 10], [34, 94], [178, -71], [15, -63], [77, -95], [-18, -72], [118, -35], [57, -51], [-42, -68], [77, -66], [-2, -28], [-63, -72], [96, -73], [55, 1], [46, -62], [12, -161], [46, -98], [141, -71], [84, 1], [27, -57], [-16, -69]], [[4851, 7445], [13, 6], [37, -164], [115, 36], [98, -11], [24, -110], [84, -24], [-23, -47], [36, -53], [103, -40], [25, -39], [165, 103], [5, 54], [89, -38], [87, 25], [56, 88], [59, -62], [110, -48], [126, 3], [46, -59], [18, -113], [58, -16], [57, -105], [80, 27], [91, -71], [33, 8], [61, -83], [56, 5], [127, -177]], [[6687, 6540], [-43, -50], [21, -80], [22, -61], [-6, 1], [-195, 20], [-122, -80], [9, -63], [133, -169], [-9, -66], [34, -53], [-80, -72], [-46, -123], [66, -176], [-30, -38], [30, -64], [46, -16], [-20, -97], [21, -20], [-107, -131], [-4, -97], [-23, -52]], [[6384, 5053], [-20, -45], [-7, -17], [-32, -16], [-13, -6], [-13, -6], [-62, 26], [-154, -86], [-77, 54], [-115, -23], [56, -172], [-54, -44], [-50, -101], [-1, -90], [-51, -61], [50, -64], [-12, -48], [30, -55], [59, -13], [48, -55], [-34, -36], [-9, -79], [-92, -53], [-152, -14], [-292, 59], [-165, -77], [-168, 87], [-33, -139]], [[5021, 3979], [-5, -5], [-120, -137], [-52, 25], [-82, -49], [-92, 145], [-37, 0], [27, -94], [-27, -44], [-113, -11], [6, 82], [-123, 65], [-21, 11]], [[4382, 3967], [-14, 8], [-19, 141], [-76, 46], [1, 2], [36, 80], [-26, 67], [-2, 1], [-85, 27], [-24, 60], [-44, 20], [9, 105], [4, 35], [-111, 108], [10, 46], [-66, 31]], [[3975, 4744], [-1, 1], [73, 110], [9, 104], [80, -11], [88, 32], [-69, 17], [40, 86], [-130, 168], [138, 14], [32, 136], [-21, 56], [66, 98], [80, 42], [19, 116], [43, 34], [1, 67], [52, 25], [79, -18], [31, 89], [0, 1], [4, 0], [71, 20], [16, 20], [20, 47], [9, 74], [-11, 31], [-117, 89], [-20, 128], [-21, 6], [-36, 12]], [[1163, 5996], [-1, 1], [-21, -153], [-4, -9], [-60, -33], [7, -50], [-22, -5], [30, -38], [-28, -11], [33, -23], [-52, -18], [-50, 27], [-30, -40], [0, -1], [1, -1], [92, -58], [38, -61], [33, 5], [-6, 36], [29, 12], [74, -58], [7, 53], [113, -47], [62, 35], [155, -103], [89, 3], [108, -73], [-41, -57], [-162, -74], [-75, -122], [24, -58], [152, -100], [24, -2], [14, 51], [60, -32], [-1, -37], [57, -7], [26, 53], [27, -20], [-4, -76], [58, -83], [-13, -45], [-6, -6], [-139, -127], [-14, -12], [1, -2], [20, -61], [52, -15], [-18, -72], [124, -85], [3, 3]], [[1929, 4400], [14, -9], [-1, -8], [-43, -22], [-27, -124], [-89, -85], [-6, -52], [-47, -6], [11, -73], [-78, -84]], [[1663, 3937], [-39, -13], [28, 82], [-23, 30], [-46, -106], [-61, 12], [-50, 106], [-89, 32], [8, 53], [-79, 44], [1, 106], [-90, 89], [-82, 230], [-129, 172], [11, 33]], [[1023, 4807], [3, 3], [2, 2], [-2, 0], [0, 0], [-2, -1], [-50, -7], [-125, 81], [-126, 19], [-330, 186], [-83, 145], [0, 0], [-32, -1], [-4, 38], [-50, 35], [-2, 6], [-8, 1], [-76, 8], [-88, 9], [-25, -17], [-20, -1], [-1, 0], [-1, 12], [-3, 23], [0, 2], [42, 32], [16, 13], [3, 2], [124, 15], [25, 51], [100, 0], [81, 79], [252, 104], [20, 51], [7, 20], [-23, 104], [-116, -10], [-81, 111], [0, 1], [7, 34], [68, -23], [-1, 62], [65, -6], [-39, 102], [27, 40], [-116, 65], [22, 85], [-3, 6], [-31, 45], [-44, 47], [-24, 10], [-28, 12], [-11, 4], [3, 58], [1, 0], [152, 196], [-46, 65], [37, 96], [41, 2], [15, -54], [70, -23], [-11, 127], [36, 35], [-5, 56], [-133, -12], [-36, 109], [26, 31], [34, 36], [37, -20], [74, -43], [8, -1], [54, -1], [-61, 27], [46, 102], [-18, 50], [-46, 20], [23, 47], [36, -7], [-1, 2]], [[3861, 6608], [6, -11], [-25, -27], [6, -95], [-26, -57], [-48, -2], [-22, -55], [-84, 10], [-96, -65], [-35, -75], [-3, -193], [-61, -83], [7, -55], [-69, -61], [9, -183], [-113, -97], [71, -70], [-17, -83], [-72, -92], [48, -30], [0, -108], [61, -33], [-8, -45], [37, -34], [-28, -63], [40, -84], [117, -43], [6, -2], [-49, -59], [-6, 5]], [[3507, 4818], [-9, 8], [4, 13], [-45, 29], [-3, -2], [-3, 2], [-59, -50], [-22, 7], [-8, -32], [-47, 22], [-53, -75], [-51, 28], [-103, -49], [-85, 75], [-88, 6], [-7, -90], [-88, -9], [-6, -41], [-64, -51], [-9, -87], [-52, -19], [10, -57], [-38, -15], [3, -45], [-24, -13], [35, -53], [-26, -47], [52, -13], [-29, -6], [-8, -41], [-147, 71], [-56, -66], [-37, 65], [-46, -61], [-8, 34], [-111, 43], [10, 97], [-35, 6], [-41, 71], [-10, 0], [-12, 0], [-26, 0], [-1, -22], [-44, -35], [-2, -1], [0, -1], [-107, 55], [-2, 1], [-61, -46], [-1, -8], [-18, -16]], [[3975, 4744], [-1, -2], [-98, -13], [-44, -86], [-50, -22], [-55, -10], [-221, 3], [9, 94], [33, -17], [1, 82], [-64, 18], [0, 1], [22, 26]], [[6687, 6540], [44, -26], [88, 29], [10, 50], [46, 11], [70, -93], [-4, -55], [40, -33], [58, 27], [38, -49], [-1, -77], [-41, -34], [-2, -57], [-91, -15], [-13, -60], [70, -79], [-19, -185], [60, -9], [51, -53], [161, 1], [83, -140], [50, 24], [53, -32], [85, 20], [71, -86], [142, -32], [112, -123], [176, 74], [15, -42], [29, 13], [174, -153], [49, 10], [69, -56], [138, -145], [45, -96], [159, -30], [55, -46], [63, 13], [72, -129], [169, 14], [86, -65], [-14, -62], [49, -56], [-44, -45], [-90, -29], [-54, 20], [-121, -56], [-32, 41], [9, 88], [-33, 22], [-119, -168], [39, -130], [-90, -42], [-17, -54], [-56, -4], [-142, -149], [-47, -144], [-66, -12], [-115, -137], [-122, 21], [-72, -22], [-78, -125], [-50, -216], [-123, -85], [-36, -88], [-154, 32], [-74, -33], [-25, -112], [-84, -58], [64, -233], [68, -47], [12, -72], [-38, -52], [6, -54], [-77, -110], [-91, -29], [-9, -35], [43, -31], [-42, -65], [-71, -34], [-7, -54], [-40, 16], [-31, -40], [39, -79], [-66, 21], [-9, -27], [-77, -16]], [[7060, 2282], [-157, 44], [-59, 55], [-30, -11], [-63, 89], [-47, 68], [-5, 4], [-22, 0], [-57, -1]], [[6620, 2530], [-4, 0], [-2, 4], [-19, 45], [-179, 71], [-28, 171], [-79, 136], [-2, 42], [-16, 36], [-20, 15], [-92, 48], [-54, 9], [2, 17], [1, 9]], [[6128, 3133], [1, 10], [-59, 74], [-95, 36], [-31, 73], [6, 16], [3, 9], [1, 2], [120, -45], [91, 98], [13, -29], [58, 2], [1, -74], [148, -6], [34, 17], [8, 25], [90, 30], [-12, 58], [-1, 2], [-36, 14], [2, 57], [-69, 52], [-44, -11], [3, 34], [75, -4], [17, 115], [107, 48], [85, -50], [58, 62], [109, -27], [26, 21], [-53, 117], [-126, 87], [-62, 158], [46, 138], [-6, 156], [57, 116], [32, 248], [-102, 35], [-12, 80], [-33, 25], [19, 33], [-69, 32], [-44, 71], [-75, 6], [-26, 2], [1, 7]], [[6128, 3133], [-21, 2], [-16, 8], [-190, 2], [-122, -48], [-164, 205], [19, 100], [-39, 42], [50, 49], [-99, 55], [-78, -8], [-15, 28], [-65, -27], [-94, 72], [-40, -14], [-138, 45], [-36, 72], [-18, 182], [-4, 38], [-2, 23], [-1, 6], [-18, 4], [-16, 10]], [[1663, 3937], [0, 0], [127, -115], [66, -23], [-22, -58], [129, -133], [43, -200], [10, -5]], [[2016, 3403], [1, -3], [-201, -184], [-148, -4], [-21, -141], [-64, -3], [-109, -124], [-130, -55], [14, -32], [-77, 18], [-23, -51], [-81, -52], [6, -53], [-67, 45], [-74, -49], [-90, 64], [40, 52], [78, -11], [15, 96], [1, 3], [-46, 17], [9, 52], [-79, 94], [26, 27], [9, 9], [-12, 3], [-57, 17], [-38, -57], [-79, 21], [-21, -48], [-32, 7], [-75, -58], [-36, 8], [-1, 0], [-57, 40], [31, 51], [-101, 45], [27, 62], [-35, 29], [29, 62], [-15, 83], [-61, 62], [20, 25], [-50, 51], [4, 54], [-60, 32], [-8, 16], [-2, 38], [2, 19], [84, 83], [-46, 65], [47, 63], [-39, 14], [75, 169], [53, 29], [-28, 93], [79, 151], [71, -31], [23, 56], [63, 31], [136, 219], [18, 91], [109, 94]], [[4382, 3967], [-1, -28], [6, -12], [-8, -31], [-5, -20], [-1, -5], [-91, -3], [-41, -116], [-48, 25], [-158, -70], [-38, -17], [-41, -69], [-3, -8], [-2, -4], [96, -80], [18, -15], [16, -53], [21, -73], [7, -26], [-1, -3], [-9, -18], [-8, -17], [-6, 1], [-23, 7], [-2, -115], [32, -63], [2, -2], [-1, -1], [-43, -60], [31, -51], [-24, -59], [-5, -12], [-3, 8], [-16, 34], [-4, -4], [-7, -8], [-29, -59], [-7, -26], [37, -49], [-36, -37], [-36, 15], [-17, -103]], [[3934, 2740], [-137, -7], [-28, -75], [21, -25], [-96, -57], [-47, -246], [-69, -65]], [[3578, 2265], [-14, -12], [-320, 96], [-145, 114], [-90, -1], [-17, 16], [-11, 1], [-194, 156], [-27, 12], [-29, 33], [-188, 214], [-5, 6], [-30, 61], [-22, 206], [-5, 6], [-368, 182], [-97, 48]], [[6620, 2530], [4, -3], [-44, -20], [-96, -45], [-180, -83], [11, 64], [-21, 16], [-19, -71], [-50, -14], [-46, 55], [-60, -20], [-67, -110], [-29, 11], [-6, -64]], [[6017, 2246], [-44, -18], [-7, -3], [-135, 82], [-38, -57], [-74, -6], [-24, 48], [19, 47], [-63, -6], [-54, 45], [-26, 169], [-84, 65], [-42, -17], [-29, 43], [-130, -52], [-57, 3], [-2, 34], [-59, -7], [-28, -117], [-41, -48], [-51, -1], [-269, 290], [-80, 19], [-4, 40], [-33, 16], [-1, -1], [-128, -68], [-2, -37], [-178, -175], [-179, 50], [-12, 3], [-16, 41], [-85, -27], [3, 45], [-87, 75], [-106, 8], [63, 11]], [[6017, 2246], [21, 2], [-61, -111], [69, -57], [-4, -31], [90, -1], [21, -68], [-76, -10], [-35, -108], [-69, 39], [1, 47], [-102, -5], [65, -90], [-16, -49], [18, -67], [98, 17], [33, -52], [101, -33], [53, -113], [-69, -102], [-53, -27], [-7, -49], [86, -93], [81, 13], [89, -39], [-1, -1], [-3, -7], [-51, -114], [29, -67], [17, -16]], [[6342, 1054], [3, -3], [-159, -68], [-43, -126], [-44, 0], [-9, -48], [-49, 110], [-121, -89], [2, 24], [-51, 18], [35, 57], [-16, 28], [-71, 1], [-25, 37], [-47, -36], [-95, 17], [-73, 101], [-82, 46], [-64, -87], [-560, 5], [52, 101], [-137, 192], [-282, 302], [-377, 113], [-70, -63], [-85, 72], [-90, -75], [-78, 112], [45, 50], [-28, 63], [-165, -24], [-77, 74], [-3, 0], [-118, 22], [-34, 6], [-49, -8]], [[3377, 1978], [5, 41], [52, 6], [16, 40], [65, 32], [62, 129], [46, 28], [-45, 11]], [[7060, 2282], [0, -103], [85, -62], [-9, -90], [124, -193], [-35, -47], [-38, -171], [3, -115], [-88, 32], [-12, 44], [-27, -26], [-7, -37], [45, -42], [34, -150], [-99, -67], [-131, 55], [-49, -34], [-56, -74], [-40, -128], [23, -6], [-35, -154], [29, -13], [-47, -82], [-46, 17], [-72, -153], [1, 7]], [[6613, 690], [9, 19], [-9, 9], [-33, 31], [-5, 5], [-1, 3], [-19, 78], [17, 57], [-114, 279], [-1, 1], [-20, -60], [-95, -58]], [[6613, 690], [-39, -79], [16, -251], [-35, -51], [43, -56], [-17, -62], [-84, -109], [-200, -82], [-41, 87], [12, 30], [-51, 42], [42, 46], [-102, -18], [-75, 49], [-9, 35], [23, 20], [98, -33], [-2, 45], [-54, 6], [-16, 51], [-26, -20], [-40, 32], [-45, -54], [8, -75], [-107, 1], [20, 54], [21, -31], [34, 80], [-35, 21], [34, 3], [-16, 48], [26, -24], [24, 37], [-13, 50], [-31, -14], [-35, 52], [-3, 1], [-14, 0], [-36, 0], [-5, -6], [-38, -45], [-91, 2], [-9, -38], [-51, 3], [-52, -63], [-63, 43], [2, 3], [-73, 45], [-189, -74], [-29, 26], [28, 78], [-73, 64], [-92, -110], [-146, 17], [-39, 5], [-10, -10], [-59, -6], [0, 0], [0, 2], [14, 62], [-11, 16], [-13, 17], [15, 49], [0, 0], [-52, 124], [-60, 0], [-34, 113], [-27, -19], [7, -42], [-140, 28], [-12, -34], [-127, 83], [-22, 56], [-104, 55], [-107, -50], [-23, 55], [33, 54], [-86, 66], [29, 152], [-88, 49], [-22, -8], [-199, 130], [-20, -16], [27, -18], [-4, -40], [-1, -3], [-49, 28], [-3, -18], [-2, -46], [62, -18], [0, 0], [1, 0], [-2, -2], [-24, -32], [-11, -14], [-49, -2], [-55, 79], [-18, 14], [-4, -3], [-11, -30], [-1, 1], [-48, -36], [-57, 31], [-21, -37], [-53, 22], [-1, 0], [33, 34], [-46, 108], [19, 28], [-89, 84], [22, 20], [-24, 60], [-42, 8], [0, 28], [-24, -28], [-30, 31], [-48, -34], [-2, 1], [-1, 1], [-29, 13], [-11, 13], [-115, 74], [30, 2], [-17, 46], [-106, 76], [25, -1], [-2, 60], [44, -14], [43, 63], [29, -37], [42, 8], [7, -52], [23, 24], [-13, 48], [69, 92], [4, -27], [0, -2], [0, -1], [3, -1], [44, -22], [12, -6], [1, 8]]], "transform": {"scale": [0.0003781506207797862, 0.00031579468515309067], "translate": [77.573108, 28.722897]}, "objects": {"districts": {"type": "GeometryCollection", "geometries": [{"arcs": [[0, 1, 2, 3, 4]], "type": "Polygon", "properties": {"dt_code": "056", "district": "Uttarkashi", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-1, 5, 6, 7, 8, 9, 10]], "type": "Polygon", "properties": {"dt_code": "057", "district": "<PERSON><PERSON><PERSON>", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-4, 11, 12, 13, 14]], "type": "Polygon", "properties": {"dt_code": "060", "district": "Dehradun", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-3, 15, 16, -12]], "type": "Polygon", "properties": {"dt_code": "059", "district": "<PERSON><PERSON><PERSON>", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-2, -11, 17, -16]], "type": "Polygon", "properties": {"dt_code": "058", "district": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-7, 18, 19, 20, 21]], "type": "Polygon", "properties": {"dt_code": "062", "district": "Pithoragarh", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-8, -22, 22]], "type": "Polygon", "properties": {"dt_code": "063", "district": "<PERSON><PERSON><PERSON><PERSON>", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-14, 23, 24]], "type": "Polygon", "properties": {"dt_code": "068", "district": "<PERSON><PERSON><PERSON>", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-10, 25, 26, 27, -24, -13, -17, -18]], "type": "Polygon", "properties": {"dt_code": "061", "district": "<PERSON><PERSON>", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-9, -23, -21, 28, 29, -26]], "type": "Polygon", "properties": {"dt_code": "064", "district": "Almora", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-27, -30, 30, 31, 32]], "type": "Polygon", "properties": {"dt_code": "066", "district": "Nainital", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-20, 33, 34, -31, -29]], "type": "Polygon", "properties": {"dt_code": "065", "district": "Champawat", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}, {"arcs": [[-32, -35, 35]], "type": "Polygon", "properties": {"dt_code": "067", "district": "Udham Singh Nagar", "st_code": "05", "year": "2011_c", "st_nm": "Uttarakhand"}}]}}, "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:OGC:1.3:CRS84"}}}