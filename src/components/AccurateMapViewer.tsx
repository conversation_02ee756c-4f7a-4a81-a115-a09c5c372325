import React, { useState, useCallback, useMemo } from 'react';
import { useMapStore } from '../store/mapStore';
import { useGeographicData } from '../services/geographicService';
import type { State } from '../types';
import type { AccurateStateData } from '../utils/geographicUtils';
import clsx from 'clsx';

interface AccurateMapViewerProps {
  className?: string;
  showTooltips?: boolean;
  showLabels?: boolean;
}

interface MapTooltip {
  visible: boolean;
  x: number;
  y: number;
  content: {
    name: string;
    templeCount: number;
    description: string;
    area: number;
  };
}

/**
 * Enhanced MapViewer component using accurate geographic data from SVG
 * Replaces hardcoded simplified paths with professional cartographic boundaries
 */
const AccurateMapViewer: React.FC<AccurateMapViewerProps> = ({ 
  className,
  showTooltips = true,
  showLabels = true 
}) => {
  const { viewState, states, selectState } = useMapStore();
  const { mapData, loading, error } = useGeographicData();
  const [tooltip, setTooltip] = useState<MapTooltip>({
    visible: false,
    x: 0,
    y: 0,
    content: { name: '', templeCount: 0, description: '', area: 0 }
  });
  const [hoveredState, setHoveredState] = useState<string | null>(null);

  // Create mapping between accurate geographic data and application states
  const stateMapping = useCallback(() => {
    if (!mapData || !states) return new Map();
    
    const mapping = new Map<string, { appState: State; geoState: AccurateStateData }>();
    
    states.forEach(appState => {
      const geoState = mapData.states.find(geo => geo.id === appState.id);
      if (geoState) {
        mapping.set(appState.id, { appState, geoState });
      }
    });
    
    return mapping;
  }, [mapData, states]);

  const stateData = useMemo(() => stateMapping(), [stateMapping]);

  const handleStateMouseEnter = useCallback((
    appState: State, 
    geoState: AccurateStateData, 
    event: React.MouseEvent
  ) => {
    setHoveredState(appState.id);
    
    const parentRect = (event.currentTarget as SVGElement).closest('svg')?.getBoundingClientRect();
    
    if (parentRect && showTooltips) {
      setTooltip({
        visible: true,
        x: event.clientX - parentRect.left,
        y: event.clientY - parentRect.top,
        content: {
          name: appState.name,
          templeCount: appState.totalTemples,
          description: appState.description,
          area: Math.round(geoState.area)
        }
      });
    }
  }, [showTooltips]);

  const handleStateMouseLeave = useCallback(() => {
    setHoveredState(null);
    setTooltip(prev => ({ ...prev, visible: false }));
  }, []);

  const handleStateClick = useCallback((appState: State) => {
    selectState(appState);
  }, [selectState]);

  const getStateStyle = useCallback((stateId: string) => {
    const isSelected = viewState.selectedState?.id === stateId;
    const isHovered = hoveredState === stateId;
    
    return clsx(
      'transition-all duration-300 ease-in-out cursor-pointer',
      'stroke-2 stroke-white',
      {
        'fill-blue-200 hover:fill-blue-300': !isSelected && !isHovered,
        'fill-blue-400 hover:fill-blue-500': isHovered && !isSelected,
        'fill-orange-400 stroke-orange-600': isSelected,
        'filter drop-shadow-lg': isHovered || isSelected,
      }
    );
  }, [viewState.selectedState?.id, hoveredState]);

  // Loading state
  if (loading) {
    return (
      <div className={clsx('flex items-center justify-center', className)}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading accurate map data...</span>
      </div>
    );
  }

  // Error state
  if (error || !mapData) {
    return (
      <div className={clsx('flex items-center justify-center bg-red-50 border border-red-200 rounded-lg p-4', className)}>
        <div className="text-red-600">
          <span className="font-medium">Failed to load map data:</span>
          <span className="ml-2">{error || 'Unknown error'}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('relative w-full h-full', className)}>
      <svg
        viewBox={mapData.configuration.viewBox}
        className="w-full h-full"
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="xMidYMid meet"
        role="img"
        aria-label="Interactive map of India showing states with accurate geographic boundaries"
      >
        <defs>
          {/* Enhanced gradients and filters */}
          <linearGradient id="accurateMapBackground" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#f8fafc" />
            <stop offset="50%" stopColor="#e2e8f0" />
            <stop offset="100%" stopColor="#f1f5f9" />
          </linearGradient>
          
          <filter id="accurateStateShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="2" dy="2" stdDeviation="3" floodOpacity="0.15"/>
          </filter>
          
          <filter id="accurateSelectedShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="4" dy="4" stdDeviation="5" floodOpacity="0.3"/>
          </filter>
        </defs>

        {/* Background */}
        <rect
          x="0" y="0" 
          width={mapData.configuration.width} 
          height={mapData.configuration.height}
          fill="url(#accurateMapBackground)"
        />

        {/* States with accurate boundaries */}
        {Array.from(stateData.entries()).map(([stateId, { appState, geoState }]) => (
          <g key={stateId}>
            {/* Accurate state shape from SVG */}
            <path
              d={geoState.svgPath}
              className={getStateStyle(stateId)}
              filter={viewState.selectedState?.id === stateId ? "url(#accurateSelectedShadow)" : "url(#accurateStateShadow)"}
              onMouseEnter={(e) => handleStateMouseEnter(appState, geoState, e)}
              onMouseLeave={handleStateMouseLeave}
              onClick={() => handleStateClick(appState)}
              aria-label={`${appState.name} - ${appState.totalTemples} temples`}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleStateClick(appState);
                }
              }}
            />

            {/* State labels using calculated centers */}
            {showLabels && (
              <text
                x={geoState.center.x}
                y={geoState.center.y}
                textAnchor="middle"
                className="text-xs font-semibold fill-slate-700 pointer-events-none select-none"
                style={{ fontSize: '10px' }}
              >
                {geoState.name}
              </text>
            )}

            {/* Temple count indicators */}
            <circle
              cx={geoState.center.x + 20}
              cy={geoState.center.y - 20}
              r="10"
              fill="#f97316"
              stroke="white"
              strokeWidth="2"
              className="pointer-events-none"
            />
            <text
              x={geoState.center.x + 20}
              y={geoState.center.y - 16}
              textAnchor="middle"
              className="text-xs font-bold fill-white pointer-events-none select-none"
            >
              {appState.totalTemples}
            </text>
          </g>
        ))}

        {/* Compass */}
        <g transform={`translate(${mapData.configuration.width - 60}, 60)`}>
          <circle cx="0" cy="0" r="20" fill="white" stroke="#64748b" strokeWidth="1.5" opacity="0.9"/>
          <path d="M 0,-15 L 6,0 L 0,15 L -6,0 Z" fill="#ef4444"/>
          <text x="0" y="30" textAnchor="middle" className="text-xs fill-slate-600 font-medium">N</text>
        </g>

        {/* Data source attribution */}
        <text 
          x={mapData.configuration.width - 10} 
          y={mapData.configuration.height - 10} 
          textAnchor="end"
          className="text-xs fill-slate-500"
        >
          Accurate Geographic Data
        </text>
      </svg>

      {/* Enhanced Tooltip */}
      {tooltip.visible && showTooltips && (
        <div
          className="absolute z-20 bg-white rounded-lg shadow-xl border border-gray-200 p-4 min-w-64 pointer-events-none"
          style={{
            left: tooltip.x + 10,
            top: tooltip.y - 100,
            transform: 'translateX(-50%)'
          }}
        >
          <div className="font-semibold text-gray-900 mb-1">{tooltip.content.name}</div>
          <div className="text-sm text-gray-600 mb-2 line-clamp-2">{tooltip.content.description}</div>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">Temples:</span>
              <span className="font-medium text-orange-600">{tooltip.content.templeCount}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">Area:</span>
              <span className="font-medium text-blue-600">{tooltip.content.area.toLocaleString()} sq units</span>
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-400">Click to explore temples</div>
        </div>
      )}

      {/* Map statistics overlay */}
      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-md">
        <div className="text-sm font-medium text-gray-900 mb-1">Map Statistics</div>
        <div className="text-xs text-gray-600 space-y-1">
          <div>States: {mapData.states.length}</div>
          <div>Total Area: {Math.round(mapData.totalArea).toLocaleString()}</div>
          <div>Projection: {mapData.configuration.projection}</div>
        </div>
      </div>
    </div>
  );
};

export default AccurateMapViewer;
