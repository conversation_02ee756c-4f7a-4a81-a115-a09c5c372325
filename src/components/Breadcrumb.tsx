import React from 'react';
import { useMapStore } from '../store/mapStore';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';

interface BreadcrumbProps {
  className?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ className }) => {
  const { viewState, resetView, goBack } = useMapStore();

  const breadcrumbItems = [
    {
      label: 'India',
      icon: HomeIcon,
      onClick: resetView,
      active: viewState.level === 'country'
    }
  ];

  if (viewState.selectedState) {
    breadcrumbItems.push({
      label: viewState.selectedState.name,
      icon: HomeIcon, // Use HomeIcon as placeholder
      onClick: () => {
        if (viewState.level === 'city') {
          goBack();
        }
      },
      active: viewState.level === 'state'
    });
  }

  if (viewState.selectedCity) {
    breadcrumbItems.push({
      label: viewState.selectedCity.name,
      icon: HomeIcon, // Use HomeIcon as placeholder
      onClick: () => {},
      active: viewState.level === 'city'
    });
  }

  return (
    <nav className={clsx('flex items-center space-x-2 text-sm', className)}>
      {breadcrumbItems.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <ChevronRightIcon className="w-4 h-4 text-gray-400" />
          )}
          <button
            onClick={item.onClick}
            className={clsx(
              'flex items-center space-x-1 px-3 py-1.5 rounded-lg transition-all duration-200',
              item.active
                ? 'bg-primary-100 text-primary-700 font-medium'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            )}
          >
            {item.icon && <item.icon className="w-4 h-4" />}
            <span>{item.label}</span>
          </button>
        </React.Fragment>
      ))}
    </nav>
  );
};

export default Breadcrumb;
