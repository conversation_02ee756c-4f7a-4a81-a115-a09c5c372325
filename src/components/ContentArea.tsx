import React from 'react';
import { useMapStore } from '../store/mapStore';
import TempleCard from './TempleCard';
import { 
  BuildingLibraryIcon,
  MapIcon,
  InformationCircleIcon 
} from '@heroicons/react/24/outline';
import clsx from 'clsx';

interface ContentAreaProps {
  className?: string;
}

const ContentArea: React.FC<ContentAreaProps> = ({ className }) => {
  const { viewState, loading } = useMapStore();

  const renderEmptyState = () => (
    <div className="text-center py-12">
      <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
        <MapIcon className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        Explore India's Sacred Temples
      </h3>
      <p className="text-gray-600 max-w-md mx-auto">
        Click on a state in the map above to discover beautiful temples and their rich history.
      </p>
    </div>
  );

  const renderStateInfo = () => {
    if (!viewState.selectedState) return null;

    return (
      <div className="mb-8">
        <div className="card">
          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
              <BuildingLibraryIcon className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                {viewState.selectedState.name}
              </h2>
              <p className="text-gray-600 mb-4">
                {viewState.selectedState.description}
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Capital:</span>
                  <span className="ml-2 text-gray-600">{viewState.selectedState.capital}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Total Temples:</span>
                  <span className="ml-2 text-gray-600">{viewState.selectedState.totalTemples}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">State Code:</span>
                  <span className="ml-2 text-gray-600">{viewState.selectedState.code}</span>
                </div>
              </div>
              
              {viewState.selectedState.famousTemples.length > 0 && (
                <div className="mt-4">
                  <span className="font-medium text-gray-700">Famous Temples:</span>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {viewState.selectedState.famousTemples.map((temple, index) => (
                      <span
                        key={index}
                        className="bg-primary-50 text-primary-700 px-3 py-1 rounded-full text-sm"
                      >
                        {temple}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderCityInfo = () => {
    if (!viewState.selectedCity) return null;

    return (
      <div className="mb-8">
        <div className="card">
          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-secondary-500 to-accent-500 rounded-lg flex items-center justify-center">
              <MapIcon className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                {viewState.selectedCity.name}
              </h2>
              <p className="text-gray-600 mb-4">
                {viewState.selectedCity.description}
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Total Temples:</span>
                  <span className="ml-2 text-gray-600">{viewState.selectedCity.totalTemples}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">State:</span>
                  <span className="ml-2 text-gray-600">{viewState.selectedState?.name}</span>
                </div>
              </div>
              
              {viewState.selectedCity.famousTemples.length > 0 && (
                <div className="mt-4">
                  <span className="font-medium text-gray-700">Famous Temples:</span>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {viewState.selectedCity.famousTemples.map((temple, index) => (
                      <span
                        key={index}
                        className="bg-secondary-50 text-secondary-700 px-3 py-1 rounded-full text-sm"
                      >
                        {temple}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderTemples = () => {
    if (loading) {
      return (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading temples...</p>
        </div>
      );
    }

    if (viewState.selectedTemples.length === 0) {
      return (
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <InformationCircleIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Temples Found
          </h3>
          <p className="text-gray-600">
            No temple data available for this location yet.
          </p>
        </div>
      );
    }

    return (
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900">
            Temples ({viewState.selectedTemples.length})
          </h3>
          <div className="text-sm text-gray-600">
            {viewState.level === 'state' && `in ${viewState.selectedState?.name}`}
            {viewState.level === 'city' && `in ${viewState.selectedCity?.name}`}
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {viewState.selectedTemples.map((temple) => (
            <TempleCard key={temple.id} temple={temple} />
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={clsx('space-y-6', className)}>
      {viewState.level === 'country' && renderEmptyState()}
      {viewState.level === 'state' && (
        <>
          {renderStateInfo()}
          {renderTemples()}
        </>
      )}
      {viewState.level === 'city' && (
        <>
          {renderCityInfo()}
          {renderTemples()}
        </>
      )}
    </div>
  );
};

export default ContentArea;
