import React, { useState, useCallback, useMemo } from 'react';
import { useMapStore } from '../store/mapStore';
import type { State, City } from '../types';
import clsx from 'clsx';

interface EnhancedIndiaMapProps {
  className?: string;
  showTooltips?: boolean;
  showLabels?: boolean;
  showLegend?: boolean;
}

interface MapTooltip {
  visible: boolean;
  x: number;
  y: number;
  content: {
    name: string;
    templeCount: number;
    description: string;
  };
}

const EnhancedIndiaMap: React.FC<EnhancedIndiaMapProps> = ({
  className,
  showTooltips = true,
  showLabels = true,
  showLegend = true
}) => {
  const { viewState, states, selectState, selectCity } = useMapStore();
  const [tooltip, setTooltip] = useState<MapTooltip>({
    visible: false,
    x: 0,
    y: 0,
    content: { name: '', templeCount: 0, description: '' }
  });
  const [hoveredState, setHoveredState] = useState<string | null>(null);

  // Enhanced map data with proper geographical boundaries
  const enhancedMapData = useMemo(() => ({
    viewBox: "0 0 1000 800",
    states: {
      rajasthan: {
        path: "M 100,220 L 260,200 L 320,240 L 360,340 L 320,420 L 260,440 L 200,420 L 140,380 L 120,320 L 100,260 Z",
        center: { x: 230, y: 320 },
        label: "Rajasthan",
        templeCount: 45
      },
      "tamil-nadu": {
        path: "M 420,600 L 520,580 L 580,620 L 620,720 L 580,760 L 520,750 L 460,730 L 420,680 Z",
        center: { x: 520, y: 670 },
        label: "Tamil Nadu",
        templeCount: 75
      },
      karnataka: {
        path: "M 320,540 L 420,520 L 480,560 L 520,640 L 480,680 L 420,670 L 360,640 L 320,580 Z",
        center: { x: 420, y: 600 },
        label: "Karnataka",
        templeCount: 65
      },
      kerala: {
        path: "M 280,640 L 320,620 L 360,660 L 400,720 L 360,760 L 320,750 L 280,730 L 260,690 Z",
        center: { x: 330, y: 690 },
        label: "Kerala",
        templeCount: 40
      },
      "andhra-pradesh": {
        path: "M 520,480 L 620,460 L 680,500 L 720,580 L 680,620 L 620,610 L 560,590 L 520,520 Z",
        center: { x: 620, y: 540 },
        label: "Andhra Pradesh",
        templeCount: 55
      },
      odisha: {
        path: "M 620,360 L 720,340 L 780,380 L 820,460 L 780,500 L 720,490 L 660,470 L 620,400 Z",
        center: { x: 720, y: 420 },
        label: "Odisha",
        templeCount: 50
      },
      "west-bengal": {
        path: "M 700,280 L 800,260 L 860,300 L 900,380 L 860,420 L 800,410 L 740,390 L 700,320 Z",
        center: { x: 800, y: 340 },
        label: "West Bengal",
        templeCount: 35
      },
      bihar: {
        path: "M 580,250 L 680,230 L 720,270 L 760,350 L 720,390 L 680,380 L 620,360 L 580,290 Z",
        center: { x: 670, y: 320 },
        label: "Bihar",
        templeCount: 60
      },
      jharkhand: {
        path: "M 620,320 L 720,300 L 760,340 L 800,420 L 760,460 L 720,450 L 660,430 L 620,360 Z",
        center: { x: 710, y: 380 },
        label: "Jharkhand",
        templeCount: 30
      },
      "uttar-pradesh": {
        path: "M 400,180 L 580,160 L 640,200 L 680,280 L 640,320 L 580,310 L 520,290 L 460,250 L 420,210 Z",
        center: { x: 540, y: 240 },
        label: "Uttar Pradesh",
        templeCount: 120
      },
      gujarat: {
        path: "M 80,280 L 180,260 L 220,300 L 260,380 L 220,420 L 180,410 L 140,390 L 100,350 Z",
        center: { x: 170, y: 340 },
        label: "Gujarat",
        templeCount: 60
      },
      maharashtra: {
        path: "M 220,380 L 360,360 L 420,400 L 480,480 L 440,520 L 360,510 L 300,490 L 240,450 Z",
        center: { x: 350, y: 440 },
        label: "Maharashtra",
        templeCount: 80
      },
      "madhya-pradesh": {
        path: "M 280,260 L 440,240 L 500,280 L 560,360 L 520,400 L 440,390 L 380,370 L 320,330 Z",
        center: { x: 420, y: 320 },
        label: "Madhya Pradesh",
        templeCount: 70
      }
    }
  }), []);

  const handleStateMouseEnter = useCallback((state: State, event: React.MouseEvent) => {
    setHoveredState(state.id);
    
    if (showTooltips) {
      const rect = event.currentTarget.getBoundingClientRect();
      setTooltip({
        visible: true,
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
        content: {
          name: state.name,
          templeCount: state.totalTemples,
          description: state.description
        }
      });
    }
  }, [showTooltips]);

  const handleStateMouseLeave = useCallback(() => {
    setHoveredState(null);
    setTooltip(prev => ({ ...prev, visible: false }));
  }, []);

  const handleStateClick = useCallback((state: State) => {
    selectState(state);
  }, [selectState]);

  const getStateStyle = useCallback((stateId: string) => {
    const isSelected = viewState.selectedState?.id === stateId;
    const isHovered = hoveredState === stateId;
    
    return clsx(
      'transition-all duration-300 ease-in-out cursor-pointer',
      'stroke-2 stroke-white',
      {
        'fill-blue-200 hover:fill-blue-300': !isSelected && !isHovered,
        'fill-blue-400 hover:fill-blue-500': isHovered && !isSelected,
        'fill-orange-400 stroke-orange-600': isSelected,
        'drop-shadow-lg': isHovered || isSelected,
        'transform scale-105': isHovered,
      }
    );
  }, [viewState.selectedState?.id, hoveredState]);

  return (
    <div className={clsx('relative w-full', className)}>
      {/* Map Container */}
      <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl overflow-hidden shadow-lg border border-white/20">
        <svg
          viewBox={enhancedMapData.viewBox}
          className="w-full h-auto"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="xMidYMid meet"
          role="img"
          aria-label="Interactive map of India showing states with temple information"
        >
          <defs>
            {/* Enhanced gradients and filters */}
            <linearGradient id="mapBackground" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#dbeafe" />
              <stop offset="50%" stopColor="#e0e7ff" />
              <stop offset="100%" stopColor="#f0f9ff" />
            </linearGradient>
            
            <filter id="stateShadow" x="-20%" y="-20%" width="140%" height="140%">
              <feDropShadow dx="3" dy="3" stdDeviation="4" floodOpacity="0.2"/>
            </filter>
            
            <filter id="selectedShadow" x="-20%" y="-20%" width="140%" height="140%">
              <feDropShadow dx="5" dy="5" stdDeviation="6" floodOpacity="0.4"/>
            </filter>
          </defs>

          {/* Background */}
          <rect
            x="0" y="0" 
            width="1000" height="800"
            fill="url(#mapBackground)"
          />

          {/* Ocean/Water bodies */}
          <circle cx="150" cy="700" r="40" fill="#bfdbfe" opacity="0.6" />
          <circle cx="850" cy="200" r="30" fill="#bfdbfe" opacity="0.6" />
          
          {/* States */}
          {states.map((state) => {
            const stateMapData = enhancedMapData.states[state.id as keyof typeof enhancedMapData.states];
            if (!stateMapData) return null;

            return (
              <g key={state.id}>
                {/* State shape */}
                <path
                  d={stateMapData.path}
                  className={getStateStyle(state.id)}
                  filter={viewState.selectedState?.id === state.id ? "url(#selectedShadow)" : "url(#stateShadow)"}
                  onMouseEnter={(e) => handleStateMouseEnter(state, e)}
                  onMouseLeave={handleStateMouseLeave}
                  onClick={() => handleStateClick(state)}
                  aria-label={`${state.name} - ${state.totalTemples} temples`}
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleStateClick(state);
                    }
                  }}
                />

                {/* State labels */}
                {showLabels && (
                  <text
                    x={stateMapData.center.x}
                    y={stateMapData.center.y}
                    textAnchor="middle"
                    className="text-sm font-semibold fill-slate-700 pointer-events-none select-none"
                    style={{ fontSize: '14px' }}
                  >
                    {stateMapData.label}
                  </text>
                )}

                {/* Temple count indicators */}
                <circle
                  cx={stateMapData.center.x + 20}
                  cy={stateMapData.center.y - 20}
                  r="12"
                  fill="#f97316"
                  stroke="white"
                  strokeWidth="2"
                  className="pointer-events-none"
                />
                <text
                  x={stateMapData.center.x + 20}
                  y={stateMapData.center.y - 16}
                  textAnchor="middle"
                  className="text-xs font-bold fill-white pointer-events-none select-none"
                >
                  {state.totalTemples}
                </text>
              </g>
            );
          })}

          {/* Decorative compass */}
          <g transform="translate(900, 100)">
            <circle cx="0" cy="0" r="25" fill="white" stroke="#64748b" strokeWidth="2" opacity="0.9"/>
            <path d="M 0,-20 L 8,0 L 0,20 L -8,0 Z" fill="#ef4444"/>
            <text x="0" y="35" textAnchor="middle" className="text-xs fill-slate-600 font-medium">N</text>
          </g>
        </svg>

        {/* Tooltip */}
        {tooltip.visible && showTooltips && (
          <div
            className="absolute z-20 bg-white rounded-lg shadow-xl border border-gray-200 p-4 min-w-64 pointer-events-none"
            style={{
              left: tooltip.x + 10,
              top: tooltip.y - 80,
              transform: 'translateX(-50%)'
            }}
          >
            <div className="font-semibold text-gray-900 mb-1">{tooltip.content.name}</div>
            <div className="text-sm text-gray-600 mb-2">{tooltip.content.description}</div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span className="text-sm font-medium">{tooltip.content.templeCount} Temples</span>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Legend */}
      {showLegend && (
        <div className="mt-4 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Map Legend</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-200 border border-blue-300 rounded"></div>
              <span className="text-sm text-gray-600">States</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-orange-400 border border-orange-500 rounded"></div>
              <span className="text-sm text-gray-600">Selected</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
              <span className="text-sm text-gray-600">Temple Count</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-300 rounded"></div>
              <span className="text-sm text-gray-600">Water Bodies</span>
            </div>
          </div>
          
          {/* Additional Info */}
          <div className="mt-4 pt-3 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              Click on any state to explore temples • Hover for details • Use keyboard navigation for accessibility
            </p>
          </div>
        </div>
      )}

      {/* Performance Stats */}
      <div className="mt-2 flex justify-between text-xs text-gray-500">
        <span>Showing {states.length} states</span>
        <span>Total: {states.reduce((sum, state) => sum + state.totalTemples, 0)} temples</span>
      </div>
    </div>
  );
};

export default EnhancedIndiaMap;
