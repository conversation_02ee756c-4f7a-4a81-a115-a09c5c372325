/**
 * Enhanced MapViewer Component
 * Renders interactive India map with high-precision geographic data
 */

import React, { useState, useEffect, useMemo } from 'react';
import { useTempleStore } from '../store/templeStore';
import { StateGeographicData } from '../types/geographic';
import { SVGMapDataExtractor, ExtractedMapData } from '../services/svgMapDataExtractor';

interface EnhancedMapViewerProps {
  className?: string;
  onStateSelect?: (stateCode: string) => void;
  selectedState?: string | null;
  showTempleCount?: boolean;
}

interface MapState {
  svgData: ExtractedMapData | null;
  enhancedStates: StateGeographicData[];
  loading: boolean;
  error: string | null;
}

export const EnhancedMapViewer: React.FC<EnhancedMapViewerProps> = ({
  className = '',
  onStateSelect,
  selectedState,
  showTempleCount = true
}) => {
  const { temples, stateData } = useTempleStore();
  const [mapState, setMapState] = useState<MapState>({
    svgData: null,
    enhancedStates: [],
    loading: true,
    error: null
  });
  const [hoveredState, setHoveredState] = useState<string | null>(null);

  // Load and process SVG map data
  useEffect(() => {
    const loadMapData = async () => {
      try {
        setMapState(prev => ({ ...prev, loading: true, error: null }));
        
        // Load SVG file
        const response = await fetch('/src/assets/maps/in.svg');
        if (!response.ok) {
          throw new Error('Failed to load map SVG');
        }
        
        const svgContent = await response.text();
        const extractedData = SVGMapDataExtractor.extractFromSVG(svgContent);
        
        // Enhance existing state data with SVG paths
        const enhanced = SVGMapDataExtractor.enhanceGeographicData(extractedData, stateData);
        
        setMapState({
          svgData: extractedData,
          enhancedStates: enhanced,
          loading: false,
          error: null
        });
      } catch (error) {
        console.error('Error loading map data:', error);
        setMapState(prev => ({
          ...prev,
          loading: false,
          error: error instanceof Error ? error.message : 'Failed to load map data'
        }));
      }
    };

    loadMapData();
  }, [stateData]);

  // Calculate temple counts per state
  const templeCounts = useMemo(() => {
    const counts: Record<string, number> = {};
    temples.forEach(temple => {
      counts[temple.state] = (counts[temple.state] || 0) + 1;
    });
    return counts;
  }, [temples]);

  // Get color intensity based on temple count
  const getStateColor = (stateCode: string, isHovered: boolean, isSelected: boolean): string => {
    if (isSelected) return '#e11d48'; // Selected state - red
    if (isHovered) return '#f97316'; // Hovered state - orange
    
    const count = templeCounts[stateCode] || 0;
    if (count === 0) return '#f3f4f6'; // No temples - light gray
    if (count <= 5) return '#ddd6fe'; // Few temples - light purple
    if (count <= 15) return '#c4b5fd'; // Some temples - medium purple
    if (count <= 30) return '#a78bfa'; // Many temples - darker purple
    return '#8b5cf6'; // Most temples - dark purple
  };

  // Handle state click
  const handleStateClick = (stateId: string) => {
    // Find the state code from the SVG ID
    const state = mapState.enhancedStates.find(s => 
      stateId.includes(s.code.toLowerCase()) || 
      s.name.toLowerCase().includes(stateId.replace('in-', ''))
    );
    
    if (state && onStateSelect) {
      onStateSelect(state.code);
    }
  };

  // Render loading state
  if (mapState.loading) {
    return (
      <div className={`flex items-center justify-center min-h-96 ${className}`}>
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <p className="text-gray-600">Loading enhanced map data...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (mapState.error) {
    return (
      <div className={`flex items-center justify-center min-h-96 ${className}`}>
        <div className="text-center">
          <div className="text-red-500 mb-2">⚠️</div>
          <p className="text-red-600 font-medium">Error loading map</p>
          <p className="text-gray-600 text-sm">{mapState.error}</p>
        </div>
      </div>
    );
  }

  // Render map
  if (!mapState.svgData) {
    return null;
  }

  const { svgData } = mapState;

  return (
    <div className={`relative ${className}`}>
      {/* Map Container */}
      <div className="relative bg-white rounded-lg shadow-lg overflow-hidden">
        <svg
          viewBox={`${svgData.viewBox.x} ${svgData.viewBox.y} ${svgData.viewBox.width} ${svgData.viewBox.height}`}
          className="w-full h-auto"
          style={{ maxHeight: '600px' }}
        >
          {/* Render state paths */}
          {svgData.states.map((state) => {
            const isHovered = hoveredState === state.id;
            const isSelected = selectedState && mapState.enhancedStates.some(s => 
              s.code === selectedState && (
                state.id.includes(s.code.toLowerCase()) || 
                s.name.toLowerCase().includes(state.id.replace('in-', ''))
              )
            );
            
            return (
              <path
                key={state.id}
                d={state.path}
                fill={getStateColor(state.id, isHovered, Boolean(isSelected))}
                stroke="#ffffff"
                strokeWidth="1"
                className="transition-all duration-200 cursor-pointer hover:stroke-2"
                onMouseEnter={() => setHoveredState(state.id)}
                onMouseLeave={() => setHoveredState(null)}
                onClick={() => handleStateClick(state.id)}
              />
            );
          })}
        </svg>

        {/* State tooltip */}
        {hoveredState && (
          <div className="absolute top-4 left-4 bg-white p-3 rounded-lg shadow-lg border z-10">
            <div className="font-medium text-gray-900">
              {svgData.states.find(s => s.id === hoveredState)?.name}
            </div>
            {showTempleCount && (
              <div className="text-sm text-gray-600">
                {templeCounts[hoveredState] || 0} temples
              </div>
            )}
          </div>
        )}
      </div>

      {/* Legend */}
      {showTempleCount && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-2">Temple Count Legend</h4>
          <div className="flex flex-wrap gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-gray-200 rounded"></div>
              <span>No temples</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-purple-200 rounded"></div>
              <span>1-5 temples</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-purple-300 rounded"></div>
              <span>6-15 temples</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-purple-400 rounded"></div>
              <span>16-30 temples</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-purple-600 rounded"></div>
              <span>30+ temples</span>
            </div>
          </div>
        </div>
      )}

      {/* Data Quality Indicator */}
      <div className="mt-2 text-xs text-gray-500 text-center">
        Enhanced with high-precision SVG geographic data
      </div>
    </div>
  );
};
