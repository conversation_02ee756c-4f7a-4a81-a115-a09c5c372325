import React from 'react';
import { useMapStore } from '../store/mapStore';
import MapViewer from './MapViewer';
import Breadcrumb from './Breadcrumb';
import ContentArea from './ContentArea';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

const IndiaMap: React.FC = () => {
  const { viewState, goBack } = useMapStore();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-white/20 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gradient">
                Hindu Temple Locator
              </h1>
              <div className="hidden md:block">
                <span className="text-sm text-gray-600">
                  Discover India's Sacred Heritage
                </span>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {viewState.level !== 'country' && (
                <button
                  onClick={goBack}
                  className="btn-secondary flex items-center space-x-2"
                >
                  <ArrowLeftIcon className="w-4 h-4" />
                  <span>Back</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb Navigation */}
        <div className="mb-8">
          <Breadcrumb />
        </div>

        {/* Map and Content Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Map Section */}
          <div className="space-y-6">
            <div className="card">
              <div className="mb-4">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Interactive Map
                </h2>
                <p className="text-sm text-gray-600">
                  {viewState.level === 'country' && 'Click on a state to explore its temples'}
                  {viewState.level === 'state' && 'Click on a city to see temples in that area'}
                  {viewState.level === 'city' && 'Viewing temples in this city'}
                </p>
              </div>
              <MapViewer />
            </div>

            {/* Map Legend */}
            <div className="card">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Map Legend</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-primary-200 border border-primary-300 rounded"></div>
                  <span className="text-sm text-gray-600">States</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-secondary-200 border border-secondary-300 rounded"></div>
                  <span className="text-sm text-gray-600">Cities</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-primary-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Selected Area</span>
                </div>
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="space-y-6">
            <ContentArea />
          </div>
        </div>

        {/* Additional Information */}
        {viewState.level === 'country' && (
          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="card text-center">
              <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">🏛️</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Rich Heritage
              </h3>
              <p className="text-gray-600 text-sm">
                Explore thousands of ancient temples with rich history and architectural marvels.
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">🗺️</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Interactive Maps
              </h3>
              <p className="text-gray-600 text-sm">
                Navigate through states and cities with our intuitive map interface.
              </p>
            </div>

            <div className="card text-center">
              <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-accent-500 to-accent-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">📍</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Detailed Information
              </h3>
              <p className="text-gray-600 text-sm">
                Get comprehensive details about visiting hours, festivals, and contact information.
              </p>
            </div>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white/80 backdrop-blur-md border-t border-white/20 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-gray-600 text-sm">
              © 2024 Hindu Temple Locator. Discover India's Sacred Heritage.
            </p>
            <p className="text-gray-500 text-xs mt-2">
              Built with React, TypeScript, and Tailwind CSS
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default IndiaMap;
