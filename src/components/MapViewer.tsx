import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useMapStore } from '../store/mapStore';
import { useGeographicData, getGeographicService } from '../services/geographicService';
import type { State } from '../types';
import type { AccurateStateData } from '../utils/geographicUtils';
import clsx from 'clsx';

interface MapViewerProps {
  className?: string;
}

interface MapTooltip {
  visible: boolean;
  x: number;
  y: number;
  content: {
    name: string;
    templeCount: number;
    description: string;
  };
}

const MapViewer: React.FC<MapViewerProps> = ({ className }) => {
  const { viewState, states, selectState } = useMapStore();
  const [tooltip, setTooltip] = useState<MapTooltip>({
    visible: false,
    x: 0,
    y: 0,
    content: { name: '', templeCount: 0, description: '' }
  });
  const [hoveredState, setHoveredState] = useState<string | null>(null);

  // Enhanced map data with better geographical accuracy
  const enhancedMapData = useMemo(() => ({
    viewBox: "0 0 1000 800",
    states: {
      rajasthan: {
        path: "M 100,220 L 260,200 L 320,240 L 360,340 L 320,420 L 260,440 L 200,420 L 140,380 L 120,320 L 100,260 Z",
        center: { x: 230, y: 320 },
        label: "Rajasthan"
      },
      "tamil-nadu": {
        path: "M 420,600 L 520,580 L 580,620 L 620,720 L 580,760 L 520,750 L 460,730 L 420,680 Z",
        center: { x: 520, y: 670 },
        label: "Tamil Nadu"
      },
      karnataka: {
        path: "M 320,540 L 420,520 L 480,560 L 520,640 L 480,680 L 420,670 L 360,640 L 320,580 Z",
        center: { x: 420, y: 600 },
        label: "Karnataka"
      },
      kerala: {
        path: "M 280,640 L 320,620 L 360,660 L 400,720 L 360,760 L 320,750 L 280,730 L 260,690 Z",
        center: { x: 330, y: 690 },
        label: "Kerala"
      },
      "andhra-pradesh": {
        path: "M 520,480 L 620,460 L 680,500 L 720,580 L 680,620 L 620,610 L 560,590 L 520,520 Z",
        center: { x: 620, y: 540 },
        label: "Andhra Pradesh"
      },
      telangana: {
        path: "M 480,420 L 580,400 L 620,440 L 660,520 L 620,560 L 580,550 L 520,530 L 480,460 Z",
        center: { x: 570, y: 480 },
        label: "Telangana"
      },
      odisha: {
        path: "M 620,360 L 720,340 L 780,380 L 820,460 L 780,500 L 720,490 L 660,470 L 620,400 Z",
        center: { x: 720, y: 420 },
        label: "Odisha"
      },
      "west-bengal": {
        path: "M 700,280 L 800,260 L 860,300 L 900,380 L 860,420 L 800,410 L 740,390 L 700,320 Z",
        center: { x: 800, y: 340 },
        label: "West Bengal"
      },
      bihar: {
        path: "M 580,250 L 680,230 L 720,270 L 760,350 L 720,390 L 680,380 L 620,360 L 580,290 Z",
        center: { x: 670, y: 320 },
        label: "Bihar"
      },
      jharkhand: {
        path: "M 620,320 L 720,300 L 760,340 L 800,420 L 760,460 L 720,450 L 660,430 L 620,360 Z",
        center: { x: 710, y: 380 },
        label: "Jharkhand"
      },
      "uttar-pradesh": {
        path: "M 400,180 L 580,160 L 640,200 L 680,280 L 640,320 L 580,310 L 520,290 L 460,250 L 420,210 Z",
        center: { x: 540, y: 240 },
        label: "Uttar Pradesh"
      },
      gujarat: {
        path: "M 80,280 L 180,260 L 220,300 L 260,380 L 220,420 L 180,410 L 140,390 L 100,350 Z",
        center: { x: 170, y: 340 },
        label: "Gujarat"
      },
      maharashtra: {
        path: "M 220,380 L 360,360 L 420,400 L 480,480 L 440,520 L 360,510 L 300,490 L 240,450 Z",
        center: { x: 350, y: 440 },
        label: "Maharashtra"
      },
      "madhya-pradesh": {
        path: "M 280,260 L 440,240 L 500,280 L 560,360 L 520,400 L 440,390 L 380,370 L 320,330 Z",
        center: { x: 420, y: 320 },
        label: "Madhya Pradesh"
      },
      punjab: {
        path: "M 240,120 L 340,100 L 380,140 L 420,220 L 380,260 L 340,250 L 280,230 L 240,160 Z",
        center: { x: 330, y: 180 },
        label: "Punjab"
      },
      haryana: {
        path: "M 320,140 L 420,120 L 460,160 L 500,240 L 460,280 L 420,270 L 360,250 L 320,180 Z",
        center: { x: 410, y: 200 },
        label: "Haryana"
      },
      "himachal-pradesh": {
        path: "M 300,80 L 400,60 L 440,100 L 480,180 L 440,220 L 400,210 L 340,190 L 300,120 Z",
        center: { x: 390, y: 140 },
        label: "Himachal Pradesh"
      },
      uttarakhand: {
        path: "M 420,100 L 520,80 L 560,120 L 600,200 L 560,240 L 520,230 L 460,210 L 420,140 Z",
        center: { x: 510, y: 160 },
        label: "Uttarakhand"
      },
      "jammu-and-kashmir": {
        path: "M 240,40 L 380,20 L 420,60 L 460,140 L 420,180 L 380,170 L 300,150 L 240,80 Z",
        center: { x: 350, y: 100 },
        label: "Jammu & Kashmir"
      },
      ladakh: {
        path: "M 420,20 L 520,0 L 560,40 L 600,120 L 560,160 L 520,150 L 460,130 L 420,60 Z",
        center: { x: 510, y: 80 },
        label: "Ladakh"
      },
      chhattisgarh: {
        path: "M 480,380 L 580,360 L 620,400 L 660,480 L 620,520 L 580,510 L 520,490 L 480,420 Z",
        center: { x: 570, y: 440 },
        label: "Chhattisgarh"
      },
      assam: {
        path: "M 780,200 L 860,180 L 890,220 L 920,300 L 890,340 L 860,330 L 820,310 L 780,240 Z",
        center: { x: 850, y: 260 },
        label: "Assam"
      },
      "arunachal-pradesh": {
        path: "M 860,120 L 940,100 L 960,140 L 980,220 L 960,260 L 940,250 L 900,230 L 860,160 Z",
        center: { x: 920, y: 180 },
        label: "Arunachal Pradesh"
      },
      nagaland: {
        path: "M 860,200 L 920,180 L 940,220 L 960,280 L 940,320 L 920,310 L 880,290 L 860,240 Z",
        center: { x: 910, y: 250 },
        label: "Nagaland"
      },
      manipur: {
        path: "M 880,280 L 920,260 L 940,300 L 960,360 L 940,400 L 920,390 L 900,370 L 880,320 Z",
        center: { x: 920, y: 330 },
        label: "Manipur"
      },
      mizoram: {
        path: "M 820,360 L 860,340 L 880,380 L 900,440 L 880,480 L 860,470 L 840,450 L 820,400 Z",
        center: { x: 860, y: 410 },
        label: "Mizoram"
      },
      tripura: {
        path: "M 800,320 L 840,300 L 860,340 L 880,400 L 860,440 L 840,430 L 820,410 L 800,360 Z",
        center: { x: 840, y: 370 },
        label: "Tripura"
      },
      meghalaya: {
        path: "M 740,280 L 780,260 L 800,300 L 820,360 L 800,400 L 780,390 L 760,370 L 740,320 Z",
        center: { x: 780, y: 330 },
        label: "Meghalaya"
      },
      sikkim: {
        path: "M 700,200 L 730,180 L 750,220 L 770,280 L 750,320 L 730,310 L 710,290 L 700,240 Z",
        center: { x: 735, y: 250 },
        label: "Sikkim"
      },
      goa: {
        path: "M 240,480 L 280,460 L 300,500 L 320,560 L 300,600 L 280,590 L 260,570 L 240,520 Z",
        center: { x: 280, y: 530 },
        label: "Goa"
      }
    }
  }), []);

  const handleStateMouseEnter = useCallback((state: State, event: React.MouseEvent) => {
    setHoveredState(state.id);
    
    const rect = (event.currentTarget as SVGElement).getBoundingClientRect();
    const parentRect = (event.currentTarget as SVGElement).closest('svg')?.getBoundingClientRect();
    
    if (parentRect) {
      setTooltip({
        visible: true,
        x: event.clientX - parentRect.left,
        y: event.clientY - parentRect.top,
        content: {
          name: state.name,
          templeCount: state.totalTemples,
          description: state.description
        }
      });
    }
  }, []);

  const handleStateMouseLeave = useCallback(() => {
    setHoveredState(null);
    setTooltip(prev => ({ ...prev, visible: false }));
  }, []);

  const handleStateClick = useCallback((state: State) => {
    selectState(state);
  }, [selectState]);

  const getStateStyle = useCallback((stateId: string) => {
    const isSelected = viewState.selectedState?.id === stateId;
    const isHovered = hoveredState === stateId;
    
    return clsx(
      'transition-all duration-300 ease-in-out cursor-pointer',
      'stroke-2 stroke-white',
      {
        'fill-blue-200 hover:fill-blue-300': !isSelected && !isHovered,
        'fill-blue-400 hover:fill-blue-500': isHovered && !isSelected,
        'fill-orange-400 stroke-orange-600': isSelected,
        'filter drop-shadow-lg': isHovered || isSelected,
      }
    );
  }, [viewState.selectedState?.id, hoveredState]);

  const renderCountryView = () => (
    <div className="relative">
      <svg
        viewBox={enhancedMapData.viewBox}
        className="w-full h-full"
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="xMidYMid meet"
        role="img"
        aria-label="Interactive map of India showing states with temple information"
      >
        <defs>
          {/* Enhanced gradients and filters */}
          <linearGradient id="mapBackground" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#dbeafe" />
            <stop offset="50%" stopColor="#e0e7ff" />
            <stop offset="100%" stopColor="#f0f9ff" />
          </linearGradient>
          
          <filter id="stateShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="3" dy="3" stdDeviation="4" floodOpacity="0.2"/>
          </filter>
          
          <filter id="selectedShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="5" dy="5" stdDeviation="6" floodOpacity="0.4"/>
          </filter>
        </defs>

        {/* Background */}
        <rect
          x="0" y="0" 
          width="1000" height="800"
          fill="url(#mapBackground)"
        />

        {/* Ocean/Water bodies indication */}
        <circle cx="150" cy="700" r="40" fill="#bfdbfe" opacity="0.6" />
        <circle cx="850" cy="200" r="30" fill="#bfdbfe" opacity="0.6" />
        
        {/* States */}
        {states.map((state) => {
          const stateMapData = enhancedMapData.states[state.id as keyof typeof enhancedMapData.states];
          if (!stateMapData) return null;

          return (
            <g key={state.id}>
              {/* State shape */}
              <path
                d={stateMapData.path}
                className={getStateStyle(state.id)}
                filter={viewState.selectedState?.id === state.id ? "url(#selectedShadow)" : "url(#stateShadow)"}
                onMouseEnter={(e) => handleStateMouseEnter(state, e)}
                onMouseLeave={handleStateMouseLeave}
                onClick={() => handleStateClick(state)}
                aria-label={`${state.name} - ${state.totalTemples} temples`}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleStateClick(state);
                  }
                }}
              />

              {/* State labels */}
              <text
                x={stateMapData.center.x}
                y={stateMapData.center.y}
                textAnchor="middle"
                className="text-sm font-semibold fill-slate-700 pointer-events-none select-none"
                style={{ fontSize: '12px' }}
              >
                {stateMapData.label}
              </text>

              {/* Temple count indicators */}
              <circle
                cx={stateMapData.center.x + 25}
                cy={stateMapData.center.y - 25}
                r="12"
                fill="#f97316"
                stroke="white"
                strokeWidth="2"
                className="pointer-events-none"
              />
              <text
                x={stateMapData.center.x + 25}
                y={stateMapData.center.y - 21}
                textAnchor="middle"
                className="text-xs font-bold fill-white pointer-events-none select-none"
              >
                {state.totalTemples}
              </text>
            </g>
          );
        })}

        {/* Decorative compass */}
        <g transform="translate(900, 100)">
          <circle cx="0" cy="0" r="25" fill="white" stroke="#64748b" strokeWidth="2" opacity="0.9"/>
          <path d="M 0,-20 L 8,0 L 0,20 L -8,0 Z" fill="#ef4444"/>
          <text x="0" y="35" textAnchor="middle" className="text-xs fill-slate-600 font-medium">N</text>
        </g>
      </svg>

      {/* Tooltip */}
      {tooltip.visible && (
        <div
          className="absolute z-20 bg-white rounded-lg shadow-xl border border-gray-200 p-4 min-w-64 pointer-events-none"
          style={{
            left: tooltip.x + 10,
            top: tooltip.y - 80,
            transform: 'translateX(-50%)'
          }}
        >
          <div className="font-semibold text-gray-900 mb-1">{tooltip.content.name}</div>
          <div className="text-sm text-gray-600 mb-2 line-clamp-2">{tooltip.content.description}</div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span className="text-sm font-medium">{tooltip.content.templeCount} Temples</span>
          </div>
        </div>
      )}
    </div>
  );

  const renderStateView = () => {
    if (!viewState.selectedState) return null;

    const stateMapData = enhancedMapData.states[viewState.selectedState.id as keyof typeof enhancedMapData.states];
    if (!stateMapData) return null;

    return (
      <div className="relative">
        <svg
          viewBox="0 0 600 500"
          className="w-full h-full"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="xMidYMid meet"
        >
          <defs>
            <linearGradient id="stateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#0ea5e9" stopOpacity="0.2" />
              <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.3" />
            </linearGradient>
          </defs>

          {/* State outline - centered and scaled */}
          <path
            d={stateMapData.path}
            fill="url(#stateGradient)"
            stroke="#0ea5e9"
            strokeWidth="3"
            transform="translate(50, 50) scale(1.2)"
          />

          {/* State center marker */}
          <circle
            cx={stateMapData.center.x * 1.2 + 50}
            cy={stateMapData.center.y * 1.2 + 50}
            r="6"
            fill="#0ea5e9"
            stroke="#fff"
            strokeWidth="2"
          />

          <text
            x={stateMapData.center.x * 1.2 + 50}
            y={stateMapData.center.y * 1.2 + 70}
            textAnchor="middle"
            className="text-lg font-semibold fill-slate-800"
          >
            {viewState.selectedState.name}
          </text>
        </svg>
      </div>
    );
  };

  const renderCityView = () => {
    if (!viewState.selectedCity || !viewState.selectedState) return null;

    return (
      <div className="relative">
        <svg
          viewBox="0 0 300 200"
          className="w-full h-full"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="xMidYMid meet"
        >
          <defs>
            <linearGradient id="cityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#d946ef" stopOpacity="0.3" />
              <stop offset="100%" stopColor="#f97316" stopOpacity="0.3" />
            </linearGradient>
          </defs>

          {/* City representation as a circle */}
          <circle
            cx="150"
            cy="80"
            r="40"
            fill="url(#cityGradient)"
            stroke="#d946ef"
            strokeWidth="4"
          />

          {/* City center marker */}
          <circle
            cx="150"
            cy="80"
            r="8"
            fill="#f97316"
            stroke="#fff"
            strokeWidth="2"
            className="animate-pulse"
          />

          <text
            x="150"
            y="140"
            textAnchor="middle"
            className="text-lg font-semibold fill-slate-800"
          >
            {viewState.selectedCity.name}
          </text>
          
          <text
            x="150"
            y="160"
            textAnchor="middle"
            className="text-sm text-slate-600"
          >
            {viewState.selectedState.name}
          </text>
        </svg>
      </div>
    );
  };

  return (
    <div className={clsx('relative bg-white rounded-xl shadow-soft border border-gray-200', className)}>
      <div className="p-6">
        <div className="aspect-[4/3] w-full">
          {viewState.level === 'country' && renderCountryView()}
          {viewState.level === 'state' && renderStateView()}
          {viewState.level === 'city' && renderCityView()}
        </div>
      </div>
    </div>
  );
};

export default MapViewer;
