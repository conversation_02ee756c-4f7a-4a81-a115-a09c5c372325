import React from 'react';
import { useMapStore } from '../store/mapStore';
import { State, City } from '../types';
import { indiaMapData, getStateCenter, getCityCenter } from '../data/mapData';
import clsx from 'clsx';

interface MapViewerProps {
  className?: string;
}

const MapViewer: React.FC<MapViewerProps> = ({ className }) => {
  const { viewState, states, selectState, selectCity } = useMapStore();

  const handleStateClick = (state: State) => {
    selectState(state);
  };

  const handleCityClick = (city: City) => {
    selectCity(city);
  };

  const renderCountryView = () => (
    <svg
      viewBox={indiaMapData.viewBox}
      className="w-full h-full"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="mapGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#f97316" stopOpacity="0.1" />
          <stop offset="100%" stopColor="#0ea5e9" stopOpacity="0.1" />
        </linearGradient>
      </defs>

      {/* India outline background */}
      <rect
        x="20" y="20" width="760" height="560"
        fill="url(#mapGradient)"
        stroke="#e2e8f0"
        strokeWidth="2"
        className="opacity-20"
        rx="10"
      />

      {/* Render states */}
      {states.map((state) => {
        const stateData = indiaMapData.states[state.id as keyof typeof indiaMapData.states];
        if (!stateData) return null;

        return (
          <g key={state.id}>
            <path
              d={stateData.path}
              className={clsx(
                'map-state',
                viewState.selectedState?.id === state.id && 'map-state-active'
              )}
              onClick={() => handleStateClick(state)}
            />
            <text
              x={stateData.center.x}
              y={stateData.center.y}
              textAnchor="middle"
              className="text-xs font-medium fill-slate-700 pointer-events-none select-none"
            >
              {state.name}
            </text>
          </g>
        );
      })}
    </svg>
  );

  const renderStateView = () => {
    if (!viewState.selectedState) return null;

    const stateData = indiaMapData.states[viewState.selectedState.id as keyof typeof indiaMapData.states];
    const stateCities = indiaMapData.cities[viewState.selectedState.id as keyof typeof indiaMapData.cities];

    if (!stateData) return null;

    return (
      <svg
        viewBox="0 0 400 300"
        className="w-full h-full"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="stateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#0ea5e9" stopOpacity="0.2" />
            <stop offset="100%" stopColor="#d946ef" stopOpacity="0.2" />
          </linearGradient>
        </defs>

        {/* State outline - centered and scaled */}
        <path
          d={stateData.path}
          fill="url(#stateGradient)"
          stroke="#0ea5e9"
          strokeWidth="3"
          transform="translate(50, 50) scale(1.2)"
        />

        {/* Render cities if available */}
        {stateCities && viewState.selectedState.cities.map((city) => {
          const cityData = (stateCities as any)[city.id.split('-').pop()];
          if (!cityData) return null;

          return (
            <g key={city.id}>
              <path
                d={cityData.path}
                className={clsx(
                  'map-city',
                  viewState.selectedCity?.id === city.id && 'map-city-active'
                )}
                onClick={() => handleCityClick(city)}
                transform="translate(50, 50) scale(1.2)"
              />
              <text
                x={cityData.center.x * 1.2 + 50}
                y={cityData.center.y * 1.2 + 50}
                textAnchor="middle"
                className="text-xs font-medium fill-slate-700 pointer-events-none select-none"
              >
                {city.name}
              </text>
            </g>
          );
        })}
      </svg>
    );
  };

  const renderCityView = () => {
    if (!viewState.selectedCity || !viewState.selectedState) return null;

    const stateCities = indiaMapData.cities[viewState.selectedState.id as keyof typeof indiaMapData.cities];
    const cityData = stateCities && (stateCities as any)[viewState.selectedCity.id.split('-').pop()];

    if (!cityData) return null;

    return (
      <svg
        viewBox="0 0 300 200"
        className="w-full h-full"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="cityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#d946ef" stopOpacity="0.3" />
            <stop offset="100%" stopColor="#f97316" stopOpacity="0.3" />
          </linearGradient>
        </defs>

        {/* City outline - centered and scaled */}
        <path
          d={cityData.path}
          fill="url(#cityGradient)"
          stroke="#d946ef"
          strokeWidth="4"
          transform="translate(100, 50) scale(2)"
        />

        {/* City center marker */}
        <circle
          cx="150"
          cy="100"
          r="8"
          fill="#f97316"
          stroke="#fff"
          strokeWidth="2"
          className="animate-pulse-slow"
        />

        <text
          x="150"
          y="130"
          textAnchor="middle"
          className="text-lg font-semibold fill-slate-800"
        >
          {viewState.selectedCity.name}
        </text>
      </svg>
    );
  };

  return (
    <div className={clsx('relative bg-white rounded-xl shadow-soft border border-gray-200', className)}>
      <div className="p-6">
        <div className="aspect-[4/3] w-full">
          {viewState.level === 'country' && renderCountryView()}
          {viewState.level === 'state' && renderStateView()}
          {viewState.level === 'city' && renderCityView()}
        </div>
      </div>
    </div>
  );
};

export default MapViewer;
