import React from 'react';
import type { Temple } from '../types';
import {
  MapPinIcon,
  ClockIcon,
  StarIcon,
  PhoneIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import clsx from 'clsx';

interface TempleCardProps {
  temple: Temple;
  className?: string;
}

const TempleCard: React.FC<TempleCardProps> = ({ temple, className }) => {
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <StarSolidIcon key={i} className="w-4 h-4 text-yellow-400" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <div key="half" className="relative">
          <StarIcon className="w-4 h-4 text-yellow-400" />
          <StarSolidIcon
            className="w-4 h-4 text-yellow-400 absolute top-0 left-0"
            style={{ clipPath: 'inset(0 50% 0 0)' }}
          />
        </div>
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <StarIcon key={`empty-${i}`} className="w-4 h-4 text-gray-300" />
      );
    }

    return stars;
  };

  return (
    <div className={clsx('card group hover:scale-105', className)}>
      {/* Temple Image */}
      <div className="relative mb-4 overflow-hidden rounded-lg">
        <div className="aspect-[16/10] bg-gradient-to-br from-primary-100 to-secondary-100 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <div className="w-16 h-16 mx-auto mb-2 bg-gray-200 rounded-lg flex items-center justify-center">
              <GlobeAltIcon className="w-8 h-8" />
            </div>
            <p className="text-sm">Temple Image</p>
          </div>
        </div>

        {/* Rating Badge */}
        <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1 flex items-center space-x-1">
          <StarSolidIcon className="w-4 h-4 text-yellow-400" />
          <span className="text-sm font-medium text-gray-900">{temple.rating}</span>
        </div>
      </div>

      {/* Temple Info */}
      <div className="space-y-3">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
            {temple.name}
          </h3>
          <div className="flex items-center text-sm text-gray-600 mt-1">
            <MapPinIcon className="w-4 h-4 mr-1" />
            <span>{temple.location.city}, {temple.location.state}</span>
          </div>
        </div>

        <p className="text-sm text-gray-600 line-clamp-3">
          {temple.description}
        </p>

        {/* Deity */}
        <div className="flex items-center justify-between">
          <div>
            <span className="text-xs font-medium text-primary-600 bg-primary-50 px-2 py-1 rounded-full">
              {temple.deity}
            </span>
          </div>
          <div className="text-xs text-gray-500">
            {temple.reviews} reviews
          </div>
        </div>

        {/* Visiting Hours */}
        <div className="flex items-center text-sm text-gray-600">
          <ClockIcon className="w-4 h-4 mr-2" />
          <span>{temple.visitingHours.opening} - {temple.visitingHours.closing}</span>
        </div>

        {/* Rating */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {renderStars(temple.rating)}
          </div>
          <div className="text-sm font-medium text-gray-900">
            {temple.entryFee}
          </div>
        </div>

        {/* Contact Info */}
        {(temple.contact.phone || temple.contact.website) && (
          <div className="flex items-center space-x-4 pt-2 border-t border-gray-100">
            {temple.contact.phone && (
              <a
                href={`tel:${temple.contact.phone}`}
                className="flex items-center text-sm text-gray-600 hover:text-primary-600 transition-colors"
              >
                <PhoneIcon className="w-4 h-4 mr-1" />
                <span>Call</span>
              </a>
            )}
            {temple.contact.website && (
              <a
                href={temple.contact.website}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-sm text-gray-600 hover:text-primary-600 transition-colors"
              >
                <GlobeAltIcon className="w-4 h-4 mr-1" />
                <span>Website</span>
              </a>
            )}
          </div>
        )}

        {/* Festivals */}
        {temple.festivals.length > 0 && (
          <div className="pt-2">
            <p className="text-xs font-medium text-gray-700 mb-1">Major Festivals:</p>
            <div className="flex flex-wrap gap-1">
              {temple.festivals.slice(0, 3).map((festival, index) => (
                <span
                  key={index}
                  className="text-xs bg-secondary-50 text-secondary-700 px-2 py-0.5 rounded-full"
                >
                  {festival}
                </span>
              ))}
              {temple.festivals.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{temple.festivals.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TempleCard;
