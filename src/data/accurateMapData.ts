// Auto-generated accurate map data extracted from professional SVG
// Generated on: 2025-05-30T13:10:45.718Z
// Source: /Users/<USER>/development/all-india-temple-website/src/assets/maps/in.svg
// Total states: 0

export interface AccurateStateGeography {
  id: string;
  svgId: string;
  name: string;
  path: string;
  center: { x: number; y: number };
  boundingBox: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
    width: number;
    height: number;
  };
  area: number;
}

export interface AccurateMapData {
  viewBox: string;
  states: Record<string, AccurateStateGeography>;
  metadata: {
    extractedAt: string;
    totalStates: number;
    sourceFile: string;
  };
}

export const accurateMapData: AccurateMapData = {
  "viewBox": "0 0 800 600",
  "states": {},
  "metadata": {
    "extractedAt": "2025-05-30T13:10:45.709Z",
    "totalStates": 0,
    "sourceFile": "/Users/<USER>/development/all-india-temple-website/src/assets/maps/in.svg"
  }
};

export default accurateMapData;
