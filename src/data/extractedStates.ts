// Auto-generated from extract_states.py
// Indian states and union territories mapping from SVG map data

export interface StateData {
  id: string;
  name: string;
}

export const INDIAN_STATES: Record<string, string> = {
  "INAN": "Andaman and Nicobar",
  "INAP": "Andhra Pradesh",
  "INAR": "Arunachal Pradesh",
  "INAS": "Assam",
  "INBR": "Bihar",
  "INCH": "Chandigarh",
  "INCT": "Chhattisgarh",
  "INDH": "Dādra and Nagar Haveli and Damān and Diu",
  "INDL": "Delhi",
  "INGA": "Goa",
  "INGJ": "Gujarat",
  "INHP": "Himachal Pradesh",
  "INHR": "Haryana",
  "INJH": "Jharkhand",
  "INJK": "Jammu and Kashmir",
  "INKA": "Karnataka",
  "INKL": "Kerala",
  "INLA": "Ladakh",
  "INLD": "Lakshadweep",
  "INMH": "Maharashtra",
  "INML": "Meghalaya",
  "INMN": "Manipur",
  "INMP": "Madhya Pradesh",
  "INMZ": "Mizoram",
  "INNL": "Nagaland",
  "INOR": "Orissa",
  "INPB": "Punjab",
  "INPY": "Puducherry",
  "INRJ": "Rajasthan",
  "INSK": "Sikkim",
  "INTG": "Telangana",
  "INTN": "Tamil Nadu",
  "INTR": "Tripura",
  "INUP": "Uttar Pradesh",
  "INUT": "Uttaranchal",
  "INWB": "West Bengal"
};

export const STATES_BY_NAME: Record<string, string> = {
  "Andaman and Nicobar": "INAN",
  "Andhra Pradesh": "INAP",
  "Arunachal Pradesh": "INAR",
  "Assam": "INAS",
  "Bihar": "INBR",
  "Chandigarh": "INCH",
  "Chhattisgarh": "INCT",
  "Dādra and Nagar Haveli and Damān and Diu": "INDH",
  "Delhi": "INDL",
  "Goa": "INGA",
  "Gujarat": "INGJ",
  "Himachal Pradesh": "INHP",
  "Haryana": "INHR",
  "Jharkhand": "INJH",
  "Jammu and Kashmir": "INJK",
  "Karnataka": "INKA",
  "Kerala": "INKL",
  "Ladakh": "INLA",
  "Lakshadweep": "INLD",
  "Maharashtra": "INMH",
  "Meghalaya": "INML",
  "Manipur": "INMN",
  "Madhya Pradesh": "INMP",
  "Mizoram": "INMZ",
  "Nagaland": "INNL",
  "Orissa": "INOR",
  "Punjab": "INPB",
  "Puducherry": "INPY",
  "Rajasthan": "INRJ",
  "Sikkim": "INSK",
  "Telangana": "INTG",
  "Tamil Nadu": "INTN",
  "Tripura": "INTR",
  "Uttar Pradesh": "INUP",
  "Uttaranchal": "INUT",
  "West Bengal": "INWB"
};

export const STATE_IDS = Object.keys(INDIAN_STATES).sort();
export const STATE_NAMES = Object.values(INDIAN_STATES).sort();
export const STATE_COUNT = Object.keys(INDIAN_STATES).length;

// Helper functions
export function getStateName(stateId: string): string | undefined {
  return INDIAN_STATES[stateId];
}

export function getStateId(stateName: string): string | undefined {
  return STATES_BY_NAME[stateName];
}

export function getAllStates(): StateData[] {
  return Object.entries(INDIAN_STATES).map(([id, name]) => ({ id, name }));
}

export function isValidStateId(stateId: string): boolean {
  return stateId in INDIAN_STATES;
}

export function isValidStateName(stateName: string): boolean {
  return stateName in STATES_BY_NAME;
}
