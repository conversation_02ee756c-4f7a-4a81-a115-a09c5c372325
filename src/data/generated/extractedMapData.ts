// Generated on 2025-05-30T13:16:42.987Z
// SVG Map Data Extraction Results

export interface ExtractedStateData {
  id: string;
  code: string;
  name: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  path: string;
  bounds: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
  center: {
    x: number;
    y: number;
  };
  coordinateCount: number;
}

export interface ExtractedMapData {
  metadata: {
    extractedAt: string;
    source: string;
    totalStates: number;
    dataQuality: string;
    coordinateSystem: string;
  };
  viewBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  totalBounds: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
  states: ExtractedStateData[];
}

// Extracted map data
export const extractedMapData: ExtractedMapData = {
  "metadata": {
    "extractedAt": "2025-05-30T13:16:42.985Z",
    "source": "in.svg",
    "totalStates": 0,
    "dataQuality": "high",
    "coordinateSystem": "SVG with lat/lng approximation"
  },
  "viewBox": {
    "x": 0,
    "y": 0,
    "width": 800,
    "height": 600
  },
  "totalBounds": {
    "minX": null,
    "minY": null,
    "maxX": null,
    "maxY": null
  },
  "states": []
};

// Quick access to state coordinates
export const stateCoordinates = extractedMapData.states.reduce((acc, state) => {
  acc[state.code] = state.coordinates;
  return acc;
}, {} as Record<string, { lat: number; lng: number }>);

// Quick access to SVG paths
export const statePaths = extractedMapData.states.reduce((acc, state) => {
  acc[state.code] = state.path;
  return acc;
}, {} as Record<string, string>);
