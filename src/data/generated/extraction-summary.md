# SVG Map Data Extraction Summary

**Generated:** 2025-05-30T13:16:42.985Z
**Source:** in.svg
**Total States:** 0
**Data Quality:** high

## Statistics

- **Average coordinates per state:** NaN
- **Total coordinates extracted:** 0
- **ViewBox dimensions:** 800 × 600
- **Geographic bounds:** 
  - X: Infinity to -Infinity
  - Y: Infinity to -Infinity

## Extracted States



## Files Generated

1. `extractedMapData.json` - Complete extracted data
2. `stateCoordinates.json` - Coordinates only
3. `svgMapPaths.json` - SVG paths for rendering
4. `extractedMapData.ts` - TypeScript interfaces and data
5. `extraction-summary.md` - This summary report

## Usage

Import the generated data in your React components:

```typescript
import { extractedMapData, stateCoordinates, statePaths } from './data/generated/extractedMapData';
```

## Data Quality Notes

- Coordinates are approximated from SVG space to lat/lng
- SVG paths maintain original precision for accurate rendering
- Geographic bounds are calibrated to India's actual coordinates
- All 0 states and union territories included
