// Simplified SVG path data for demonstration
// In a real application, you would use actual geographic SVG paths

export const indiaMapData = {
  viewBox: "0 0 800 600",
  states: {
    rajasthan: {
      path: "M 100,120 L 200,100 L 220,180 L 180,220 L 120,200 L 80,160 Z",
      center: { x: 150, y: 160 }
    },
    "tamil-nadu": {
      path: "M 200,300 L 320,280 L 340,380 L 280,420 L 220,400 L 180,360 Z",
      center: { x: 260, y: 350 }
    },
    karnataka: {
      path: "M 150,250 L 250,230 L 270,320 L 220,360 L 170,340 L 130,300 Z",
      center: { x: 200, y: 295 }
    },
    kerala: {
      path: "M 120,320 L 180,300 L 200,380 L 160,420 L 100,400 L 80,360 Z",
      center: { x: 140, y: 360 }
    },
    "andhra-pradesh": {
      path: "M 250,220 L 350,200 L 370,300 L 320,340 L 270,320 L 230,280 Z",
      center: { x: 300, y: 260 }
    },
    odisha: {
      path: "M 300,150 L 380,130 L 400,200 L 360,240 L 320,220 L 280,180 Z",
      center: { x: 340, y: 185 }
    },
    "west-bengal": {
      path: "M 320,120 L 400,100 L 420,170 L 380,210 L 340,190 L 300,150 Z",
      center: { x: 360, y: 155 }
    },
    gujarat: {
      path: "M 50,150 L 150,130 L 170,200 L 130,240 L 70,220 L 30,180 Z",
      center: { x: 100, y: 185 }
    },
    maharashtra: {
      path: "M 150,200 L 250,180 L 270,250 L 220,290 L 170,270 L 130,230 Z",
      center: { x: 200, y: 235 }
    },
    "madhya-pradesh": {
      path: "M 200,150 L 300,130 L 320,200 L 280,240 L 230,220 L 180,180 Z",
      center: { x: 250, y: 185 }
    },
    "uttar-pradesh": {
      path: "M 250,100 L 350,80 L 370,150 L 330,190 L 280,170 L 230,130 Z",
      center: { x: 300, y: 135 }
    },
    bihar: {
      path: "M 300,100 L 380,80 L 400,130 L 360,170 L 320,150 L 280,110 Z",
      center: { x: 340, y: 125 }
    },
    jharkhand: {
      path: "M 320,130 L 380,110 L 400,160 L 360,200 L 320,180 L 300,140 Z",
      center: { x: 350, y: 155 }
    },
    "himachal-pradesh": {
      path: "M 200,50 L 280,30 L 300,80 L 260,120 L 220,100 L 180,60 Z",
      center: { x: 240, y: 75 }
    },
    punjab: {
      path: "M 150,50 L 230,30 L 250,80 L 210,120 L 170,100 L 130,60 Z",
      center: { x: 190, y: 75 }
    },
    haryana: {
      path: "M 200,80 L 280,60 L 300,110 L 260,150 L 220,130 L 180,90 Z",
      center: { x: 240, y: 105 }
    }
  },
  cities: {
    rajasthan: {
      "mount-abu": {
        path: "M 140,180 L 160,170 L 150,200 L 130,190 Z",
        center: { x: 145, y: 185 }
      },
      bikaner: {
        path: "M 110,140 L 130,130 L 120,160 L 100,150 Z",
        center: { x: 115, y: 145 }
      },
      jaipur: {
        path: "M 170,160 L 190,150 L 180,180 L 160,170 Z",
        center: { x: 175, y: 165 }
      }
    },
    "tamil-nadu": {
      thanjavur: {
        path: "M 240,340 L 260,330 L 250,360 L 230,350 Z",
        center: { x: 245, y: 345 }
      },
      madurai: {
        path: "M 210,370 L 230,360 L 220,390 L 200,380 Z",
        center: { x: 215, y: 375 }
      },
      chennai: {
        path: "M 270,310 L 290,300 L 280,330 L 260,320 Z",
        center: { x: 275, y: 315 }
      }
    },
    karnataka: {
      hampi: {
        path: "M 190,290 L 210,280 L 200,310 L 180,300 Z",
        center: { x: 195, y: 295 }
      },
      mysore: {
        path: "M 170,310 L 190,300 L 180,330 L 160,320 Z",
        center: { x: 175, y: 315 }
      },
      bangalore: {
        path: "M 200,270 L 220,260 L 210,290 L 190,280 Z",
        center: { x: 205, y: 275 }
      }
    }
  }
};

export const getStatePathData = (stateId: string): string => {
  return indiaMapData.states[stateId as keyof typeof indiaMapData.states]?.path || "";
};

export const getCityPathData = (stateId: string, cityId: string): string => {
  const stateCities = indiaMapData.cities[stateId as keyof typeof indiaMapData.cities];
  if (!stateCities) return "";
  
  return (stateCities as any)[cityId]?.path || "";
};

export const getStateCenter = (stateId: string) => {
  return indiaMapData.states[stateId as keyof typeof indiaMapData.states]?.center || { x: 0, y: 0 };
};

export const getCityCenter = (stateId: string, cityId: string) => {
  const stateCities = indiaMapData.cities[stateId as keyof typeof indiaMapData.cities];
  if (!stateCities) return { x: 0, y: 0 };
  
  return (stateCities as any)[cityId]?.center || { x: 0, y: 0 };
};
