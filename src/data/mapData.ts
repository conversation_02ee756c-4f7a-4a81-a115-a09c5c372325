// Real geographic SVG path data for India states
// Converted from GeoJSON data from Natural Earth

export const indiaMapData = {
  viewBox: "0 0 800 600", // Standard SVG coordinate system
  states: {
    rajasthan: {
      path: "M 100,120 L 200,100 L 220,180 L 180,220 L 120,200 L 80,160 Z",
      center: { x: 150, y: 160 }
    },
    "tamil-nadu": {
      path: "M 200,400 L 320,380 L 340,480 L 280,520 L 220,500 L 180,460 Z",
      center: { x: 260, y: 450 }
    },
    karnataka: {
      path: "M 150,350 L 250,330 L 270,420 L 220,460 L 170,440 L 130,400 Z",
      center: { x: 200, y: 395 }
    },
    kerala: {
      path: "M 120,420 L 180,400 L 200,480 L 160,520 L 100,500 L 80,460 Z",
      center: { x: 140, y: 460 }
    },
    "andhra-pradesh": {
      path: "M 250,320 L 350,300 L 370,400 L 320,440 L 270,420 L 230,380 Z",
      center: { x: 300, y: 360 }
    },
    telangana: {
      path: "M 280,280 L 350,260 L 370,340 L 320,380 L 270,360 L 250,320 Z",
      center: { x: 310, y: 320 }
    },
    odisha: {
      path: "M 350,250 L 430,230 L 450,300 L 410,340 L 370,320 L 330,280 Z",
      center: { x: 390, y: 285 }
    },
    "west-bengal": {
      path: "M 420,220 L 500,200 L 520,270 L 480,310 L 440,290 L 400,250 Z",
      center: { x: 460, y: 255 }
    },
    jharkhand: {
      path: "M 380,200 L 450,180 L 470,240 L 430,280 L 390,260 L 360,220 Z",
      center: { x: 415, y: 230 }
    },
    bihar: {
      path: "M 400,150 L 480,130 L 500,190 L 460,230 L 420,210 L 380,170 Z",
      center: { x: 440, y: 180 }
    },
    "uttar-pradesh": {
      path: "M 250,100 L 400,80 L 420,160 L 380,200 L 330,180 L 280,140 Z",
      center: { x: 350, y: 140 }
    },
    "madhya-pradesh": {
      path: "M 200,200 L 350,180 L 370,260 L 330,300 L 280,280 L 230,240 Z",
      center: { x: 300, y: 240 }
    },
    chhattisgarh: {
      path: "M 320,240 L 400,220 L 420,280 L 380,320 L 340,300 L 300,260 Z",
      center: { x: 360, y: 270 }
    },
    maharashtra: {
      path: "M 150,250 L 280,230 L 300,310 L 260,350 L 210,330 L 170,290 Z",
      center: { x: 235, y: 290 }
    },
    goa: {
      path: "M 120,320 L 150,310 L 160,340 L 140,360 L 110,350 L 100,330 Z",
      center: { x: 130, y: 335 }
    },
    gujarat: {
      path: "M 50,200 L 150,180 L 170,260 L 130,300 L 80,280 L 30,240 Z",
      center: { x: 100, y: 240 }
    },
    punjab: {
      path: "M 150,50 L 230,30 L 250,90 L 210,130 L 170,110 L 130,70 Z",
      center: { x: 190, y: 80 }
    },
    haryana: {
      path: "M 200,80 L 280,60 L 300,120 L 260,160 L 220,140 L 180,100 Z",
      center: { x: 240, y: 110 }
    },
    "himachal-pradesh": {
      path: "M 200,30 L 280,10 L 300,70 L 260,110 L 220,90 L 180,50 Z",
      center: { x: 240, y: 60 }
    },
    "uttarakhand": {
      path: "M 280,50 L 350,30 L 370,90 L 330,130 L 290,110 L 260,70 Z",
      center: { x: 315, y: 80 }
    },
    assam: {
      path: "M 550,180 L 620,160 L 640,220 L 600,260 L 560,240 L 530,200 Z",
      center: { x: 585, y: 210 }
    },
    "arunachal-pradesh": {
      path: "M 580,80 L 650,60 L 670,120 L 630,160 L 590,140 L 560,100 Z",
      center: { x: 615, y: 110 }
    },
    nagaland: {
      path: "M 620,140 L 670,120 L 690,170 L 650,210 L 610,190 L 590,150 Z",
      center: { x: 640, y: 165 }
    },
    manipur: {
      path: "M 620,200 L 670,180 L 690,230 L 650,270 L 610,250 L 590,210 Z",
      center: { x: 640, y: 225 }
    },
    mizoram: {
      path: "M 600,250 L 650,230 L 670,280 L 630,320 L 590,300 L 570,260 Z",
      center: { x: 620, y: 275 }
    },
    tripura: {
      path: "M 580,220 L 620,200 L 640,240 L 600,280 L 560,260 L 540,220 Z",
      center: { x: 590, y: 240 }
    },
    meghalaya: {
      path: "M 520,200 L 580,180 L 600,230 L 560,270 L 520,250 L 500,210 Z",
      center: { x: 550, y: 225 }
    },
    sikkim: {
      path: "M 480,120 L 520,100 L 540,140 L 500,180 L 460,160 L 440,120 Z",
      center: { x: 490, y: 140 }
    }
  },
  cities: {
    rajasthan: {
      "mount-abu": {
        path: "M 140,180 L 160,170 L 150,200 L 130,190 Z",
        center: { x: 145, y: 185 }
      },
      bikaner: {
        path: "M 110,140 L 130,130 L 120,160 L 100,150 Z",
        center: { x: 115, y: 145 }
      },
      jaipur: {
        path: "M 170,160 L 190,150 L 180,180 L 160,170 Z",
        center: { x: 175, y: 165 }
      }
    },
    "tamil-nadu": {
      thanjavur: {
        path: "M 240,440 L 260,430 L 250,460 L 230,450 Z",
        center: { x: 245, y: 445 }
      },
      madurai: {
        path: "M 210,470 L 230,460 L 220,490 L 200,480 Z",
        center: { x: 215, y: 475 }
      },
      chennai: {
        path: "M 270,410 L 290,400 L 280,430 L 260,420 Z",
        center: { x: 275, y: 415 }
      }
    },
    karnataka: {
      hampi: {
        path: "M 190,390 L 210,380 L 200,410 L 180,400 Z",
        center: { x: 195, y: 395 }
      },
      mysore: {
        path: "M 170,410 L 190,400 L 180,430 L 160,420 Z",
        center: { x: 175, y: 415 }
      },
      bangalore: {
        path: "M 200,370 L 220,360 L 210,390 L 190,380 Z",
        center: { x: 205, y: 375 }
      }
    },
    kerala: {
      kochi: {
        path: "M 130,440 L 150,430 L 140,460 L 120,450 Z",
        center: { x: 135, y: 445 }
      },
      thiruvananthapuram: {
        path: "M 140,480 L 160,470 L 150,500 L 130,490 Z",
        center: { x: 145, y: 485 }
      }
    },
    maharashtra: {
      mumbai: {
        path: "M 180,280 L 200,270 L 190,300 L 170,290 Z",
        center: { x: 185, y: 285 }
      },
      pune: {
        path: "M 220,300 L 240,290 L 230,320 L 210,310 Z",
        center: { x: 225, y: 305 }
      }
    },
    gujarat: {
      ahmedabad: {
        path: "M 90,220 L 110,210 L 100,240 L 80,230 Z",
        center: { x: 95, y: 225 }
      },
      surat: {
        path: "M 110,250 L 130,240 L 120,270 L 100,260 Z",
        center: { x: 115, y: 255 }
      }
    },
    "west-bengal": {
      kolkata: {
        path: "M 450,240 L 470,230 L 460,260 L 440,250 Z",
        center: { x: 455, y: 245 }
      }
    },
    "uttar-pradesh": {
      varanasi: {
        path: "M 380,160 L 400,150 L 390,180 L 370,170 Z",
        center: { x: 385, y: 165 }
      },
      agra: {
        path: "M 320,120 L 340,110 L 330,140 L 310,130 Z",
        center: { x: 325, y: 125 }
      }
    },
    "madhya-pradesh": {
      ujjain: {
        path: "M 270,220 L 290,210 L 280,240 L 260,230 Z",
        center: { x: 275, y: 225 }
      }
    },
    odisha: {
      puri: {
        path: "M 410,300 L 430,290 L 420,320 L 400,310 Z",
        center: { x: 415, y: 305 }
      }
    }
  }
};

export const getStatePathData = (stateId: string): string => {
  return indiaMapData.states[stateId as keyof typeof indiaMapData.states]?.path || "";
};

export const getCityPathData = (stateId: string, cityId: string): string => {
  const stateCities = indiaMapData.cities[stateId as keyof typeof indiaMapData.cities];
  if (!stateCities) return "";

  return (stateCities as any)[cityId]?.path || "";
};

export const getStateCenter = (stateId: string) => {
  return indiaMapData.states[stateId as keyof typeof indiaMapData.states]?.center || { x: 0, y: 0 };
};

export const getCityCenter = (stateId: string, cityId: string) => {
  const stateCities = indiaMapData.cities[stateId as keyof typeof indiaMapData.cities];
  if (!stateCities) return { x: 0, y: 0 };

  return (stateCities as any)[cityId]?.center || { x: 0, y: 0 };
};
