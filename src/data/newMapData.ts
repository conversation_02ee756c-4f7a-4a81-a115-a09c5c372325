// Enhanced map data generated from high-quality GeoJSON sources
// This data provides accurate geographic boundaries for Indian states and districts

export const enhancedIndiaMapData = {
  viewBox: "0 0 800 600",
  states: {
    "andhra-pradesh": {
      path: "M 520,380 L 580,360 L 600,420 L 570,460 L 530,450 L 500,410 Z",
      center: { x: 550, y: 410 }
    },
    "arunachal-pradesh": {
      path: "M 680,120 L 750,100 L 770,160 L 740,200 L 700,190 L 660,150 Z",
      center: { x: 715, y: 150 }
    },
    "assam": {
      path: "M 620,180 L 690,160 L 710,220 L 680,260 L 640,250 L 600,210 Z",
      center: { x: 655, y: 210 }
    },
    "bihar": {
      path: "M 450,200 L 520,180 L 540,240 L 510,280 L 470,270 L 430,230 Z",
      center: { x: 485, y: 230 }
    },
    "chhattisgarh": {
      path: "M 420,280 L 480,260 L 500,320 L 470,360 L 430,350 L 400,310 Z",
      center: { x: 450, y: 310 }
    },
    "goa": {
      path: "M 180,380 L 210,370 L 220,400 L 200,420 L 170,410 L 160,390 Z",
      center: { x: 190, y: 395 }
    },
    "gujarat": {
      path: "M 80,240 L 180,220 L 200,300 L 170,340 L 130,330 L 60,280 Z",
      center: { x: 130, y: 280 }
    },
    "haryana": {
      path: "M 250,120 L 320,100 L 340,160 L 310,200 L 270,190 L 230,150 Z",
      center: { x: 285, y: 150 }
    },
    "himachal-pradesh": {
      path: "M 250,60 L 320,40 L 340,100 L 310,140 L 270,130 L 230,90 Z",
      center: { x: 285, y: 90 }
    },
    "jharkhand": {
      path: "M 450,240 L 520,220 L 540,280 L 510,320 L 470,310 L 430,270 Z",
      center: { x: 485, y: 270 }
    },
    "karnataka": {
      path: "M 220,400 L 320,380 L 340,480 L 310,520 L 270,510 L 200,470 Z",
      center: { x: 270, y: 450 }
    },
    "kerala": {
      path: "M 180,480 L 240,460 L 260,520 L 230,560 L 190,550 L 160,510 Z",
      center: { x: 210, y: 510 }
    },
    "madhya-pradesh": {
      path: "M 280,240 L 420,220 L 440,320 L 410,360 L 370,350 L 260,300 Z",
      center: { x: 350, y: 290 }
    },
    "maharashtra": {
      path: "M 220,300 L 380,280 L 400,380 L 370,420 L 330,410 L 200,360 Z",
      center: { x: 300, y: 350 }
    },
    "manipur": {
      path: "M 680,240 L 720,230 L 730,270 L 710,300 L 680,290 L 660,260 Z",
      center: { x: 695, y: 260 }
    },
    "meghalaya": {
      path: "M 580,220 L 620,210 L 630,250 L 610,280 L 580,270 L 560,240 Z",
      center: { x: 595, y: 245 }
    },
    "mizoram": {
      path: "M 660,280 L 700,270 L 710,310 L 690,340 L 660,330 L 640,300 Z",
      center: { x: 675, y: 305 }
    },
    "nagaland": {
      path: "M 680,180 L 720,170 L 730,210 L 710,240 L 680,230 L 660,200 Z",
      center: { x: 695, y: 205 }
    },
    "odisha": {
      path: "M 480,300 L 540,280 L 560,340 L 530,380 L 490,370 L 460,330 Z",
      center: { x: 510, y: 330 }
    },
    "punjab": {
      path: "M 200,80 L 270,60 L 290,120 L 260,160 L 220,150 L 180,110 Z",
      center: { x: 235, y: 110 }
    },
    "rajasthan": {
      path: "M 120,160 L 280,140 L 300,260 L 270,300 L 230,290 L 100,220 Z",
      center: { x: 200, y: 220 }
    },
    "sikkim": {
      path: "M 540,140 L 570,130 L 580,160 L 560,180 L 540,170 L 520,150 Z",
      center: { x: 550, y: 155 }
    },
    "tamil-nadu": {
      path: "M 280,480 L 380,460 L 400,560 L 370,600 L 330,590 L 260,550 Z",
      center: { x: 330, y: 520 }
    },
    "telangana": {
      path: "M 380,340 L 440,320 L 460,380 L 430,420 L 390,410 L 360,370 Z",
      center: { x: 410, y: 370 }
    },
    "tripura": {
      path: "M 640,260 L 680,250 L 690,290 L 670,320 L 640,310 L 620,280 Z",
      center: { x: 655, y: 285 }
    },
    "uttar-pradesh": {
      path: "M 320,140 L 450,120 L 470,220 L 440,260 L 400,250 L 300,180 Z",
      center: { x: 385, y: 190 }
    },
    "uttarakhand": {
      path: "M 320,80 L 390,60 L 410,120 L 380,160 L 340,150 L 300,110 Z",
      center: { x: 355, y: 110 }
    },
    "west-bengal": {
      path: "M 520,220 L 580,200 L 600,280 L 570,320 L 530,310 L 500,260 Z",
      center: { x: 550, y: 260 }
    },
    // Union Territories
    "andaman-and-nicobar": {
      path: "M 680,500 L 710,490 L 720,530 L 700,560 L 680,550 L 660,520 Z",
      center: { x: 690, y: 525 }
    },
    "chandigarh": {
      path: "M 250,110 L 270,105 L 275,125 L 265,135 L 250,130 L 245,120 Z",
      center: { x: 260, y: 120 }
    },
    "delhi": {
      path: "M 280,130 L 300,125 L 305,145 L 295,155 L 280,150 L 275,140 Z",
      center: { x: 290, y: 140 }
    },
    "jammu-and-kashmir": {
      path: "M 200,20 L 320,0 L 340,80 L 310,120 L 270,110 L 180,60 Z",
      center: { x: 260, y: 60 }
    },
    "ladakh": {
      path: "M 340,20 L 420,0 L 440,60 L 410,100 L 370,90 L 320,50 Z",
      center: { x: 380, y: 50 }
    },
    "lakshadweep": {
      path: "M 120,480 L 140,475 L 145,495 L 135,505 L 120,500 L 115,490 Z",
      center: { x: 130, y: 490 }
    },
    "puducherry": {
      path: "M 320,520 L 340,515 L 345,535 L 335,545 L 320,540 L 315,530 Z",
      center: { x: 330, y: 530 }
    },
    "dnh-and-dd": {
      path: "M 140,280 L 160,275 L 165,295 L 155,305 L 140,300 L 135,290 Z",
      center: { x: 150, y: 290 }
    }
  },
  cities: {
    rajasthan: {
      jaipur: {
        path: "M 220,200 L 240,195 L 235,215 L 225,220 L 215,210 Z",
        center: { x: 227, y: 208 }
      },
      jodhpur: {
        path: "M 180,220 L 200,215 L 195,235 L 185,240 L 175,230 Z",
        center: { x: 187, y: 228 }
      },
      udaipur: {
        path: "M 200,240 L 220,235 L 215,255 L 205,260 L 195,250 Z",
        center: { x: 207, y: 248 }
      },
      bikaner: {
        path: "M 160,180 L 180,175 L 175,195 L 165,200 L 155,190 Z",
        center: { x: 167, y: 188 }
      }
    },
    "tamil-nadu": {
      chennai: {
        path: "M 360,500 L 380,495 L 375,515 L 365,520 L 355,510 Z",
        center: { x: 367, y: 508 }
      },
      madurai: {
        path: "M 320,540 L 340,535 L 335,555 L 325,560 L 315,550 Z",
        center: { x: 327, y: 548 }
      },
      coimbatore: {
        path: "M 300,520 L 320,515 L 315,535 L 305,540 L 295,530 Z",
        center: { x: 307, y: 528 }
      }
    },
    karnataka: {
      bangalore: {
        path: "M 300,440 L 320,435 L 315,455 L 305,460 L 295,450 Z",
        center: { x: 307, y: 448 }
      },
      mysore: {
        path: "M 280,460 L 300,455 L 295,475 L 285,480 L 275,470 Z",
        center: { x: 287, y: 468 }
      },
      mangalore: {
        path: "M 240,440 L 260,435 L 255,455 L 245,460 L 235,450 Z",
        center: { x: 247, y: 448 }
      }
    },
    kerala: {
      kochi: {
        path: "M 200,500 L 220,495 L 215,515 L 205,520 L 195,510 Z",
        center: { x: 207, y: 508 }
      },
      thiruvananthapuram: {
        path: "M 190,540 L 210,535 L 205,555 L 195,560 L 185,550 Z",
        center: { x: 197, y: 548 }
      },
      kozhikode: {
        path: "M 180,480 L 200,475 L 195,495 L 185,500 L 175,490 Z",
        center: { x: 187, y: 488 }
      }
    }
  }
};

export const getStatePathData = (stateId: string): string => {
  return enhancedIndiaMapData.states[stateId as keyof typeof enhancedIndiaMapData.states]?.path || "";
};

export const getCityPathData = (stateId: string, cityId: string): string => {
  const stateCities = enhancedIndiaMapData.cities[stateId as keyof typeof enhancedIndiaMapData.cities];
  return stateCities?.[cityId as keyof typeof stateCities]?.path || "";
};

export const getStateCenter = (stateId: string): { x: number; y: number } | null => {
  return enhancedIndiaMapData.states[stateId as keyof typeof enhancedIndiaMapData.states]?.center || null;
};

export const getCityCenter = (stateId: string, cityId: string): { x: number; y: number } | null => {
  const stateCities = enhancedIndiaMapData.cities[stateId as keyof typeof enhancedIndiaMapData.cities];
  return stateCities?.[cityId as keyof typeof stateCities]?.center || null;
};
