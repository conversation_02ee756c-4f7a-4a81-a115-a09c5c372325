import { State, City } from '../types';
import { indiaMapData } from './mapData';

export const cities: City[] = [
  // Rajasthan Cities
  {
    id: 'raj-city-001',
    name: 'Mount Abu',
    stateId: 'rajasthan',
    description: 'The only hill station in Rajasthan, famous for Dilwara Jain temples.',
    totalTemples: 8,
    famousTemples: ['Dilwara Temples', 'Achalgarh Fort Temple'],
    svgPath: 'M 140,180 L 160,170 L 150,200 L 130,190 Z',
    coordinates: { lat: 24.5926, lng: 72.7156 }
  },
  {
    id: 'raj-city-002',
    name: '<PERSON><PERSON><PERSON>',
    stateId: 'rajasthan',
    description: 'Known for its forts, palaces, and the famous Karni Mata Temple.',
    totalTemples: 12,
    famousTemples: ['Karni Mata Temple', 'Laxmi Nath Temple'],
    svgPath: 'M 120,150 L 150,140 L 140,170 L 110,160 Z',
    coordinates: { lat: 27.9881, lng: 73.3119 }
  },
  {
    id: 'raj-city-003',
    name: 'Jaipur',
    stateId: 'rajasthan',
    description: 'The Pink City, capital of Rajasthan with numerous temples and palaces.',
    totalTemples: 25,
    famousTemples: ['Govind Dev Ji Temple', 'Birla Mandir'],
    svgPath: 'M 180,180 L 210,170 L 200,200 L 170,190 Z',
    coordinates: { lat: 26.9124, lng: 75.7873 }
  },

  // Tamil Nadu Cities
  {
    id: 'tn-city-001',
    name: 'Thanjavur',
    stateId: 'tamil-nadu',
    description: 'Ancient city known for the magnificent Brihadeeswarar Temple.',
    totalTemples: 18,
    famousTemples: ['Brihadeeswarar Temple', 'Schwartz Church'],
    svgPath: 'M 250,350 L 280,340 L 270,370 L 240,360 Z',
    coordinates: { lat: 10.7825, lng: 79.1317 }
  },
  {
    id: 'tn-city-002',
    name: 'Madurai',
    stateId: 'tamil-nadu',
    description: 'Temple city famous for the Meenakshi Amman Temple.',
    totalTemples: 22,
    famousTemples: ['Meenakshi Amman Temple', 'Koodal Azhagar Temple'],
    svgPath: 'M 220,380 L 250,370 L 240,400 L 210,390 Z',
    coordinates: { lat: 9.9252, lng: 78.1198 }
  },
  {
    id: 'tn-city-003',
    name: 'Chennai',
    stateId: 'tamil-nadu',
    description: 'Capital city with beautiful temples and beaches.',
    totalTemples: 35,
    famousTemples: ['Kapaleeshwarar Temple', 'Parthasarathy Temple'],
    svgPath: 'M 280,320 L 310,310 L 300,340 L 270,330 Z',
    coordinates: { lat: 13.0827, lng: 80.2707 }
  },

  // Karnataka Cities
  {
    id: 'kar-city-001',
    name: 'Hampi',
    stateId: 'karnataka',
    description: 'UNESCO World Heritage Site with ancient Vijayanagara temples.',
    totalTemples: 15,
    famousTemples: ['Virupaksha Temple', 'Vittala Temple'],
    svgPath: 'M 200,300 L 230,290 L 220,320 L 190,310 Z',
    coordinates: { lat: 15.3350, lng: 76.4600 }
  },
  {
    id: 'kar-city-002',
    name: 'Mysore',
    stateId: 'karnataka',
    description: 'City of palaces with beautiful temples and gardens.',
    totalTemples: 20,
    famousTemples: ['Chamundeshwari Temple', 'St. Philomena\'s Church'],
    svgPath: 'M 180,320 L 210,310 L 200,340 L 170,330 Z',
    coordinates: { lat: 12.2958, lng: 76.6394 }
  },
  {
    id: 'kar-city-003',
    name: 'Bangalore',
    stateId: 'karnataka',
    description: 'Silicon Valley of India with modern and ancient temples.',
    totalTemples: 30,
    famousTemples: ['ISKCON Temple', 'Bull Temple'],
    svgPath: 'M 210,280 L 240,270 L 230,300 L 200,290 Z',
    coordinates: { lat: 12.9716, lng: 77.5946 }
  }
];

export const states: State[] = [
  {
    id: 'rajasthan',
    name: 'Rajasthan',
    code: 'RJ',
    capital: 'Jaipur',
    description: 'The Land of Kings, known for its magnificent forts, palaces, and temples.',
    totalTemples: 45,
    famousTemples: ['Dilwara Temples', 'Karni Mata Temple', 'Govind Dev Ji Temple'],
    svgPath: 'M 100,120 L 200,100 L 220,180 L 180,220 L 120,200 L 80,160 Z',
    cities: cities.filter(city => city.stateId === 'rajasthan')
  },
  {
    id: 'tamil-nadu',
    name: 'Tamil Nadu',
    code: 'TN',
    capital: 'Chennai',
    description: 'Land of Temples with rich Dravidian architecture and culture.',
    totalTemples: 75,
    famousTemples: ['Brihadeeswarar Temple', 'Meenakshi Amman Temple', 'Kapaleeshwarar Temple'],
    svgPath: 'M 200,300 L 320,280 L 340,380 L 280,420 L 220,400 L 180,360 Z',
    cities: cities.filter(city => city.stateId === 'tamil-nadu')
  },
  {
    id: 'karnataka',
    name: 'Karnataka',
    code: 'KA',
    capital: 'Bangalore',
    description: 'Garden State with diverse temples from different dynasties.',
    totalTemples: 65,
    famousTemples: ['Virupaksha Temple', 'Chamundeshwari Temple', 'ISKCON Temple'],
    svgPath: 'M 150,250 L 250,230 L 270,320 L 220,360 L 170,340 L 130,300 Z',
    cities: cities.filter(city => city.stateId === 'karnataka')
  },
  {
    id: 'kerala',
    name: 'Kerala',
    code: 'KL',
    capital: 'Thiruvananthapuram',
    description: 'God\'s Own Country with unique temple architecture and traditions.',
    totalTemples: 40,
    famousTemples: ['Padmanabhaswamy Temple', 'Guruvayur Temple', 'Sabarimala Temple'],
    svgPath: 'M 120,320 L 180,300 L 200,380 L 160,420 L 100,400 L 80,360 Z',
    cities: []
  },
  {
    id: 'andhra-pradesh',
    name: 'Andhra Pradesh',
    code: 'AP',
    capital: 'Amaravati',
    description: 'State with ancient temples and rich spiritual heritage.',
    totalTemples: 55,
    famousTemples: ['Tirumala Temple', 'Srisailam Temple', 'Simhachalam Temple'],
    svgPath: 'M 250,220 L 350,200 L 370,300 L 320,340 L 270,320 L 230,280 Z',
    cities: []
  },
  {
    id: 'odisha',
    name: 'Odisha',
    code: 'OR',
    capital: 'Bhubaneswar',
    description: 'Land of Jagannath with magnificent temple architecture.',
    totalTemples: 50,
    famousTemples: ['Jagannath Temple', 'Lingaraj Temple', 'Konark Sun Temple'],
    svgPath: 'M 300,150 L 380,130 L 400,200 L 360,240 L 320,220 L 280,180 Z',
    cities: []
  },
  {
    id: 'west-bengal',
    name: 'West Bengal',
    code: 'WB',
    capital: 'Kolkata',
    description: 'Cultural capital with diverse temples and spiritual traditions.',
    totalTemples: 35,
    famousTemples: ['Dakshineswar Temple', 'Kalighat Temple', 'Belur Math'],
    svgPath: 'M 320,120 L 400,100 L 420,170 L 380,210 L 340,190 L 300,150 Z',
    cities: []
  },
  {
    id: 'gujarat',
    name: 'Gujarat',
    code: 'GJ',
    capital: 'Gandhinagar',
    description: 'Land of diverse temples and rich cultural heritage.',
    totalTemples: 60,
    famousTemples: ['Somnath Temple', 'Dwarka Temple', 'Akshardham Temple'],
    svgPath: 'M 50,150 L 150,130 L 170,200 L 130,240 L 70,220 L 30,180 Z',
    cities: []
  }
];
