@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    margin: 0;
    min-height: 100vh;
    font-family: 'Inter', system-ui, sans-serif;
    background: linear-gradient(to bottom right, #f8fafc, #eff6ff);
    color: #0f172a;
    -webkit-font-smoothing: antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    background: linear-gradient(to right, #f97316, #ea580c);
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
    border: none;
    cursor: pointer;
  }

  .btn-primary:hover {
    background: linear-gradient(to right, #ea580c, #c2410c);
    transform: scale(1.05);
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15);
  }

  .btn-secondary {
    background: white;
    color: #111827;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
    transition: all 0.2s;
    cursor: pointer;
  }

  .btn-secondary:hover {
    background: #f9fafb;
    transform: scale(1.05);
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    border-radius: 0.75rem;
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.5rem;
    transition: all 0.2s;
  }

  .card:hover {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .map-state {
    fill: #fed7aa;
    stroke: #fdba74;
    stroke-width: 1;
    cursor: pointer;
    transition: all 0.2s;
  }

  .map-state:hover {
    fill: #fbbf24;
    stroke: #f59e0b;
    stroke-width: 2;
  }

  .map-state-active {
    fill: #fdba74;
    stroke: #f97316;
    stroke-width: 2;
  }

  .map-city {
    fill: #bae6fd;
    stroke: #7dd3fc;
    stroke-width: 1;
    cursor: pointer;
    transition: all 0.2s;
  }

  .map-city:hover {
    fill: #38bdf8;
    stroke: #0ea5e9;
    stroke-width: 2;
  }

  .map-city-active {
    fill: #7dd3fc;
    stroke: #0ea5e9;
    stroke-width: 2;
  }
}

@layer utilities {
  .text-gradient {
    background: linear-gradient(to right, #ea580c, #0284c7);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .bg-gradient-temple {
    background: linear-gradient(to bottom right, #FF9933, #FFD700, #800000);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
