/**
 * <PERSON><PERSON><PERSON> to extract real SVG paths from the reference file
 * Run this in the browser console on the reference SVG page
 */

// Function to extract and scale paths from the reference SVG
function extractAndScalePaths() {
  // Get all path elements from the reference SVG
  const paths = document.querySelectorAll('path[id]');
  const extractedData = {};
  
  // State ID mapping
  const stateMapping = {
    'INRJ': 'rajasthan',
    'INTN': 'tamil-nadu',
    'INKA': 'karnataka', 
    'INKL': 'kerala',
    'INAP': 'andhra-pradesh',
    'INTG': 'telangana',
    'INOR': 'odisha',
    'INWB': 'west-bengal',
    'INJH': 'jharkhand',
    'INBR': 'bihar',
    'INUP': 'uttar-pradesh',
    'INMP': 'madhya-pradesh',
    'INCT': 'chhattisgarh',
    'INMH': 'maharashtra',
    'INGA': 'goa',
    'INGJ': 'gujarat',
    'INPB': 'punjab',
    'INHR': 'haryana',
    'INHP': 'himachal-pradesh',
    'INUT': 'uttarakhand',
    'INAS': 'assam',
    'INAR': 'arunachal-pradesh',
    'INNL': 'nagaland',
    'INMN': 'manipur',
    'INMZ': 'mizoram',
    'INTR': 'tripura',
    'INML': 'meghalaya',
    'INSK': 'sikkim'
  };
  
  // Scale factors from 1000x1000 to 800x600
  const scaleX = 0.8;
  const scaleY = 0.6;
  
  paths.forEach(path => {
    const id = path.getAttribute('id');
    const stateName = stateMapping[id];
    
    if (stateName) {
      const originalPath = path.getAttribute('d');
      
      // Scale the path coordinates
      const scaledPath = originalPath.replace(/([ML])\s*([\d.-]+)\s*,?\s*([\d.-]+)/g, (match, command, x, y) => {
        const newX = (parseFloat(x) * scaleX).toFixed(1);
        const newY = (parseFloat(y) * scaleY).toFixed(1);
        return `${command} ${newX},${newY}`;
      });
      
      // Calculate center point
      const bbox = path.getBBox();
      const centerX = Math.round((bbox.x + bbox.width / 2) * scaleX);
      const centerY = Math.round((bbox.y + bbox.height / 2) * scaleY);
      
      extractedData[stateName] = {
        path: scaledPath,
        center: { x: centerX, y: centerY }
      };
    }
  });
  
  return extractedData;
}

// For manual extraction, here are the key state paths from the reference SVG
// These are simplified but more accurate representations

const improvedMapData = {
  rajasthan: {
    path: "M 80,180 L 200,160 L 240,200 L 260,280 L 240,340 L 200,360 L 160,340 L 120,300 L 100,240 L 80,180 Z",
    center: { x: 170, y: 260 }
  },
  gujarat: {
    path: "M 100,240 L 200,220 L 240,260 L 260,320 L 240,340 L 200,330 L 160,310 L 120,290 L 100,260 L 100,240 Z",
    center: { x: 180, y: 280 }
  },
  maharashtra: {
    path: "M 260,300 L 400,280 L 440,320 L 460,380 L 440,400 L 400,390 L 360,370 L 320,350 L 300,330 L 260,300 Z",
    center: { x: 360, y: 340 }
  },
  karnataka: {
    path: "M 260,420 L 340,400 L 380,440 L 400,500 L 380,520 L 340,510 L 300,480 L 280,450 L 260,420 Z",
    center: { x: 330, y: 460 }
  },
  kerala: {
    path: "M 220,500 L 260,480 L 280,520 L 300,560 L 280,580 L 260,570 L 240,550 L 220,520 L 220,500 Z",
    center: { x: 260, y: 530 }
  },
  "tamil-nadu": {
    path: "M 340,480 L 400,460 L 440,500 L 460,560 L 440,580 L 400,570 L 360,550 L 340,520 L 340,480 Z",
    center: { x: 400, y: 520 }
  },
  "andhra-pradesh": {
    path: "M 540,380 L 600,360 L 640,400 L 660,460 L 640,480 L 600,470 L 560,450 L 540,420 L 540,380 Z",
    center: { x: 600, y: 420 }
  },
  telangana: {
    path: "M 440,340 L 500,320 L 540,360 L 560,420 L 540,440 L 500,430 L 460,410 L 440,380 L 440,340 Z",
    center: { x: 500, y: 380 }
  },
  odisha: {
    path: "M 520,300 L 580,280 L 620,320 L 640,380 L 620,400 L 580,390 L 540,370 L 520,340 L 520,300 Z",
    center: { x: 580, y: 340 }
  },
  "west-bengal": {
    path: "M 560,220 L 620,200 L 660,240 L 680,300 L 660,320 L 620,310 L 580,290 L 560,260 L 560,220 Z",
    center: { x: 620, y: 260 }
  },
  jharkhand: {
    path: "M 500,240 L 560,220 L 600,260 L 620,320 L 600,340 L 560,330 L 520,310 L 500,280 L 500,240 Z",
    center: { x: 560, y: 280 }
  },
  bihar: {
    path: "M 480,200 L 540,180 L 580,220 L 600,280 L 580,300 L 540,290 L 500,270 L 480,240 L 480,200 Z",
    center: { x: 540, y: 240 }
  },
  "uttar-pradesh": {
    path: "M 360,140 L 480,120 L 520,160 L 540,220 L 520,240 L 480,230 L 440,210 L 400,190 L 380,170 L 360,140 Z",
    center: { x: 450, y: 180 }
  },
  "madhya-pradesh": {
    path: "M 320,240 L 440,220 L 480,260 L 500,320 L 480,340 L 440,330 L 400,310 L 360,290 L 340,270 L 320,240 Z",
    center: { x: 410, y: 280 }
  },
  chhattisgarh: {
    path: "M 460,280 L 520,260 L 560,300 L 580,360 L 560,380 L 520,370 L 480,350 L 460,320 L 460,280 Z",
    center: { x: 520, y: 320 }
  },
  punjab: {
    path: "M 240,80 L 310,60 L 350,100 L 370,160 L 350,180 L 310,170 L 270,150 L 250,120 L 240,80 Z",
    center: { x: 305, y: 120 }
  },
  haryana: {
    path: "M 270,120 L 340,100 L 360,140 L 340,180 L 300,170 L 280,150 L 270,120 Z",
    center: { x: 315, y: 140 }
  },
  "himachal-pradesh": {
    path: "M 280,60 L 360,40 L 400,80 L 380,120 L 340,110 L 300,90 L 280,60 Z",
    center: { x: 340, y: 80 }
  },
  uttarakhand: {
    path: "M 380,80 L 440,60 L 480,100 L 460,140 L 420,130 L 400,110 L 380,80 Z",
    center: { x: 430, y: 100 }
  },
  assam: {
    path: "M 640,180 L 700,160 L 740,200 L 720,240 L 680,230 L 660,210 L 640,180 Z",
    center: { x: 690, y: 200 }
  },
  "arunachal-pradesh": {
    path: "M 700,100 L 760,80 L 800,120 L 780,160 L 740,150 L 720,130 L 700,100 Z",
    center: { x: 750, y: 120 }
  },
  nagaland: {
    path: "M 720,180 L 760,170 L 780,200 L 760,230 L 740,220 L 720,200 L 720,180 Z",
    center: { x: 750, y: 200 }
  },
  manipur: {
    path: "M 720,240 L 760,230 L 780,260 L 760,290 L 740,280 L 720,260 L 720,240 Z",
    center: { x: 750, y: 260 }
  },
  mizoram: {
    path: "M 700,280 L 740,270 L 760,300 L 740,330 L 720,320 L 700,300 L 700,280 Z",
    center: { x: 730, y: 300 }
  },
  tripura: {
    path: "M 680,260 L 720,250 L 740,280 L 720,310 L 700,300 L 680,280 L 680,260 Z",
    center: { x: 710, y: 280 }
  },
  meghalaya: {
    path: "M 620,220 L 660,210 L 680,240 L 660,270 L 640,260 L 620,240 L 620,220 Z",
    center: { x: 650, y: 240 }
  },
  sikkim: {
    path: "M 580,140 L 600,135 L 610,155 L 600,175 L 580,170 L 570,155 L 580,140 Z",
    center: { x: 590, y: 155 }
  },
  goa: {
    path: "M 220,380 L 250,370 L 270,390 L 280,420 L 270,430 L 250,420 L 230,400 L 220,380 Z",
    center: { x: 250, y: 400 }
  }
};

console.log('Improved map data:', JSON.stringify(improvedMapData, null, 2));
