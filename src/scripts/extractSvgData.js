#!/usr/bin/env node

/**
 * SVG Path Extraction Script
 * Extracts accurate geographic data from the professional SVG map file
 * Generates consolidated map data for the application
 */

import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Path configuration
const SVG_PATH = join(__dirname, '../assets/maps/in.svg');
const OUTPUT_PATH = join(__dirname, '../data/accurateMapData.ts');

// State mapping from SVG IDs to application IDs
const STATE_MAPPING = {
  'INAN': { id: 'andaman-nicobar', name: 'Andaman and Nicobar Islands' },
  'INAP': { id: 'andhra-pradesh', name: 'Andhra Pradesh' },
  'INAR': { id: 'arunachal-pradesh', name: 'Arunachal Pradesh' },
  'INAS': { id: 'assam', name: 'Assam' },
  'INBR': { id: 'bihar', name: 'Bihar' },
  'INCH': { id: 'chandigarh', name: '<PERSON>digarh' },
  'INCT': { id: 'chhattisgarh', name: 'Chhattisgarh' },
  'INDH': { id: 'dadra-nagar-haveli-daman-diu', name: 'Dadra and Nagar Haveli and Daman and Diu' },
  'INDL': { id: 'delhi', name: 'Delhi' },
  'INGA': { id: 'goa', name: 'Goa' },
  'INGJ': { id: 'gujarat', name: 'Gujarat' },
  'INHR': { id: 'haryana', name: 'Haryana' },
  'INHP': { id: 'himachal-pradesh', name: 'Himachal Pradesh' },
  'INJK': { id: 'jammu-kashmir', name: 'Jammu and Kashmir' },
  'INJH': { id: 'jharkhand', name: 'Jharkhand' },
  'INKA': { id: 'karnataka', name: 'Karnataka' },
  'INKL': { id: 'kerala', name: 'Kerala' },
  'INLA': { id: 'ladakh', name: 'Ladakh' },
  'INLD': { id: 'lakshadweep', name: 'Lakshadweep' },
  'INMP': { id: 'madhya-pradesh', name: 'Madhya Pradesh' },
  'INMH': { id: 'maharashtra', name: 'Maharashtra' },
  'INMN': { id: 'manipur', name: 'Manipur' },
  'INML': { id: 'meghalaya', name: 'Meghalaya' },
  'INMZ': { id: 'mizoram', name: 'Mizoram' },
  'INNL': { id: 'nagaland', name: 'Nagaland' },
  'INOR': { id: 'odisha', name: 'Odisha' },
  'INPY': { id: 'puducherry', name: 'Puducherry' },
  'INPB': { id: 'punjab', name: 'Punjab' },
  'INRJ': { id: 'rajasthan', name: 'Rajasthan' },
  'INSK': { id: 'sikkim', name: 'Sikkim' },
  'INTN': { id: 'tamil-nadu', name: 'Tamil Nadu' },
  'INTG': { id: 'telangana', name: 'Telangana' },
  'INTR': { id: 'tripura', name: 'Tripura' },
  'INUP': { id: 'uttar-pradesh', name: 'Uttar Pradesh' },
  'INUK': { id: 'uttarakhand', name: 'Uttarakhand' },
  'INWB': { id: 'west-bengal', name: 'West Bengal' }
};

/**
 * Parse SVG content and extract path data
 */
function parseSvgFile(filePath) {
  try {
    const svgContent = readFileSync(filePath, 'utf-8');
    console.log('✓ Successfully loaded SVG file');
    
    // Extract viewBox
    const viewBoxMatch = svgContent.match(/viewBox=["']([^"']+)["']/);
    const viewBox = viewBoxMatch ? viewBoxMatch[1] : '0 0 800 600';
    
    // Extract state paths
    const stateData = {};
    let extractedCount = 0;
    
    Object.entries(STATE_MAPPING).forEach(([svgId, stateInfo]) => {
      // Look for the path element with this ID
      const pathRegex = new RegExp(`<path[^>]+id=["']${svgId}["'][^>]*d=["']([^"']+)["']`, 'i');
      const match = svgContent.match(pathRegex);
      
      if (match) {
        const pathData = match[1];
        const center = calculateCenter(pathData);
        const boundingBox = calculateBoundingBox(pathData);
        const area = calculateArea(pathData);
        
        stateData[stateInfo.id] = {
          id: stateInfo.id,
          svgId: svgId,
          name: stateInfo.name,
          path: pathData,
          center: center,
          boundingBox: boundingBox,
          area: area
        };
        
        extractedCount++;
        console.log(`✓ Extracted: ${stateInfo.name} (${svgId})`);
      } else {
        console.warn(`⚠ Could not find path for: ${stateInfo.name} (${svgId})`);
      }
    });
    
    console.log(`\n✓ Successfully extracted ${extractedCount} state paths`);
    
    return {
      viewBox,
      states: stateData,
      metadata: {
        extractedAt: new Date().toISOString(),
        totalStates: extractedCount,
        sourceFile: filePath
      }
    };
    
  } catch (error) {
    console.error('✗ Error parsing SVG file:', error.message);
    throw error;
  }
}

/**
 * Calculate center point of SVG path
 */
function calculateCenter(pathData) {
  const coordinates = extractCoordinates(pathData);
  if (coordinates.length === 0) return { x: 0, y: 0 };
  
  const sumX = coordinates.reduce((sum, coord) => sum + coord.x, 0);
  const sumY = coordinates.reduce((sum, coord) => sum + coord.y, 0);
  
  return {
    x: Math.round(sumX / coordinates.length * 100) / 100,
    y: Math.round(sumY / coordinates.length * 100) / 100
  };
}

/**
 * Calculate bounding box of SVG path
 */
function calculateBoundingBox(pathData) {
  const coordinates = extractCoordinates(pathData);
  if (coordinates.length === 0) {
    return { minX: 0, minY: 0, maxX: 0, maxY: 0, width: 0, height: 0 };
  }
  
  const xs = coordinates.map(c => c.x);
  const ys = coordinates.map(c => c.y);
  
  const minX = Math.min(...xs);
  const minY = Math.min(...ys);
  const maxX = Math.max(...xs);
  const maxY = Math.max(...ys);
  
  return {
    minX: Math.round(minX * 100) / 100,
    minY: Math.round(minY * 100) / 100,
    maxX: Math.round(maxX * 100) / 100,
    maxY: Math.round(maxY * 100) / 100,
    width: Math.round((maxX - minX) * 100) / 100,
    height: Math.round((maxY - minY) * 100) / 100
  };
}

/**
 * Calculate approximate area using shoelace formula
 */
function calculateArea(pathData) {
  const coordinates = extractCoordinates(pathData);
  if (coordinates.length < 3) return 0;
  
  let area = 0;
  for (let i = 0; i < coordinates.length; i++) {
    const j = (i + 1) % coordinates.length;
    area += coordinates[i].x * coordinates[j].y;
    area -= coordinates[j].x * coordinates[i].y;
  }
  
  return Math.round(Math.abs(area) / 2 * 100) / 100;
}

/**
 * Extract coordinate pairs from SVG path data
 */
function extractCoordinates(pathData) {
  // Clean up path data and extract numbers
  const cleanPath = pathData.replace(/[MmLlHhVvCcSsQqTtAaZz]/g, ' ');
  const numbers = cleanPath.split(/[\s,]+/).filter(n => n && !isNaN(parseFloat(n)));
  
  const coordinates = [];
  for (let i = 0; i < numbers.length - 1; i += 2) {
    const x = parseFloat(numbers[i]);
    const y = parseFloat(numbers[i + 1]);
    
    if (!isNaN(x) && !isNaN(y)) {
      coordinates.push({ x, y });
    }
  }
  
  return coordinates;
}

/**
 * Generate TypeScript file content
 */
function generateTypeScriptFile(mapData) {
  const timestamp = new Date().toISOString();
  
  return `// Auto-generated accurate map data extracted from professional SVG
// Generated on: ${timestamp}
// Source: ${mapData.metadata.sourceFile}
// Total states: ${mapData.metadata.totalStates}

export interface AccurateStateGeography {
  id: string;
  svgId: string;
  name: string;
  path: string;
  center: { x: number; y: number };
  boundingBox: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
    width: number;
    height: number;
  };
  area: number;
}

export interface AccurateMapData {
  viewBox: string;
  states: Record<string, AccurateStateGeography>;
  metadata: {
    extractedAt: string;
    totalStates: number;
    sourceFile: string;
  };
}

export const accurateMapData: AccurateMapData = ${JSON.stringify(mapData, null, 2)};

export default accurateMapData;
`;
}

/**
 * Main execution function
 */
function main() {
  console.log('🗺️  SVG Path Extraction Script');
  console.log('================================\n');
  
  try {
    // Parse SVG file
    console.log(`📂 Loading SVG file: ${SVG_PATH}`);
    const mapData = parseSvgFile(SVG_PATH);
    
    // Generate TypeScript file
    console.log(`\n📝 Generating TypeScript file: ${OUTPUT_PATH}`);
    const tsContent = generateTypeScriptFile(mapData);
    writeFileSync(OUTPUT_PATH, tsContent, 'utf-8');
    
    console.log('\n✅ Successfully generated accurate map data!');
    console.log(`📊 Statistics:`);
    console.log(`   - States extracted: ${mapData.metadata.totalStates}`);
    console.log(`   - ViewBox: ${mapData.viewBox}`);
    console.log(`   - Output file: ${OUTPUT_PATH}`);
    
  } catch (error) {
    console.error('\n❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
