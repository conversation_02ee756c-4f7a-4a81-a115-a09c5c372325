/**
 * <PERSON><PERSON>t to generate accurate map data from GeoJSON files
 * This will create properly scaled SVG paths that fit within our viewBox
 */

import {
  calculateBoundingBox,
  featureToSVGPath,
  calculateFeatureCenter,
  groupFeaturesByState,
  projectToSVG
} from '../utils/geoJsonToSvg';

// State name mapping from GeoJSON to our application format
const STATE_NAME_MAPPING: Record<string, string> = {
  'Andaman and Nicobar Islands': 'andaman-and-nicobar',
  'Andhra Pradesh': 'andhra-pradesh',
  'Arunachal Pradesh': 'arunachal-pradesh',
  'Assam': 'assam',
  'Bihar': 'bihar',
  'Chandigarh': 'chandigarh',
  'Chhattisgarh': 'chhattisgarh',
  'Delhi': 'delhi',
  'Dadra and Nagar Haveli and Daman and Diu': 'dnh-and-dd',
  'Goa': 'goa',
  'Gujarat': 'gujarat',
  'Haryana': 'haryana',
  'Himachal Pradesh': 'himachal-pradesh',
  'Jammu and Kashmir': 'jammu-and-kashmir',
  'Jharkhand': 'jharkhand',
  'Karnataka': 'karnataka',
  'Kerala': 'kerala',
  'Ladakh': 'ladakh',
  'Lakshadweep': 'lakshadweep',
  'Madhya Pradesh': 'madhya-pradesh',
  'Maharashtra': 'maharashtra',
  'Manipur': 'manipur',
  'Meghalaya': 'meghalaya',
  'Mizoram': 'mizoram',
  'Nagaland': 'nagaland',
  'Odisha': 'odisha',
  'Puducherry': 'puducherry',
  'Punjab': 'punjab',
  'Rajasthan': 'rajasthan',
  'Sikkim': 'sikkim',
  'Tamil Nadu': 'tamil-nadu',
  'Telangana': 'telangana',
  'Tripura': 'tripura',
  'Uttar Pradesh': 'uttar-pradesh',
  'Uttarakhand': 'uttarakhand',
  'West Bengal': 'west-bengal'
};

interface GeoJSONFeature {
  type: 'Feature';
  properties: {
    st_nm: string;
    st_code: string;
    district?: string;
    dt_code?: string;
  };
  geometry: {
    type: 'Polygon' | 'MultiPolygon';
    coordinates: number[][][] | number[][][][];
  };
}

interface GeoJSONFeatureCollection {
  type: 'FeatureCollection';
  features: GeoJSONFeature[];
}

/**
 * Create simplified state boundaries by merging districts
 */
function createSimplifiedStateBoundary(features: GeoJSONFeature[], bbox: any, width: number, height: number): string {
  // Find the bounding box of all features in this state
  let allCoords: number[][] = [];
  
  features.forEach(feature => {
    if (feature.geometry.type === 'Polygon') {
      allCoords.push(...feature.geometry.coordinates[0]);
    } else if (feature.geometry.type === 'MultiPolygon') {
      feature.geometry.coordinates.forEach(polygon => {
        allCoords.push(...polygon[0]);
      });
    }
  });

  if (allCoords.length === 0) return '';

  // Calculate state bounding box
  const stateBbox = calculateBoundingBox([allCoords]);
  
  // Create a simplified boundary using the convex hull approach
  // For now, we'll use a simplified rectangular approach
  const { minX, maxX, minY, maxY } = stateBbox;
  
  // Project corners to SVG coordinates
  const [x1, y1] = projectToSVG(minX, maxY, bbox, width, height);
  const [x2, y2] = projectToSVG(maxX, maxY, bbox, width, height);
  const [x3, y3] = projectToSVG(maxX, minY, bbox, width, height);
  const [x4, y4] = projectToSVG(minX, minY, bbox, width, height);
  
  return `M ${x1.toFixed(1)},${y1.toFixed(1)} L ${x2.toFixed(1)},${y2.toFixed(1)} L ${x3.toFixed(1)},${y3.toFixed(1)} L ${x4.toFixed(1)},${y4.toFixed(1)} Z`;
}

/**
 * Generate map data from GeoJSON
 */
export async function generateMapDataFromGeoJSON(): Promise<any> {
  try {
    // Load the GeoJSON data
    const response = await fetch('/src/assets/maps/india-maps-data-main/geojson/india.geojson');
    const geoJsonData: GeoJSONFeatureCollection = await response.json();
    
    const { features } = geoJsonData;
    
    // Calculate overall bounding box for India
    let allCoords: number[][] = [];
    features.forEach(feature => {
      if (feature.geometry.type === 'Polygon') {
        allCoords.push(...feature.geometry.coordinates[0]);
      } else if (feature.geometry.type === 'MultiPolygon') {
        feature.geometry.coordinates.forEach(polygon => {
          allCoords.push(...polygon[0]);
        });
      }
    });
    
    const overallBbox = calculateBoundingBox([allCoords]);
    const mapWidth = 800;
    const mapHeight = 600;
    
    // Group features by state
    const stateGroups = groupFeaturesByState(features);
    
    const processedStates: Record<string, any> = {};
    const processedCities: Record<string, any> = {};
    
    // Process each state
    stateGroups.forEach((stateFeatures, stateName) => {
      const stateId = STATE_NAME_MAPPING[stateName];
      if (!stateId) {
        console.warn(`No mapping found for state: ${stateName}`);
        return;
      }
      
      // Create simplified state boundary
      const statePath = createSimplifiedStateBoundary(stateFeatures, overallBbox, mapWidth, mapHeight);
      
      // Calculate state center
      let allStateCoords: number[][] = [];
      stateFeatures.forEach(feature => {
        if (feature.geometry.type === 'Polygon') {
          allStateCoords.push(...feature.geometry.coordinates[0]);
        } else if (feature.geometry.type === 'MultiPolygon') {
          feature.geometry.coordinates.forEach(polygon => {
            allStateCoords.push(...polygon[0]);
          });
        }
      });
      
      if (allStateCoords.length > 0) {
        const centerLon = allStateCoords.reduce((sum, coord) => sum + coord[0], 0) / allStateCoords.length;
        const centerLat = allStateCoords.reduce((sum, coord) => sum + coord[1], 0) / allStateCoords.length;
        const [centerX, centerY] = projectToSVG(centerLon, centerLat, overallBbox, mapWidth, mapHeight);
        
        processedStates[stateId] = {
          path: statePath,
          center: { x: Math.round(centerX), y: Math.round(centerY) }
        };
      }
      
      // Process districts as cities
      const stateCities: Record<string, any> = {};
      stateFeatures.forEach(feature => {
        const districtName = feature.properties.district;
        if (districtName) {
          const districtId = districtName.toLowerCase().replace(/\s+/g, '-');
          const districtPath = featureToSVGPath(feature, overallBbox, mapWidth, mapHeight);
          const districtCenter = calculateFeatureCenter(feature, overallBbox, mapWidth, mapHeight);
          
          stateCities[districtId] = {
            path: districtPath,
            center: { x: Math.round(districtCenter.x), y: Math.round(districtCenter.y) }
          };
        }
      });
      
      if (Object.keys(stateCities).length > 0) {
        processedCities[stateId] = stateCities;
      }
    });
    
    return {
      viewBox: `0 0 ${mapWidth} ${mapHeight}`,
      states: processedStates,
      cities: processedCities
    };
    
  } catch (error) {
    console.error('Error generating map data:', error);
    throw error;
  }
}
