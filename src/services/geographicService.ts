// Geographic service for extracting and managing accurate map data
// Integrates with the professional SVG map file to provide accurate state boundaries

import type { AccurateStateData, GeographicCoordinate, BoundingBox } from '../utils/geographicUtils';
import { 
  createAccurateStateData, 
  svgStateMapping,
  isPointInBoundingBox 
} from '../utils/geographicUtils';

export interface MapConfiguration {
  viewBox: string;
  width: number;
  height: number;
  projection: 'mercator' | 'robinson' | 'svg-native';
}

export interface ProcessedMapData {
  configuration: MapConfiguration;
  states: AccurateStateData[];
  boundingBox: BoundingBox;
  center: GeographicCoordinate;
  totalArea: number;
}

/**
 * Service class for managing geographic data and map operations
 */
export class GeographicService {
  private static instance: GeographicService;
  private mapData: ProcessedMapData | null = null;
  private svgElement: SVGElement | null = null;

  private constructor() {}

  static getInstance(): GeographicService {
    if (!GeographicService.instance) {
      GeographicService.instance = new GeographicService();
    }
    return GeographicService.instance;
  }

  /**
   * Loads the SVG map file and extracts accurate geographic data
   */
  async loadMapData(): Promise<ProcessedMapData> {
    if (this.mapData) {
      return this.mapData;
    }

    try {
      // Load the SVG file
      const svgContent = await this.loadSvgFile();
      this.svgElement = this.parseSvgContent(svgContent);
      
      // Extract state data from SVG
      const states = this.extractStateDataFromSvg();
      
      // Calculate overall map metrics
      const mapBoundingBox = this.calculateMapBoundingBox(states);
      const mapCenter = this.calculateMapCenter(mapBoundingBox);
      const totalArea = states.reduce((sum, state) => sum + state.area, 0);
      
      // Get SVG configuration
      const configuration = this.extractSvgConfiguration();
      
      this.mapData = {
        configuration,
        states,
        boundingBox: mapBoundingBox,
        center: mapCenter,
        totalArea
      };

      return this.mapData;
    } catch (error) {
      console.error('Failed to load map data:', error);
      throw new Error('Geographic service initialization failed');
    }
  }

  /**
   * Gets accurate state data by state ID
   */
  getStateData(stateId: string): AccurateStateData | null {
    if (!this.mapData) {
      console.warn('Map data not loaded. Call loadMapData() first.');
      return null;
    }

    return this.mapData.states.find(state => state.id === stateId) || null;
  }

  /**
   * Gets all available state data
   */
  getAllStates(): AccurateStateData[] {
    return this.mapData?.states || [];
  }

  /**
   * Finds state containing a specific coordinate point
   */
  getStateByCoordinates(coordinates: GeographicCoordinate): AccurateStateData | null {
    if (!this.mapData) return null;

    // First check bounding boxes for performance
    const candidateStates = this.mapData.states.filter(state =>
      isPointInBoundingBox(coordinates, state.boundingBox)
    );

    // If only one candidate, return it
    if (candidateStates.length === 1) {
      return candidateStates[0];
    }

    // For multiple candidates, use more precise point-in-polygon test
    // This would require a more sophisticated algorithm for production use
    return candidateStates.length > 0 ? candidateStates[0] : null;
  }

  /**
   * Gets map configuration information
   */
  getMapConfiguration(): MapConfiguration | null {
    return this.mapData?.configuration || null;
  }

  /**
   * Validates if the geographic data is properly loaded
   */
  isDataLoaded(): boolean {
    return this.mapData !== null && this.mapData.states.length > 0;
  }

  /**
   * Gets map statistics
   */
  getMapStatistics() {
    if (!this.mapData) return null;

    return {
      totalStates: this.mapData.states.length,
      totalArea: this.mapData.totalArea,
      averageStateArea: this.mapData.totalArea / this.mapData.states.length,
      largestState: this.mapData.states.reduce((largest, state) => 
        state.area > largest.area ? state : largest
      ),
      smallestState: this.mapData.states.reduce((smallest, state) => 
        state.area < smallest.area ? state : smallest
      ),
      mapDimensions: {
        width: this.mapData.boundingBox.width,
        height: this.mapData.boundingBox.height
      }
    };
  }

  // Private methods

  /**
   * Loads the SVG file content
   */
  private async loadSvgFile(): Promise<string> {
    try {
      // In a real implementation, this would load from the assets folder
      // For now, we'll provide a placeholder that would be replaced with actual file loading
      const response = await fetch('/src/assets/maps/in.svg');
      if (!response.ok) {
        throw new Error(`Failed to load SVG: ${response.status}`);
      }
      return await response.text();
    } catch (error) {
      // Fallback: return empty SVG for development
      console.warn('Could not load SVG file, using fallback data');
      return this.getFallbackSvgData();
    }
  }

  /**
   * Parses SVG content into DOM element
   */
  private parseSvgContent(svgContent: string): SVGElement {
    const parser = new DOMParser();
    const doc = parser.parseFromString(svgContent, 'image/svg+xml');
    const svgElement = doc.querySelector('svg');
    
    if (!svgElement) {
      throw new Error('Invalid SVG content');
    }
    
    return svgElement;
  }

  /**
   * Extracts state data from SVG elements
   */
  private extractStateDataFromSvg(): AccurateStateData[] {
    if (!this.svgElement) {
      throw new Error('SVG element not loaded');
    }

    const states: AccurateStateData[] = [];
    
    // Find all path elements with state IDs
    svgStateMapping.forEach(mapping => {
      const pathElement = this.svgElement!.querySelector(`#${mapping.svgId}`) as SVGPathElement;
      
      if (pathElement) {
        const pathData = pathElement.getAttribute('d');
        if (pathData) {
          const stateData = createAccurateStateData(mapping.svgId, pathData);
          if (stateData) {
            states.push(stateData);
          }
        }
      }
    });

    return states;
  }

  /**
   * Calculates the overall bounding box for the entire map
   */
  private calculateMapBoundingBox(states: AccurateStateData[]): BoundingBox {
    if (states.length === 0) {
      return { minX: 0, minY: 0, maxX: 0, maxY: 0, width: 0, height: 0 };
    }

    const allBounds = states.map(state => state.boundingBox);
    
    const minX = Math.min(...allBounds.map(b => b.minX));
    const minY = Math.min(...allBounds.map(b => b.minY));
    const maxX = Math.max(...allBounds.map(b => b.maxX));
    const maxY = Math.max(...allBounds.map(b => b.maxY));
    
    return {
      minX,
      minY,
      maxX,
      maxY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  /**
   * Calculates the geographic center of the entire map
   */
  private calculateMapCenter(boundingBox: BoundingBox): GeographicCoordinate {
    return {
      x: boundingBox.minX + boundingBox.width / 2,
      y: boundingBox.minY + boundingBox.height / 2
    };
  }

  /**
   * Extracts SVG configuration from the SVG element
   */
  private extractSvgConfiguration(): MapConfiguration {
    if (!this.svgElement) {
      throw new Error('SVG element not loaded');
    }

    const viewBox = this.svgElement.getAttribute('viewBox') || '0 0 800 600';
    const [, , width, height] = viewBox.split(' ').map(Number);
    
    return {
      viewBox,
      width: width || 800,
      height: height || 600,
      projection: 'svg-native'
    };
  }

  /**
   * Provides fallback SVG data for development
   */
  private getFallbackSvgData(): string {
    return `
      <svg viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
        <path id="INRJ" d="M 100,220 L 260,200 L 320,240 L 360,340 L 320,420 L 260,440 L 200,420 L 140,380 L 120,320 L 100,260 Z" />
        <path id="INTN" d="M 420,600 L 520,580 L 580,620 L 620,720 L 580,760 L 520,750 L 460,730 L 420,680 Z" />
        <path id="INKA" d="M 320,540 L 420,520 L 480,560 L 520,640 L 480,680 L 420,670 L 360,640 L 320,580 Z" />
      </svg>
    `;
  }
}

/**
 * Convenience function to get the geographic service instance
 */
export function getGeographicService(): GeographicService {
  return GeographicService.getInstance();
}

/**
 * Hook for React components to use geographic data
 */
export function useGeographicData() {
  const [mapData, setMapData] = React.useState<ProcessedMapData | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const service = getGeographicService();
        const data = await service.loadMapData();
        setMapData(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load geographic data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return { mapData, loading, error };
}

// Export React for the hook
import React from 'react';
