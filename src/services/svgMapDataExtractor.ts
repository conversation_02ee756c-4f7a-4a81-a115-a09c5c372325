/**
 * SVG Map Data Extractor Service
 * Extracts geographic path data from SVG files for accurate map rendering
 */

import { StateGeographicData } from '../types/geographic';

export interface SVGPathData {
  id: string;
  path: string;
  name: string;
  bounds?: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
  center?: {
    x: number;
    y: number;
  };
}

export interface ExtractedMapData {
  states: SVGPathData[];
  viewBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  totalBounds: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
}

export class SVGMapDataExtractor {
  /**
   * Extract geographic data from SVG content
   */
  static extractFromSVG(svgContent: string): ExtractedMapData {
    const parser = new DOMParser();
    const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml');
    const svgElement = svgDoc.querySelector('svg');
    
    if (!svgElement) {
      throw new Error('Invalid SVG content');
    }

    // Extract viewBox
    const viewBox = this.extractViewBox(svgElement);
    
    // Extract path elements
    const pathElements = Array.from(svgDoc.querySelectorAll('path[id]'));
    const states: SVGPathData[] = [];
    
    let totalMinX = Infinity;
    let totalMinY = Infinity;
    let totalMaxX = -Infinity;
    let totalMaxY = -Infinity;

    for (const pathElement of pathElements) {
      const id = pathElement.getAttribute('id');
      const pathData = pathElement.getAttribute('d');
      
      if (id && pathData) {
        const bounds = this.calculatePathBounds(pathData);
        const center = bounds ? {
          x: (bounds.minX + bounds.maxX) / 2,
          y: (bounds.minY + bounds.maxY) / 2
        } : undefined;

        // Update total bounds
        if (bounds) {
          totalMinX = Math.min(totalMinX, bounds.minX);
          totalMinY = Math.min(totalMinY, bounds.minY);
          totalMaxX = Math.max(totalMaxX, bounds.maxX);
          totalMaxY = Math.max(totalMaxY, bounds.maxY);
        }

        states.push({
          id,
          path: pathData,
          name: this.formatStateName(id),
          bounds,
          center
        });
      }
    }

    return {
      states,
      viewBox,
      totalBounds: {
        minX: totalMinX,
        minY: totalMinY,
        maxX: totalMaxX,
        maxY: totalMaxY
      }
    };
  }

  /**
   * Extract viewBox from SVG element
   */
  private static extractViewBox(svgElement: Element): { x: number; y: number; width: number; height: number } {
    const viewBoxAttr = svgElement.getAttribute('viewBox');
    if (viewBoxAttr) {
      const [x, y, width, height] = viewBoxAttr.split(' ').map(Number);
      return { x, y, width, height };
    }
    
    // Fallback to width/height attributes
    const width = Number(svgElement.getAttribute('width')) || 800;
    const height = Number(svgElement.getAttribute('height')) || 600;
    return { x: 0, y: 0, width, height };
  }

  /**
   * Calculate bounds from SVG path data
   */
  private static calculatePathBounds(pathData: string): { minX: number; minY: number; maxX: number; maxY: number } | null {
    try {
      // Extract all coordinate pairs from path data
      const coordinates = this.extractCoordinatesFromPath(pathData);
      
      if (coordinates.length === 0) return null;

      let minX = Infinity;
      let minY = Infinity;
      let maxX = -Infinity;
      let maxY = -Infinity;

      for (const [x, y] of coordinates) {
        minX = Math.min(minX, x);
        minY = Math.min(minY, y);
        maxX = Math.max(maxX, x);
        maxY = Math.max(maxY, y);
      }

      return { minX, minY, maxX, maxY };
    } catch (error) {
      console.warn('Error calculating path bounds:', error);
      return null;
    }
  }

  /**
   * Extract coordinate pairs from SVG path data
   */
  private static extractCoordinatesFromPath(pathData: string): [number, number][] {
    const coordinates: [number, number][] = [];
    
    // Regular expression to match coordinate pairs
    const coordRegex = /(-?\d*\.?\d+)[,\s]+(-?\d*\.?\d+)/g;
    let match;
    
    while ((match = coordRegex.exec(pathData)) !== null) {
      const x = parseFloat(match[1]);
      const y = parseFloat(match[2]);
      
      if (!isNaN(x) && !isNaN(y)) {
        coordinates.push([x, y]);
      }
    }
    
    return coordinates;
  }

  /**
   * Format state ID to readable name
   */
  private static formatStateName(id: string): string {
    // State ID to name mapping
    const stateNames: Record<string, string> = {
      'in-an': 'Andaman and Nicobar Islands',
      'in-ap': 'Andhra Pradesh',
      'in-ar': 'Arunachal Pradesh',
      'in-as': 'Assam',
      'in-br': 'Bihar',
      'in-ch': 'Chandigarh',
      'in-ct': 'Chhattisgarh',
      'in-dn': 'Dadra and Nagar Haveli',
      'in-dd': 'Daman and Diu',
      'in-dl': 'Delhi',
      'in-ga': 'Goa',
      'in-gj': 'Gujarat',
      'in-hr': 'Haryana',
      'in-hp': 'Himachal Pradesh',
      'in-jk': 'Jammu and Kashmir',
      'in-jh': 'Jharkhand',
      'in-ka': 'Karnataka',
      'in-kl': 'Kerala',
      'in-ld': 'Lakshadweep',
      'in-mp': 'Madhya Pradesh',
      'in-mh': 'Maharashtra',
      'in-mn': 'Manipur',
      'in-ml': 'Meghalaya',
      'in-mz': 'Mizoram',
      'in-nl': 'Nagaland',
      'in-or': 'Odisha',
      'in-py': 'Puducherry',
      'in-pb': 'Punjab',
      'in-rj': 'Rajasthan',
      'in-sk': 'Sikkim',
      'in-tn': 'Tamil Nadu',
      'in-tg': 'Telangana',
      'in-tr': 'Tripura',
      'in-up': 'Uttar Pradesh',
      'in-ut': 'Uttarakhand',
      'in-wb': 'West Bengal'
    };

    return stateNames[id] || id.replace(/^in-/, '').toUpperCase();
  }

  /**
   * Convert SVG coordinates to lat/lng approximation
   * Note: This is an approximation and should be calibrated with known reference points
   */
  static svgToLatLng(svgX: number, svgY: number, bounds: { minX: number; minY: number; maxX: number; maxY: number }): { lat: number; lng: number } {
    // India's approximate geographic bounds
    const INDIA_BOUNDS = {
      north: 37.6,
      south: 6.4,
      east: 97.25,
      west: 68.7
    };

    // Normalize SVG coordinates to 0-1 range
    const normalizedX = (svgX - bounds.minX) / (bounds.maxX - bounds.minX);
    const normalizedY = (svgY - bounds.minY) / (bounds.maxY - bounds.minY);

    // Map to lat/lng (note: Y is inverted for SVG)
    const lng = INDIA_BOUNDS.west + normalizedX * (INDIA_BOUNDS.east - INDIA_BOUNDS.west);
    const lat = INDIA_BOUNDS.north - normalizedY * (INDIA_BOUNDS.north - INDIA_BOUNDS.south);

    return { lat, lng };
  }

  /**
   * Generate enhanced geographic data by combining SVG paths with existing data
   */
  static enhanceGeographicData(svgData: ExtractedMapData, existingData: StateGeographicData[]): StateGeographicData[] {
    return existingData.map(state => {
      const svgState = svgData.states.find(s => 
        s.id.includes(state.code.toLowerCase()) || 
        s.name.toLowerCase().includes(state.name.toLowerCase()) ||
        state.name.toLowerCase().includes(s.name.toLowerCase())
      );

      if (svgState && svgState.center) {
        const enhancedCoords = this.svgToLatLng(
          svgState.center.x, 
          svgState.center.y, 
          svgData.totalBounds
        );

        return {
          ...state,
          coordinates: enhancedCoords,
          svgPath: svgState.path,
          svgBounds: svgState.bounds,
          accuracy: 'high' // Mark as high accuracy from SVG data
        };
      }

      return {
        ...state,
        accuracy: 'medium' // Keep existing coordinate data
      };
    });
  }
}
