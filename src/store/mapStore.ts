import { create } from 'zustand';
import type { MapStore } from '../types';
import { states } from '../data/states';
import { temples } from '../data/temples';

export const useMapStore = create<MapStore>((set, get) => ({
  viewState: {
    level: 'country',
    selectedState: undefined,
    selectedCity: undefined,
    selectedTemples: []
  },
  states,
  temples,
  loading: false,
  error: null,

  setViewLevel: (level) => {
    set((state) => ({
      viewState: {
        ...state.viewState,
        level
      }
    }));
  },

  selectState: (selectedState) => {
    const stateTemples = temples.filter(temple =>
      temple.location.state === selectedState.name
    );

    set(() => ({
      viewState: {
        level: 'state',
        selectedState,
        selectedCity: undefined,
        selectedTemples: stateTemples
      }
    }));
  },

  selectCity: (selectedCity) => {
    const { selectedState } = get().viewState;
    const cityTemples = temples.filter(temple =>
      temple.location.city === selectedCity.name &&
      temple.location.state === selectedState?.name
    );

    set((state) => ({
      viewState: {
        ...state.viewState,
        level: 'city',
        selectedCity,
        selectedTemples: cityTemples
      }
    }));
  },

  goBack: () => {
    const { viewState } = get();

    if (viewState.level === 'city') {
      const stateTemples = temples.filter(temple =>
        temple.location.state === viewState.selectedState?.name
      );

      set((state) => ({
        viewState: {
          level: 'state',
          selectedState: state.viewState.selectedState,
          selectedCity: undefined,
          selectedTemples: stateTemples
        }
      }));
    } else if (viewState.level === 'state') {
      set(() => ({
        viewState: {
          level: 'country',
          selectedState: undefined,
          selectedCity: undefined,
          selectedTemples: []
        }
      }));
    }
  },

  resetView: () => {
    set({
      viewState: {
        level: 'country',
        selectedState: undefined,
        selectedCity: undefined,
        selectedTemples: []
      }
    });
  },

  loadTemples: (stateId, cityId) => {
    set({ loading: true, error: null });

    try {
      let filteredTemples = temples;

      if (stateId) {
        const state = states.find(s => s.id === stateId);
        if (state) {
          filteredTemples = temples.filter(temple =>
            temple.location.state === state.name
          );
        }
      }

      if (cityId) {
        const state = states.find(s => s.id === stateId);
        const city = state?.cities.find(c => c.id === cityId);
        if (city) {
          filteredTemples = filteredTemples.filter(temple =>
            temple.location.city === city.name
          );
        }
      }

      set((state) => ({
        viewState: {
          ...state.viewState,
          selectedTemples: filteredTemples
        },
        loading: false
      }));
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to load temples',
        loading: false
      });
    }
  }
}));
