/**
 * Utility functions to convert GeoJSON data to SVG paths
 * for use in the interactive India map
 */

interface GeoJSONFeature {
  type: 'Feature';
  properties: {
    st_nm: string;
    st_code: string;
    district?: string;
    dt_code?: string;
  };
  geometry: {
    type: 'Polygon' | 'MultiPolygon';
    coordinates: number[][][] | number[][][][];
  };
}

interface GeoJSONFeatureCollection {
  type: 'FeatureCollection';
  features: GeoJSONFeature[];
}

interface BoundingBox {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
}

/**
 * Calculate bounding box for a set of coordinates
 */
export function calculateBoundingBox(coordinates: number[][][]): BoundingBox {
  let minX = Infinity;
  let maxX = -Infinity;
  let minY = Infinity;
  let maxY = -Infinity;

  coordinates.forEach(ring => {
    ring.forEach(coord => {
      const [x, y] = coord;
      minX = Math.min(minX, x);
      maxX = Math.max(maxX, x);
      minY = Math.min(minY, y);
      maxY = Math.max(maxY, y);
    });
  });

  return { minX, maxX, minY, maxY };
}

/**
 * Convert longitude/latitude coordinates to SVG coordinates
 */
export function projectToSVG(
  lon: number,
  lat: number,
  bbox: BoundingBox,
  width: number,
  height: number,
  padding: number = 20
): [number, number] {
  const { minX, maxX, minY, maxY } = bbox;
  
  // Calculate scale factors
  const scaleX = (width - 2 * padding) / (maxX - minX);
  const scaleY = (height - 2 * padding) / (maxY - minY);
  
  // Use the smaller scale to maintain aspect ratio
  const scale = Math.min(scaleX, scaleY);
  
  // Calculate center offsets
  const centerX = (width - (maxX - minX) * scale) / 2;
  const centerY = (height - (maxY - minY) * scale) / 2;
  
  // Project coordinates (note: SVG Y axis is flipped)
  const x = (lon - minX) * scale + centerX;
  const y = height - ((lat - minY) * scale + centerY);
  
  return [x, y];
}

/**
 * Convert GeoJSON polygon coordinates to SVG path string
 */
export function polygonToSVGPath(
  coordinates: number[][][],
  bbox: BoundingBox,
  width: number,
  height: number
): string {
  const paths: string[] = [];
  
  coordinates.forEach((ring, index) => {
    const pathCommands: string[] = [];
    
    ring.forEach((coord, coordIndex) => {
      const [lon, lat] = coord;
      const [x, y] = projectToSVG(lon, lat, bbox, width, height);
      
      if (coordIndex === 0) {
        pathCommands.push(`M ${x.toFixed(2)},${y.toFixed(2)}`);
      } else {
        pathCommands.push(`L ${x.toFixed(2)},${y.toFixed(2)}`);
      }
    });
    
    pathCommands.push('Z'); // Close path
    paths.push(pathCommands.join(' '));
  });
  
  return paths.join(' ');
}

/**
 * Convert GeoJSON MultiPolygon to SVG path string
 */
export function multiPolygonToSVGPath(
  coordinates: number[][][][],
  bbox: BoundingBox,
  width: number,
  height: number
): string {
  const paths: string[] = [];
  
  coordinates.forEach(polygon => {
    const polygonPath = polygonToSVGPath(polygon, bbox, width, height);
    paths.push(polygonPath);
  });
  
  return paths.join(' ');
}

/**
 * Convert GeoJSON feature to SVG path string
 */
export function featureToSVGPath(
  feature: GeoJSONFeature,
  bbox: BoundingBox,
  width: number,
  height: number
): string {
  const { geometry } = feature;
  
  if (geometry.type === 'Polygon') {
    return polygonToSVGPath(geometry.coordinates as number[][][], bbox, width, height);
  } else if (geometry.type === 'MultiPolygon') {
    return multiPolygonToSVGPath(geometry.coordinates as number[][][][], bbox, width, height);
  }
  
  return '';
}

/**
 * Calculate center point of a feature
 */
export function calculateFeatureCenter(
  feature: GeoJSONFeature,
  bbox: BoundingBox,
  width: number,
  height: number
): { x: number; y: number } {
  const { geometry } = feature;
  let allCoords: number[][] = [];
  
  if (geometry.type === 'Polygon') {
    allCoords = geometry.coordinates[0] as number[][];
  } else if (geometry.type === 'MultiPolygon') {
    // Use the largest polygon for center calculation
    const polygons = geometry.coordinates as number[][][][];
    let largestPolygon = polygons[0][0];
    let largestArea = 0;
    
    polygons.forEach(polygon => {
      const area = polygon[0].length; // Simple approximation
      if (area > largestArea) {
        largestArea = area;
        largestPolygon = polygon[0];
      }
    });
    
    allCoords = largestPolygon;
  }
  
  // Calculate centroid
  let sumX = 0;
  let sumY = 0;
  
  allCoords.forEach(coord => {
    sumX += coord[0];
    sumY += coord[1];
  });
  
  const centerLon = sumX / allCoords.length;
  const centerLat = sumY / allCoords.length;
  
  const [x, y] = projectToSVG(centerLon, centerLat, bbox, width, height);
  return { x, y };
}

/**
 * Group features by state
 */
export function groupFeaturesByState(features: GeoJSONFeature[]): Map<string, GeoJSONFeature[]> {
  const stateGroups = new Map<string, GeoJSONFeature[]>();
  
  features.forEach(feature => {
    const stateName = feature.properties.st_nm;
    if (!stateGroups.has(stateName)) {
      stateGroups.set(stateName, []);
    }
    stateGroups.get(stateName)!.push(feature);
  });
  
  return stateGroups;
}
