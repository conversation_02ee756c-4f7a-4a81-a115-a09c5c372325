// Geographic data extraction and processing utilities
// Converts SVG path data to usable coordinate information

export interface BoundingBox {
  minX: number;
  minY: number;
  maxX: number;
  maxY: number;
  width: number;
  height: number;
}

export interface GeographicCoordinate {
  x: number;
  y: number;
}

export interface AccurateStateData {
  id: string;              // Application state ID (e.g., 'andhra-pradesh')
  svgId: string;           // SVG element ID (e.g., 'INAP')
  name: string;            // Full state name
  svgPath: string;         // Exact SVG path data
  boundingBox: BoundingBox; // Calculated bounding rectangle
  center: GeographicCoordinate; // Geographic center point
  area: number;            // Calculated area in SVG units
}

export interface StateMappingEntry {
  svgId: string;           // ID from SVG file
  stateId: string;         // Application state ID
  name: string;            // Display name
}

// Mapping between SVG IDs and application state IDs
export const svgStateMapping: StateMappingEntry[] = [
  { svgId: 'INAN', stateId: 'andaman-nicobar', name: 'Andaman and Nicobar Islands' },
  { svgId: 'INAP', stateId: 'andhra-pradesh', name: 'Andhra Pradesh' },
  { svgId: 'INAR', stateId: 'arunachal-pradesh', name: 'Arunachal Pradesh' },
  { svgId: 'INAS', stateId: 'assam', name: 'Assam' },
  { svgId: 'INBR', stateId: 'bihar', name: 'Bihar' },
  { svgId: 'INCH', stateId: 'chandigarh', name: 'Chandigarh' },
  { svgId: 'INCT', stateId: 'chhattisgarh', name: 'Chhattisgarh' },
  { svgId: 'INDH', stateId: 'dadra-nagar-haveli-daman-diu', name: 'Dadra and Nagar Haveli and Daman and Diu' },
  { svgId: 'INDL', stateId: 'delhi', name: 'Delhi' },
  { svgId: 'INGA', stateId: 'goa', name: 'Goa' },
  { svgId: 'INGJ', stateId: 'gujarat', name: 'Gujarat' },
  { svgId: 'INHR', stateId: 'haryana', name: 'Haryana' },
  { svgId: 'INHP', stateId: 'himachal-pradesh', name: 'Himachal Pradesh' },
  { svgId: 'INJK', stateId: 'jammu-kashmir', name: 'Jammu and Kashmir' },
  { svgId: 'INJH', stateId: 'jharkhand', name: 'Jharkhand' },
  { svgId: 'INKA', stateId: 'karnataka', name: 'Karnataka' },
  { svgId: 'INKL', stateId: 'kerala', name: 'Kerala' },
  { svgId: 'INLA', stateId: 'ladakh', name: 'Ladakh' },
  { svgId: 'INLD', stateId: 'lakshadweep', name: 'Lakshadweep' },
  { svgId: 'INMP', stateId: 'madhya-pradesh', name: 'Madhya Pradesh' },
  { svgId: 'INMH', stateId: 'maharashtra', name: 'Maharashtra' },
  { svgId: 'INMN', stateId: 'manipur', name: 'Manipur' },
  { svgId: 'INML', stateId: 'meghalaya', name: 'Meghalaya' },
  { svgId: 'INMZ', stateId: 'mizoram', name: 'Mizoram' },
  { svgId: 'INNL', stateId: 'nagaland', name: 'Nagaland' },
  { svgId: 'INOR', stateId: 'odisha', name: 'Odisha' },
  { svgId: 'INPY', stateId: 'puducherry', name: 'Puducherry' },
  { svgId: 'INPB', stateId: 'punjab', name: 'Punjab' },
  { svgId: 'INRJ', stateId: 'rajasthan', name: 'Rajasthan' },
  { svgId: 'INSK', stateId: 'sikkim', name: 'Sikkim' },
  { svgId: 'INTN', stateId: 'tamil-nadu', name: 'Tamil Nadu' },
  { svgId: 'INTG', stateId: 'telangana', name: 'Telangana' },
  { svgId: 'INTR', stateId: 'tripura', name: 'Tripura' },
  { svgId: 'INUP', stateId: 'uttar-pradesh', name: 'Uttar Pradesh' },
  { svgId: 'INUK', stateId: 'uttarakhand', name: 'Uttarakhand' },
  { svgId: 'INWB', stateId: 'west-bengal', name: 'West Bengal' }
];

/**
 * Parses SVG path data and calculates bounding box
 */
export function calculateBoundingBox(svgPath: string): BoundingBox {
  const coordinates = extractCoordinatesFromPath(svgPath);
  
  if (coordinates.length === 0) {
    return { minX: 0, minY: 0, maxX: 0, maxY: 0, width: 0, height: 0 };
  }

  const xs = coordinates.map(coord => coord.x);
  const ys = coordinates.map(coord => coord.y);
  
  const minX = Math.min(...xs);
  const minY = Math.min(...ys);
  const maxX = Math.max(...xs);
  const maxY = Math.max(...ys);
  
  return {
    minX,
    minY,
    maxX,
    maxY,
    width: maxX - minX,
    height: maxY - minY
  };
}

/**
 * Calculates the geometric center of an SVG path
 */
export function calculateCenter(svgPath: string): GeographicCoordinate {
  const boundingBox = calculateBoundingBox(svgPath);
  
  return {
    x: boundingBox.minX + boundingBox.width / 2,
    y: boundingBox.minY + boundingBox.height / 2
  };
}

/**
 * Estimates the area of an SVG path using the shoelace formula
 */
export function calculateArea(svgPath: string): number {
  const coordinates = extractCoordinatesFromPath(svgPath);
  
  if (coordinates.length < 3) return 0;
  
  let area = 0;
  for (let i = 0; i < coordinates.length; i++) {
    const j = (i + 1) % coordinates.length;
    area += coordinates[i].x * coordinates[j].y;
    area -= coordinates[j].x * coordinates[i].y;
  }
  
  return Math.abs(area) / 2;
}

/**
 * Extracts coordinate pairs from SVG path data
 */
function extractCoordinatesFromPath(svgPath: string): GeographicCoordinate[] {
  const coordinates: GeographicCoordinate[] = [];
  
  // Remove path commands and split by whitespace
  const cleanPath = svgPath.replace(/[MmLlHhVvCcSsQqTtAaZz]/g, ' ');
  const numbers = cleanPath.split(/[\s,]+/).filter(n => n && !isNaN(parseFloat(n)));
  
  // Group numbers into coordinate pairs
  for (let i = 0; i < numbers.length - 1; i += 2) {
    const x = parseFloat(numbers[i]);
    const y = parseFloat(numbers[i + 1]);
    
    if (!isNaN(x) && !isNaN(y)) {
      coordinates.push({ x, y });
    }
  }
  
  return coordinates;
}

/**
 * Converts SVG state ID to application state ID
 */
export function getStateIdFromSvgId(svgId: string): string | null {
  const mapping = svgStateMapping.find(m => m.svgId === svgId);
  return mapping?.stateId || null;
}

/**
 * Gets the full state name from SVG ID
 */
export function getStateNameFromSvgId(svgId: string): string | null {
  const mapping = svgStateMapping.find(m => m.svgId === svgId);
  return mapping?.name || null;
}

/**
 * Validates SVG path data format
 */
export function validateSvgPath(svgPath: string): boolean {
  if (!svgPath || typeof svgPath !== 'string') return false;
  
  // Check for basic SVG path structure
  const hasValidCommands = /[MmLlHhVvCcSsQqTtAaZz]/.test(svgPath);
  const hasCoordinates = /[\d.-]+/.test(svgPath);
  
  return hasValidCommands && hasCoordinates;
}

/**
 * Normalizes SVG path data for consistent formatting
 */
export function normalizeSvgPath(svgPath: string): string {
  return svgPath
    .trim()
    .replace(/\s+/g, ' ')
    .replace(/,/g, ' ')
    .replace(/([MmLlHhVvCcSsQqTtAaZz])\s*/g, '$1 ')
    .replace(/\s+/g, ' ');
}

/**
 * Creates accurate state data from SVG information
 */
export function createAccurateStateData(
  svgId: string,
  svgPath: string
): AccurateStateData | null {
  const stateMapping = svgStateMapping.find(m => m.svgId === svgId);
  
  if (!stateMapping || !validateSvgPath(svgPath)) {
    return null;
  }
  
  const normalizedPath = normalizeSvgPath(svgPath);
  const boundingBox = calculateBoundingBox(normalizedPath);
  const center = calculateCenter(normalizedPath);
  const area = calculateArea(normalizedPath);
  
  return {
    id: stateMapping.stateId,
    svgId: stateMapping.svgId,
    name: stateMapping.name,
    svgPath: normalizedPath,
    boundingBox,
    center,
    area
  };
}

/**
 * Utility to check if a point is within a state's bounding box
 */
export function isPointInBoundingBox(
  point: GeographicCoordinate,
  boundingBox: BoundingBox
): boolean {
  return (
    point.x >= boundingBox.minX &&
    point.x <= boundingBox.maxX &&
    point.y >= boundingBox.minY &&
    point.y <= boundingBox.maxY
  );
}

/**
 * Calculates distance between two points in SVG coordinate space
 */
export function calculateDistance(
  point1: GeographicCoordinate,
  point2: GeographicCoordinate
): number {
  const dx = point2.x - point1.x;
  const dy = point2.y - point1.y;
  return Math.sqrt(dx * dx + dy * dy);
}
