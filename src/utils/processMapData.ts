/**
 * Process the downloaded GeoJSON data and convert it to our map data format
 */

import {
  calculateBoundingBox,
  featureToSVGPath,
  calculateFeatureCenter,
  groupFeaturesByState,
  type GeoJSONFeature,
  type GeoJSONFeatureCollection
} from './geoJsonToSvg';

// State name mapping from GeoJSON to our application format
const STATE_NAME_MAPPING: Record<string, string> = {
  'Andaman and Nicobar Islands': 'andaman-and-nicobar',
  'Andhra Pradesh': 'andhra-pradesh',
  'Arunachal Pradesh': 'arunachal-pradesh',
  'Assam': 'assam',
  'Bihar': 'bihar',
  'Chandigarh': 'chandigarh',
  'Chhattisgarh': 'chhattisgarh',
  'Delhi': 'delhi',
  'Dadra and Nagar Haveli and Daman and Diu': 'dnh-and-dd',
  'Goa': 'goa',
  'Gujarat': 'gujarat',
  'Haryana': 'haryana',
  'Himachal Pradesh': 'himachal-pradesh',
  'Jammu and Kashmir': 'jammu-and-kashmir',
  'Jharkhand': 'jharkhand',
  'Karnataka': 'karnataka',
  'Kerala': 'kerala',
  'Ladakh': 'ladakh',
  'Lakshadweep': 'lakshadweep',
  'Madhya Pradesh': 'madhya-pradesh',
  'Maharashtra': 'maharashtra',
  'Manipur': 'manipur',
  'Meghalaya': 'meghalaya',
  'Mizoram': 'mizoram',
  'Nagaland': 'nagaland',
  'Odisha': 'odisha',
  'Puducherry': 'puducherry',
  'Punjab': 'punjab',
  'Rajasthan': 'rajasthan',
  'Sikkim': 'sikkim',
  'Tamil Nadu': 'tamil-nadu',
  'Telangana': 'telangana',
  'Tripura': 'tripura',
  'Uttar Pradesh': 'uttar-pradesh',
  'Uttarakhand': 'uttarakhand',
  'West Bengal': 'west-bengal'
};

interface StateMapData {
  path: string;
  center: { x: number; y: number };
  districts?: Array<{
    name: string;
    path: string;
    center: { x: number; y: number };
  }>;
}

interface ProcessedMapData {
  viewBox: string;
  states: Record<string, StateMapData>;
  cities: Record<string, Record<string, { path: string; center: { x: number; y: number } }>>;
}

/**
 * Merge multiple features into a single state boundary
 */
function mergeStateFeatures(features: GeoJSONFeature[]): GeoJSONFeature {
  // For simplicity, we'll use the first feature as the base
  // In a more sophisticated implementation, we would actually merge the geometries
  const baseFeature = features[0];
  
  // Calculate combined bounding box for better center calculation
  let allCoords: number[][] = [];
  
  features.forEach(feature => {
    if (feature.geometry.type === 'Polygon') {
      allCoords.push(...feature.geometry.coordinates[0]);
    } else if (feature.geometry.type === 'MultiPolygon') {
      feature.geometry.coordinates.forEach(polygon => {
        allCoords.push(...polygon[0]);
      });
    }
  });
  
  // Create a simplified polygon from the bounding box for state-level view
  if (allCoords.length > 0) {
    const bbox = calculateBoundingBox([allCoords]);
    const { minX, maxX, minY, maxY } = bbox;
    
    // Create a simplified rectangular boundary
    const simplifiedCoords = [
      [minX, maxY],
      [maxX, maxY],
      [maxX, minY],
      [minX, minY],
      [minX, maxY]
    ];
    
    return {
      ...baseFeature,
      geometry: {
        type: 'Polygon',
        coordinates: [simplifiedCoords]
      }
    };
  }
  
  return baseFeature;
}

/**
 * Process GeoJSON data and convert to our map data format
 */
export async function processGeoJSONToMapData(geoJsonData: GeoJSONFeatureCollection): Promise<ProcessedMapData> {
  const { features } = geoJsonData;
  
  // Calculate overall bounding box for India
  let allCoords: number[][] = [];
  features.forEach(feature => {
    if (feature.geometry.type === 'Polygon') {
      allCoords.push(...feature.geometry.coordinates[0]);
    } else if (feature.geometry.type === 'MultiPolygon') {
      feature.geometry.coordinates.forEach(polygon => {
        allCoords.push(...polygon[0]);
      });
    }
  });
  
  const overallBbox = calculateBoundingBox([allCoords]);
  const mapWidth = 800;
  const mapHeight = 600;
  
  // Group features by state
  const stateGroups = groupFeaturesByState(features);
  
  const processedStates: Record<string, StateMapData> = {};
  const processedCities: Record<string, Record<string, { path: string; center: { x: number; y: number } }>> = {};
  
  // Process each state
  stateGroups.forEach((stateFeatures, stateName) => {
    const stateId = STATE_NAME_MAPPING[stateName];
    if (!stateId) {
      console.warn(`No mapping found for state: ${stateName}`);
      return;
    }
    
    // Merge all districts into a single state boundary
    const mergedStateFeature = mergeStateFeatures(stateFeatures);
    
    // Generate SVG path for the state
    const statePath = featureToSVGPath(mergedStateFeature, overallBbox, mapWidth, mapHeight);
    const stateCenter = calculateFeatureCenter(mergedStateFeature, overallBbox, mapWidth, mapHeight);
    
    // Process districts as cities
    const stateCities: Record<string, { path: string; center: { x: number; y: number } }> = {};
    
    stateFeatures.forEach(feature => {
      const districtName = feature.properties.district;
      if (districtName) {
        const districtId = districtName.toLowerCase().replace(/\s+/g, '-');
        const districtPath = featureToSVGPath(feature, overallBbox, mapWidth, mapHeight);
        const districtCenter = calculateFeatureCenter(feature, overallBbox, mapWidth, mapHeight);
        
        stateCities[districtId] = {
          path: districtPath,
          center: districtCenter
        };
      }
    });
    
    processedStates[stateId] = {
      path: statePath,
      center: stateCenter,
      districts: stateFeatures.map(feature => ({
        name: feature.properties.district || 'Unknown',
        path: featureToSVGPath(feature, overallBbox, mapWidth, mapHeight),
        center: calculateFeatureCenter(feature, overallBbox, mapWidth, mapHeight)
      }))
    };
    
    if (Object.keys(stateCities).length > 0) {
      processedCities[stateId] = stateCities;
    }
  });
  
  return {
    viewBox: `0 0 ${mapWidth} ${mapHeight}`,
    states: processedStates,
    cities: processedCities
  };
}

/**
 * Load and process the main India GeoJSON file
 */
export async function loadAndProcessIndiaMapData(): Promise<ProcessedMapData> {
  try {
    // Import the GeoJSON data
    const response = await fetch('/src/assets/maps/india-maps-data-main/geojson/india.geojson');
    const geoJsonData: GeoJSONFeatureCollection = await response.json();
    
    return await processGeoJSONToMapData(geoJsonData);
  } catch (error) {
    console.error('Error loading map data:', error);
    throw error;
  }
}
